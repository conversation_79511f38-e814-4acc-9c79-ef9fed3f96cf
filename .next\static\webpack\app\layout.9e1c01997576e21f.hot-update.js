"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts":
/*!******************************************************!*\
  !*** ./src/services/dataAccess/DataAccessManager.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CACHE_CONFIG: function() { return /* binding */ DEFAULT_CACHE_CONFIG; },\n/* harmony export */   DataAccessManager: function() { return /* binding */ DataAccessManager; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   costCalculations: function() { return /* binding */ costCalculations; },\n/* harmony export */   customers: function() { return /* binding */ customers; },\n/* harmony export */   dataAccessManager: function() { return /* binding */ dataAccessManager; },\n/* harmony export */   employees: function() { return /* binding */ employees; },\n/* harmony export */   inventory: function() { return /* binding */ inventory; },\n/* harmony export */   productionOrders: function() { return /* binding */ productionOrders; },\n/* harmony export */   productionWorkOrders: function() { return /* binding */ productionWorkOrders; },\n/* harmony export */   products: function() { return /* binding */ products; },\n/* harmony export */   tokenManagement: function() { return /* binding */ tokenManagement; },\n/* harmony export */   workstations: function() { return /* binding */ workstations; }\n/* harmony export */ });\n/* harmony import */ var _ProductDataAccessService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProductDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductDataAccessService.ts\");\n/* harmony import */ var _CustomerDataAccessService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CustomerDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/CustomerDataAccessService.ts\");\n/* harmony import */ var _EmployeeDataAccessService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmployeeDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/EmployeeDataAccessService.ts\");\n/* harmony import */ var _InventoryDataAccessService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InventoryDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/InventoryDataAccessService.ts\");\n/* harmony import */ var _OrderDataAccessService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/OrderDataAccessService.ts\");\n/* harmony import */ var _WorkTimeDataAccessService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./WorkTimeDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/WorkTimeDataAccessService.ts\");\n/* harmony import */ var _ProductionOrderDataAccessService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ProductionOrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductionOrderDataAccessService.ts\");\n/* harmony import */ var _ProductionWorkOrderDataAccessService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProductionWorkOrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductionWorkOrderDataAccessService.ts\");\n/* harmony import */ var _WorkstationDataAccessService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./WorkstationDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/WorkstationDataAccessService.ts\");\n/* harmony import */ var _CostCalculationDataAccessService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CostCalculationDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/CostCalculationDataAccessService.ts\");\n/* harmony import */ var _AuthDataAccessService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./AuthDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/AuthDataAccessService.ts\");\n/* harmony import */ var _RoleDataAccessService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./RoleDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/RoleDataAccessService.ts\");\n/* harmony import */ var _TokenManagementService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TokenManagementService */ \"(app-pages-browser)/./src/services/dataAccess/TokenManagementService.ts\");\n/* harmony import */ var _DataChangeNotifier__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./DataChangeNotifier */ \"(app-pages-browser)/./src/services/dataAccess/DataChangeNotifier.ts\");\n/* harmony import */ var _PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./PerformanceOptimizer */ \"(app-pages-browser)/./src/services/dataAccess/PerformanceOptimizer.ts\");\n/* harmony import */ var _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./DataAccessPerformanceMonitor */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessPerformanceMonitor.ts\");\n/* harmony import */ var _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/concurrencyControl */ \"(app-pages-browser)/./src/utils/concurrencyControl.ts\");\n/* harmony import */ var _DataAccessLayer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./DataAccessLayer */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessLayer.ts\");\n/* harmony import */ var _utils_business__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/business */ \"(app-pages-browser)/./src/utils/business/index.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/**\r\n * 统一数据访问管理器\r\n * 提供单一入口点访问所有数据服务，实现标准化的跨模块数据调用\r\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// ✅ 架构合规：已移除独立CacheStrategyManager，使用内置缓存系统\n\n\n// ✅ 架构合规：移除违规的独立缓存管理器，使用内置缓存\n\n// 优先级同步服务已删除\n\n\n/**\r\n * 默认缓存配置 - 优化版\r\n */ const DEFAULT_CACHE_CONFIG = {\n    enableCaching: true,\n    defaultTTL: 5 * 60 * 1000,\n    maxSize: 1000,\n    strategies: {\n        orders: {\n            ttl: 2 * 60 * 1000,\n            enabled: true,\n            priority: \"high\",\n            maxEntries: 200\n        },\n        products: {\n            ttl: 10 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 300\n        },\n        workstations: {\n            ttl: 30 * 1000,\n            enabled: true,\n            priority: \"critical\",\n            maxEntries: 50\n        },\n        statistics: {\n            ttl: 30 * 1000,\n            enabled: true,\n            priority: \"low\",\n            maxEntries: 100\n        },\n        customers: {\n            ttl: 15 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 150\n        },\n        employees: {\n            ttl: 30 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 100\n        } // 30分钟\n    },\n    monitoring: {\n        enabled: true,\n        reportInterval: 60000 // 1分钟\n    },\n    // ✅ 优化：智能缓存管理配置\n    smartEviction: {\n        enabled: true,\n        algorithm: \"adaptive\",\n        thresholds: {\n            memoryWarning: 0.7,\n            memoryCritical: 0.85 // 85%内存使用率紧急清理\n        }\n    },\n    // ✅ 优化：缓存预热配置\n    prewarming: {\n        enabled: true,\n        strategies: [\n            \"workstations\",\n            \"orders\",\n            \"products\"\n        ],\n        scheduleInterval: 30000 // 30秒预热间隔\n    }\n};\n/**\r\n * 默认配置 - 简化版\r\n */ const DEFAULT_CONFIG = {\n    enableLogging: true,\n    enableCaching: true,\n    defaultTTL: 5 * 60 * 1000,\n    maxCacheSize: 1000,\n    retryAttempts: 3,\n    retryDelay: 1000 // 1秒\n};\n/**\r\n * 统一数据访问管理器\r\n */ class DataAccessManager {\n    /**\r\n   * 获取单例实例\r\n   */ static getInstance(config) {\n        if (!DataAccessManager.instance) {\n            DataAccessManager.instance = new DataAccessManager(config);\n        }\n        return DataAccessManager.instance;\n    }\n    // 优先级同步服务已删除\n    /**\r\n   * 记录访问日志\r\n   */ logAccess(service, method, params, duration, success, error, fromCache) {\n        if (!this.config.enableLogging) return;\n        const log = {\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_18__.timestampGenerator.now(),\n            service,\n            method,\n            params,\n            duration,\n            success,\n            error\n        };\n        this.accessLogs.push(log);\n        // 保持最近1000条日志\n        if (this.accessLogs.length > 1000) {\n            this.accessLogs = this.accessLogs.slice(-1000);\n        }\n        // 🔧 优化：使用批量报告代替单个日志\n        if (true) {\n            this.addToPerformanceBatch(service, method, duration, success, fromCache || false);\n        }\n    }\n    // 🔧 优化：批量性能报告方法\n    /**\r\n   * 添加到性能批次\r\n   */ addToPerformanceBatch(service, method, duration, success, fromCache) {\n        this.performanceBatch.push({\n            service,\n            method,\n            duration,\n            success,\n            fromCache,\n            timestamp: Date.now()\n        });\n        // 启动批量报告定时器\n        if (!this.batchReportTimer) {\n            this.batchReportTimer = setTimeout(()=>{\n                this.flushPerformanceBatch();\n            }, this.BATCH_REPORT_INTERVAL);\n        }\n    }\n    /**\r\n   * 输出批量性能报告\r\n   */ flushPerformanceBatch() {\n        if (this.performanceBatch.length === 0) {\n            this.batchReportTimer = null;\n            return;\n        }\n        // 统计数据\n        const stats = {\n            totalOperations: this.performanceBatch.length,\n            successCount: this.performanceBatch.filter((op)=>op.success).length,\n            cacheHits: this.performanceBatch.filter((op)=>op.fromCache).length,\n            averageTime: Math.round(this.performanceBatch.reduce((sum, op)=>sum + op.duration, 0) / this.performanceBatch.length),\n            services: new Set(this.performanceBatch.map((op)=>op.service)).size,\n            timeRange: {\n                start: new Date(Math.min(...this.performanceBatch.map((op)=>op.timestamp))).toLocaleTimeString(),\n                end: new Date(Math.max(...this.performanceBatch.map((op)=>op.timestamp))).toLocaleTimeString()\n            }\n        };\n        console.log(\"\\uD83D\\uDCCA [DataAccessManager] 批量性能报告:\", stats);\n        // 清空批次\n        this.performanceBatch = [];\n        this.batchReportTimer = null;\n    }\n    // 性能优化支持方法\n    /**\r\n   * 判断是否应该使用缓存\r\n   */ shouldUseCache(method) {\n        // 🔧 临时修复：禁用getById的缓存，避免ID混淆问题\n        if (method === \"getById\") {\n            return false;\n        }\n        // 读操作使用缓存，写操作不使用\n        const readMethods = [\n            \"get\",\n            \"find\",\n            \"search\",\n            \"list\",\n            \"statistics\",\n            \"utilization\"\n        ];\n        return readMethods.some((readMethod)=>method.toLowerCase().includes(readMethod));\n    }\n    /**\r\n   * 判断是否应该缓存结果\r\n   */ shouldCacheResult(method, result) {\n        // 成功的读操作结果才缓存\n        return this.shouldUseCache(method) && (result === null || result === void 0 ? void 0 : result.status) === \"success\";\n    }\n    /**\r\n   * 生成缓存键\r\n   * 🔧 修复：统一缓存键格式，确保与清理模式匹配\r\n   */ generateCacheKey(service, method, params) {\n        // 🔧 修复：使用冒号分隔符，与WorkstationUpdateService的清理模式保持一致\n        const baseKey = \"\".concat(service, \":\").concat(method);\n        // 🔧 修复：标准化参数处理，确保键的一致性\n        if (!params || typeof params === \"object\" && Object.keys(params).length === 0) {\n            return baseKey;\n        }\n        // 对参数进行标准化处理\n        let paramStr;\n        if (typeof params === \"object\") {\n            // 对对象参数进行排序，确保键的一致性\n            const sortedParams = Object.keys(params).sort().reduce((result, key)=>{\n                result[key] = params[key];\n                return result;\n            }, {});\n            paramStr = JSON.stringify(sortedParams);\n        } else {\n            paramStr = String(params);\n        }\n        return \"\".concat(baseKey, \":\").concat(Buffer.from(paramStr).toString(\"base64\").slice(0, 32));\n    }\n    /**\r\n   * 从服务名获取数据类型\r\n   */ getDataTypeFromService(service) {\n        if (service.includes(\"ProductionOrder\")) return \"orders\";\n        if (service.includes(\"ProductionWorkOrder\")) return \"workOrders\";\n        if (service.includes(\"OrderService\") || service.includes(\"SalesOrder\")) return \"orders\" // 🔧 新增：支持销售订单\n        ;\n        if (service.includes(\"Workstation\")) return \"workstations\";\n        return \"statistics\";\n    }\n    /**\r\n   * 获取访问频率（简化实现）\r\n   */ getAccessFrequency(operationKey) {\n        // 从访问日志中统计频率\n        const recentLogs = this.accessLogs.filter((log)=>Date.now() - new Date(log.timestamp).getTime() < 5 * 60 * 1000 // 最近5分钟\n        );\n        return recentLogs.filter((log)=>\"\".concat(log.service, \".\").concat(log.method) === operationKey).length;\n    }\n    /**\r\n   * 判断是否应该预加载\r\n   */ shouldPreload(method, result) {\n        var _result_data_items, _result_data;\n        // 获取列表数据时触发预加载\n        return method.includes(\"getAll\") && (result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : (_result_data_items = _result_data.items) === null || _result_data_items === void 0 ? void 0 : _result_data_items.length) > 0;\n    }\n    // ==================== 内置缓存系统核心方法 ====================\n    /**\r\n   * 从缓存获取数据\r\n   */ getFromCache(key) {\n        const entry = this.cache.get(key);\n        if (!entry) return null;\n        // 检查是否过期\n        if (Date.now() > entry.expiresAt) {\n            this.cache.delete(key);\n            return null;\n        }\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return entry.data;\n    }\n    /**\r\n   * 设置缓存数据 - 优化版\r\n   */ setToCache(key, data, service, method) {\n        // ✅ 优化：智能缓存容量管理\n        if (this.cache.size >= this.cacheConfig.maxSize) {\n            this.smartEvictEntries();\n        }\n        const strategy = this.cacheConfig.strategies[service] || {\n            ttl: this.cacheConfig.defaultTTL,\n            priority: \"medium\"\n        };\n        // ✅ 优化：智能数据分析\n        const dataType = this.getDataType(service, method);\n        const priority = strategy.priority || \"medium\";\n        const estimatedSize = this.estimateDataSize(data);\n        const accessPattern = this.predictAccessPattern(key, service);\n        const entry = {\n            data,\n            expiresAt: Date.now() + strategy.ttl,\n            accessCount: 1,\n            lastAccessed: Date.now(),\n            createdAt: Date.now(),\n            // ✅ 优化：智能缓存属性\n            priority,\n            dataType,\n            estimatedSize,\n            accessPattern,\n            refreshable: this.isRefreshableData(service, method)\n        };\n        this.cache.set(key, entry);\n        // ✅ 优化：记录访问历史\n        this.recordCacheAccess(key);\n    }\n    /**\r\n   * ✅ 优化：智能缓存清理策略\r\n   */ smartEvictEntries() {\n        const entries = Array.from(this.cache.entries());\n        const now = Date.now();\n        // 根据配置的算法选择清理策略\n        switch(this.cacheConfig.smartEviction.algorithm){\n            case \"lru\":\n                this.evictByLRU(entries);\n                break;\n            case \"lfu\":\n                this.evictByLFU(entries);\n                break;\n            case \"adaptive\":\n            default:\n                this.evictAdaptive(entries, now);\n                break;\n        }\n        this.cacheStats.evictions++;\n    }\n    /**\r\n   * ✅ 优化：自适应缓存清理\r\n   */ evictAdaptive(entries, now) {\n        // 综合考虑优先级、访问频率、大小等因素\n        const scored = entries.map((param)=>{\n            let [key, entry] = param;\n            let score = 0;\n            // 优先级评分 (优先级越高分数越高，越不容易被清理)\n            const priorityScores = {\n                critical: 100,\n                high: 70,\n                medium: 40,\n                low: 10\n            };\n            score += priorityScores[entry.priority] || 40;\n            // 访问频率评分\n            const age = (now - entry.createdAt) / (24 * 60 * 60 * 1000 // 天数\n            );\n            const frequency = entry.accessCount / Math.max(age, 0.1);\n            score += Math.min(frequency * 10, 50);\n            // 最近访问评分\n            const lastAccessAge = (now - entry.lastAccessed) / (60 * 60 * 1000 // 小时\n            );\n            score += Math.max(50 - lastAccessAge * 2, 0);\n            // 大小惩罚（大数据降低分数）\n            score -= Math.min(entry.estimatedSize / 1000, 20);\n            return {\n                key,\n                entry,\n                score\n            };\n        });\n        // 按分数排序，删除分数最低的条目\n        scored.sort((a, b)=>a.score - b.score);\n        const deleteCount = Math.max(Math.floor(entries.length * 0.15), 1);\n        for(let i = 0; i < deleteCount && i < scored.length; i++){\n            this.cache.delete(scored[i].key);\n            this.cacheAccessHistory.delete(scored[i].key);\n        }\n    }\n    /**\r\n   * ✅ 优化：LRU清理策略\r\n   */ evictByLRU(entries) {\n        entries.sort((a, b)=>a[1].lastAccessed - b[1].lastAccessed);\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n            this.cacheAccessHistory.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * ✅ 优化：LFU清理策略\r\n   */ evictByLFU(entries) {\n        entries.sort((a, b)=>a[1].accessCount - b[1].accessCount);\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n            this.cacheAccessHistory.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * 清理过期缓存 - 保留原有方法作为备用\r\n   */ evictOldestEntries() {\n        const entries = Array.from(this.cache.entries());\n        entries.sort((a, b)=>a[1].lastAccessed - b[1].lastAccessed);\n        // 删除最旧的10%条目\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * 统一的缓存执行方法\r\n   */ async executeWithCache(service, method, params, executor) {\n        // ✅ 架构合规：使用内置缓存系统决策\n        if (!this.config.enableCaching) {\n            return executor();\n        }\n        // 简化的缓存决策：读操作才缓存\n        const readMethods = [\n            \"get\",\n            \"find\",\n            \"search\",\n            \"list\",\n            \"statistics\"\n        ];\n        const shouldCache = readMethods.some((readMethod)=>method.toLowerCase().includes(readMethod));\n        if (!shouldCache) {\n            return executor();\n        }\n        const cacheKey = this.generateCacheKey(service, method, params);\n        // 请求去重\n        if (this.requestCache.has(cacheKey)) {\n            return this.requestCache.get(cacheKey);\n        }\n        // ✅ 架构合规：使用DataAccessManager内置缓存系统\n        const cachedEntry = this.cache.get(cacheKey);\n        if (cachedEntry && Date.now() < cachedEntry.expiresAt) {\n            this.cacheStats.hits++;\n            // ✅ 优化：更新访问信息\n            cachedEntry.accessCount++;\n            cachedEntry.lastAccessed = Date.now();\n            this.recordCacheAccess(cacheKey);\n            return cachedEntry.data;\n        }\n        // 执行请求\n        const requestPromise = executor();\n        this.requestCache.set(cacheKey, requestPromise);\n        try {\n            const result = await requestPromise;\n            this.cacheStats.misses++;\n            // ✅ 架构合规：使用DataAccessManager内置缓存系统设置缓存\n            if (this.shouldCacheResult(method, result)) {\n                this.setToCache(cacheKey, result, service, method);\n            }\n            return result;\n        } finally{\n            this.requestCache.delete(cacheKey);\n        }\n    }\n    // ==================== ✅ 优化：智能缓存辅助方法 ====================\n    /**\r\n   * ✅ 优化：初始化高级缓存功能\r\n   */ initializeAdvancedCaching() {\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 启动智能缓存管理\");\n        // 启动缓存预热\n        if (this.cacheConfig.prewarming.enabled) {\n            this.startCachePrewarming();\n        }\n        // 启动定期内存检查\n        this.startMemoryMonitoring();\n        console.log(\"✅ [DataAccessManager] 智能缓存管理已启动\");\n    }\n    /**\r\n   * ✅ 优化：启动缓存预热\r\n   */ startCachePrewarming() {\n        if (this.prewarmTimer) {\n            clearInterval(this.prewarmTimer);\n        }\n        this.prewarmTimer = setInterval(()=>{\n            this.performCachePrewarming();\n        }, this.cacheConfig.prewarming.scheduleInterval);\n        // 立即执行一次预热\n        setTimeout(()=>this.performCachePrewarming(), 5000) // 5秒后开始\n        ;\n    }\n    /**\r\n   * ✅ 优化：执行缓存预热\r\n   */ async performCachePrewarming() {\n        if (this.memoryPressure) {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 内存压力过高，跳过预热\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDD25 [DataAccessManager] 开始缓存预热\");\n        try {\n            for (const strategy of this.cacheConfig.prewarming.strategies){\n                await this.prewarmStrategy(strategy);\n            }\n        } catch (error) {\n            console.error(\"❌ [DataAccessManager] 缓存预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热特定策略\r\n   */ async prewarmStrategy(strategy) {\n        switch(strategy){\n            case \"workstations\":\n                await this.prewarmWorkstations();\n                break;\n            case \"orders\":\n                await this.prewarmOrders();\n                break;\n            case \"products\":\n                await this.prewarmProducts();\n                break;\n        }\n    }\n    /**\r\n   * ✅ 优化：预热工位数据\r\n   */ async prewarmWorkstations() {\n        try {\n            await this.workstations.getActiveWorkstations();\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 工位数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 工位数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热订单数据\r\n   */ async prewarmOrders() {\n        try {\n            await this.orders.getAll({\n                limit: 50\n            }) // 最近50个订单\n            ;\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 订单数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 订单数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热产品数据\r\n   */ async prewarmProducts() {\n        try {\n            await this.products.getActive();\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 产品数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 产品数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：启动内存监控\r\n   */ startMemoryMonitoring() {\n        setInterval(()=>{\n            this.checkMemoryPressure();\n        }, 30000) // 每30秒检查一次\n        ;\n    }\n    /**\r\n   * ✅ 优化：检查内存压力\r\n   */ checkMemoryPressure() {\n        const now = Date.now();\n        if (now - this.lastMemoryCheck < 10000) return; // 10秒内不重复检查\n        this.lastMemoryCheck = now;\n        try {\n            // 简单的内存压力检测（基于缓存大小）\n            const cacheMemoryRatio = this.cache.size / this.cacheConfig.maxSize;\n            if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryCritical) {\n                this.memoryPressure = true;\n                this.handleCriticalMemoryPressure();\n            } else if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryWarning) {\n                this.memoryPressure = false;\n                this.handleMemoryWarning();\n            } else {\n                this.memoryPressure = false;\n            }\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 内存压力检查失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：处理紧急内存压力\r\n   */ handleCriticalMemoryPressure() {\n        console.warn(\"\\uD83D\\uDEA8 [DataAccessManager] 检测到紧急内存压力，执行强制清理\");\n        // 强制清理缓存\n        this.smartEvictEntries();\n        // 暂停预热\n        if (this.prewarmTimer) {\n            clearInterval(this.prewarmTimer);\n            this.prewarmTimer = null;\n        }\n    }\n    /**\r\n   * ✅ 优化：处理内存警告\r\n   */ handleMemoryWarning() {\n        console.warn(\"⚠️ [DataAccessManager] 检测到内存压力警告，执行适度清理\");\n        // 清理低优先级缓存\n        this.cleanupLowPriorityCache();\n    }\n    /**\r\n   * ✅ 优化：清理低优先级缓存\r\n   */ cleanupLowPriorityCache() {\n        const entries = Array.from(this.cache.entries());\n        let deletedCount = 0;\n        for (const [key, entry] of entries){\n            if (entry.priority === \"low\" && Date.now() > entry.expiresAt - 60000) {\n                this.cache.delete(key);\n                this.cacheAccessHistory.delete(key);\n                deletedCount++;\n            }\n        }\n        console.log(\"\\uD83E\\uDDF9 [DataAccessManager] 清理了\".concat(deletedCount, \"个低优先级缓存条目\"));\n    }\n    /**\r\n   * ✅ 优化：获取数据类型\r\n   */ getDataType(service, method) {\n        if (!method) return service;\n        if (method.includes(\"statistics\") || method.includes(\"utilization\")) return \"statistics\";\n        if (method.includes(\"list\") || method.includes(\"getAll\")) return \"list\";\n        if (method.includes(\"getById\") || method.includes(\"getBy\")) return \"detail\";\n        return service;\n    }\n    /**\r\n   * ✅ 优化：预测访问模式\r\n   */ predictAccessPattern(key, service) {\n        const history = this.cacheAccessHistory.get(key) || [];\n        if (history.length < 2) {\n            // 根据服务类型预测\n            if (service === \"workstations\" || service === \"statistics\") return \"frequent\";\n            if (service === \"orders\" || service === \"products\") return \"occasional\";\n            return \"rare\";\n        }\n        // 基于历史访问频率\n        const recentAccesses = history.filter((time)=>Date.now() - time < 60 * 60 * 1000).length // 1小时内\n        ;\n        if (recentAccesses > 10) return \"frequent\";\n        if (recentAccesses > 3) return \"occasional\";\n        return \"rare\";\n    }\n    /**\r\n   * ✅ 优化：估算数据大小\r\n   */ estimateDataSize(data) {\n        try {\n            const jsonString = JSON.stringify(data);\n            return jsonString.length * 2 // 大致估算，考虑Unicode字符\n            ;\n        } catch (e) {\n            return 1000 // 默认估算\n            ;\n        }\n    }\n    /**\r\n   * ✅ 优化：判断数据是否可刷新\r\n   */ isRefreshableData(service, method) {\n        // 统计数据、状态数据等是可刷新的\n        if ((method === null || method === void 0 ? void 0 : method.includes(\"statistics\")) || (method === null || method === void 0 ? void 0 : method.includes(\"status\")) || (method === null || method === void 0 ? void 0 : method.includes(\"utilization\"))) {\n            return true;\n        }\n        // 工位数据是频繁变化的\n        if (service === \"workstations\") return true;\n        return false;\n    }\n    /**\r\n   * ✅ 优化：记录缓存访问\r\n   */ recordCacheAccess(key) {\n        const history = this.cacheAccessHistory.get(key) || [];\n        history.push(Date.now());\n        // 只保留最近100次访问记录\n        if (history.length > 100) {\n            history.splice(0, history.length - 100);\n        }\n        this.cacheAccessHistory.set(key, history);\n    }\n    /**\r\n   * ✅ 架构合规：获取缓存TTL\r\n   */ getCacheTTL(service, method) {\n        // 根据服务类型和方法返回不同的TTL\n        const serviceConfig = {\n            orders: 2 * 60 * 1000,\n            products: 10 * 60 * 1000,\n            workstations: 30 * 1000,\n            statistics: 30 * 1000,\n            customers: 15 * 60 * 1000,\n            employees: 30 * 60 * 1000 // 员工数据: 30分钟\n        };\n        return serviceConfig[service] || this.config.defaultTTL || 5 * 60 * 1000 // 默认5分钟\n        ;\n    }\n    /**\r\n   * 初始化性能监控\r\n   */ initializePerformanceMonitoring() {\n        this.performanceMetrics = {\n            totalCalls: 0,\n            successCalls: 0,\n            errorCalls: 0,\n            averageResponseTime: 0,\n            minResponseTime: Infinity,\n            maxResponseTime: 0,\n            cacheHitRate: 0,\n            slowQueries: [],\n            methodStats: new Map(),\n            hourlyStats: []\n        };\n        // 启动定期清理任务\n        this.startPerformanceCleanupTask();\n    }\n    /**\r\n   * 启动性能数据清理任务\r\n   */ startPerformanceCleanupTask() {\n        // 每小时清理一次过期的性能数据\n        setInterval(()=>{\n            this.cleanupPerformanceData();\n        }, 60 * 60 * 1000) // 1小时\n        ;\n    }\n    /**\r\n   * 清理过期的性能数据\r\n   */ cleanupPerformanceData() {\n        const now = Date.now();\n        const oneHourAgo = now - 60 * 60 * 1000;\n        const oneDayAgo = now - 24 * 60 * 60 * 1000;\n        // 清理最近调用记录（保留1小时）\n        this.recentCalls = this.recentCalls.filter((call)=>call.timestamp > oneHourAgo);\n        // 清理慢查询记录（保留1天，最多100条）\n        this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.filter((query)=>query.timestamp > oneDayAgo).slice(-100);\n        // 更新小时统计\n        this.updateHourlyStats();\n    }\n    /**\r\n   * 更新小时统计\r\n   */ updateHourlyStats() {\n        const now = new Date();\n        const currentHour = \"\".concat(now.getFullYear(), \"-\").concat(String(now.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(now.getDate()).padStart(2, \"0\"), \" \").concat(String(now.getHours()).padStart(2, \"0\"), \":00\");\n        const oneHourAgo = Date.now() - 60 * 60 * 1000;\n        const hourCalls = this.recentCalls.filter((call)=>call.timestamp > oneHourAgo);\n        if (hourCalls.length > 0) {\n            const totalTime = hourCalls.reduce((sum, call)=>sum + call.duration, 0);\n            const errorCount = hourCalls.filter((call)=>call.status === \"error\").length;\n            const hourStat = {\n                hour: currentHour,\n                calls: hourCalls.length,\n                averageTime: totalTime / hourCalls.length,\n                errorRate: errorCount / hourCalls.length\n            };\n            // 更新或添加当前小时的统计\n            const existingIndex = this.performanceMetrics.hourlyStats.findIndex((stat)=>stat.hour === currentHour);\n            if (existingIndex >= 0) {\n                this.performanceMetrics.hourlyStats[existingIndex] = hourStat;\n            } else {\n                this.performanceMetrics.hourlyStats.push(hourStat);\n            }\n            // 保留最近24小时的统计\n            this.performanceMetrics.hourlyStats = this.performanceMetrics.hourlyStats.slice(-24);\n        }\n    }\n    /**\r\n   * 更新性能指标\r\n   */ updatePerformanceMetrics(operationKey, duration, success) {\n        let cached = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        // 更新总体指标\n        this.performanceMetrics.totalCalls++;\n        if (success) {\n            this.performanceMetrics.successCalls++;\n        } else {\n            this.performanceMetrics.errorCalls++;\n        }\n        // 更新响应时间统计\n        this.performanceMetrics.minResponseTime = Math.min(this.performanceMetrics.minResponseTime, duration);\n        this.performanceMetrics.maxResponseTime = Math.max(this.performanceMetrics.maxResponseTime, duration);\n        // 更新平均响应时间\n        const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalCalls - 1) + duration;\n        this.performanceMetrics.averageResponseTime = totalTime / this.performanceMetrics.totalCalls;\n        // 更新缓存命中率\n        if (cached) {\n            const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate + 1;\n            this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls;\n        } else {\n            const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate;\n            this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls;\n        }\n        // 更新方法统计\n        const methodStat = this.methodStats.get(operationKey) || {\n            calls: 0,\n            totalTime: 0,\n            averageTime: 0,\n            successRate: 0,\n            lastCall: 0,\n            errors: 0\n        };\n        methodStat.calls++;\n        methodStat.totalTime += duration;\n        methodStat.averageTime = methodStat.totalTime / methodStat.calls;\n        methodStat.lastCall = Date.now();\n        if (success) {\n            methodStat.successRate = (methodStat.successRate * (methodStat.calls - 1) + 1) / methodStat.calls;\n        } else {\n            methodStat.errors++;\n            methodStat.successRate = methodStat.successRate * (methodStat.calls - 1) / methodStat.calls;\n        }\n        this.methodStats.set(operationKey, methodStat);\n        // 记录慢查询\n        if (duration > 1000) {\n            this.performanceMetrics.slowQueries.push({\n                method: operationKey,\n                params: {},\n                duration,\n                timestamp: Date.now(),\n                cached\n            });\n            // 保持慢查询记录数量限制\n            if (this.performanceMetrics.slowQueries.length > 100) {\n                this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.slice(-50);\n            }\n        }\n        // 记录最近调用\n        this.recentCalls.push({\n            method: operationKey,\n            duration,\n            status: success ? \"success\" : \"error\",\n            timestamp: Date.now(),\n            cached\n        });\n        // 保持最近调用记录数量限制\n        if (this.recentCalls.length > 1000) {\n            this.recentCalls = this.recentCalls.slice(-500);\n        }\n    }\n    // 注意：executeWithCaching方法已移动到内置缓存系统部分\n    /**\r\n   * 执行带日志记录和性能监控的方法调用 - 统一改造版\r\n   */ async executeWithLogging(service, method, params, executor) {\n        const startTime = Date.now();\n        const operationKey = \"\".concat(service, \".\").concat(method);\n        try {\n            // 使用内置缓存系统\n            const result = await this.executeWithCache(service, method, params, executor);\n            const duration = Date.now() - startTime;\n            // 更新性能指标（成功）\n            this.updatePerformanceMetrics(operationKey, duration, true, false);\n            this.logAccess(service, method, params, duration, true);\n            _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(operationKey, duration, true, params);\n            return result;\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // 更新性能指标（错误）\n            this.updatePerformanceMetrics(operationKey, duration, false, false);\n            this.logAccess(service, method, params, duration, false, errorMessage);\n            _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(operationKey, duration, false, params);\n            throw error;\n        }\n    }\n    // executeWithUnifiedCache方法已移除，使用内置缓存系统\n    // invalidateUnifiedCache方法已移除，使用内置缓存系统\n    // executeWithWorkstationCaching方法已移除，使用内置缓存系统\n    // 统一缓存相关方法已移除，使用内置缓存系统\n    // ==================== 产品数据访问 ====================\n    /**\r\n   * 产品数据访问器\r\n   */ get products() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductService\", \"getProducts\", params, ()=>this.productService.getProducts(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductService\", \"getProductById\", {\n                    id\n                }, ()=>this.productService.getProductById(id)),\n            getByCode: (code)=>this.executeWithLogging(\"ProductService\", \"getProductByCode\", {\n                    code\n                }, ()=>this.productService.getProductByCode(code)),\n            create: (data)=>this.executeWithLogging(\"ProductService\", \"createProduct\", data, ()=>this.productService.createProduct(data)),\n            update: (id, updates)=>this.executeWithLogging(\"ProductService\", \"updateProduct\", {\n                    id,\n                    updates\n                }, ()=>this.productService.updateProduct(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"ProductService\", \"deleteProduct\", {\n                    id\n                }, ()=>this.productService.deleteProduct(id)),\n            getActive: ()=>this.executeWithLogging(\"ProductService\", \"getActiveProducts\", {}, ()=>this.productService.getActiveProducts()),\n            getByCategory: (category)=>this.executeWithLogging(\"ProductService\", \"getProductsByCategory\", {\n                    category\n                }, ()=>this.productService.getProductsByCategory(category)),\n            search: (keyword)=>this.executeWithLogging(\"ProductService\", \"searchProducts\", {\n                    keyword\n                }, ()=>this.productService.searchProducts(keyword)),\n            getByMold: (moldId)=>this.executeWithLogging(\"ProductService\", \"getProductsByMold\", {\n                    moldId\n                }, ()=>this.productService.getProductsByMold(moldId)),\n            getMoldUsage: ()=>this.executeWithLogging(\"ProductService\", \"getMoldUsageStatistics\", {}, ()=>this.productService.getMoldUsageStatistics())\n        };\n    }\n    // ==================== 客户数据访问 ====================\n    /**\r\n   * 客户数据访问器\r\n   */ get customers() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"CustomerService\", \"getCustomers\", params, ()=>this.customerService.getCustomers(params)),\n            getById: (id)=>this.executeWithLogging(\"CustomerService\", \"getCustomerById\", {\n                    id\n                }, ()=>this.customerService.getCustomerById(id)),\n            create: (data)=>this.executeWithLogging(\"CustomerService\", \"createCustomer\", data, ()=>this.customerService.createCustomer(data)),\n            update: (id, updates)=>this.executeWithLogging(\"CustomerService\", \"updateCustomer\", {\n                    id,\n                    updates\n                }, ()=>this.customerService.updateCustomer(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"CustomerService\", \"deleteCustomer\", {\n                    id\n                }, ()=>this.customerService.deleteCustomer(id)),\n            getActive: ()=>this.executeWithLogging(\"CustomerService\", \"getActiveCustomers\", {}, ()=>this.customerService.getActiveCustomers()),\n            search: (keyword)=>this.executeWithLogging(\"CustomerService\", \"searchCustomers\", {\n                    keyword\n                }, ()=>this.customerService.searchCustomers(keyword))\n        };\n    }\n    // ==================== 员工数据访问 ====================\n    /**\r\n   * 员工数据访问器\r\n   */ get employees() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"EmployeeService\", \"getEmployees\", params, ()=>this.employeeService.getEmployees(params)),\n            getById: (id)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeeById\", {\n                    id\n                }, ()=>this.employeeService.getEmployeeById(id)),\n            create: (data)=>this.executeWithLogging(\"EmployeeService\", \"createEmployee\", data, ()=>this.employeeService.createEmployee(data)),\n            update: (id, updates)=>this.executeWithLogging(\"EmployeeService\", \"updateEmployee\", {\n                    id,\n                    updates\n                }, ()=>this.employeeService.updateEmployee(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"EmployeeService\", \"deleteEmployee\", {\n                    id\n                }, ()=>this.employeeService.deleteEmployee(id)),\n            getActive: ()=>this.executeWithLogging(\"EmployeeService\", \"getActiveEmployees\", {}, ()=>this.employeeService.getActiveEmployees()),\n            getByDepartment: (department)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeesByDepartment\", {\n                    department\n                }, ()=>this.employeeService.getEmployeesByDepartment(department)),\n            getByRole: (role)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeesByRole\", {\n                    role\n                }, ()=>this.employeeService.getEmployeesByRole(role)),\n            getSales: ()=>this.executeWithLogging(\"EmployeeService\", \"getSalesEmployees\", {}, ()=>this.employeeService.getSalesEmployees())\n        };\n    }\n    // ==================== 库存数据访问 ====================\n    /**\r\n   * 库存数据访问器\r\n   */ get inventory() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"InventoryService\", \"getProductInventory\", params, ()=>this.inventoryService.getProductInventory(params)),\n            getByProductCode: (productCode)=>this.executeWithLogging(\"InventoryService\", \"getInventoryByProductCode\", {\n                    productCode\n                }, ()=>this.inventoryService.getInventoryByProductCode(productCode)),\n            update: (productCode, updates)=>this.executeWithLogging(\"InventoryService\", \"updateInventory\", {\n                    productCode,\n                    updates\n                }, ()=>this.inventoryService.updateInventory(productCode, updates)),\n            getLowStock: (threshold)=>this.executeWithLogging(\"InventoryService\", \"getLowStockProducts\", {\n                    threshold\n                }, ()=>this.inventoryService.getLowStockProducts(threshold)),\n            getValue: ()=>this.executeWithLogging(\"InventoryService\", \"getInventoryValue\", {}, ()=>this.inventoryService.getInventoryValue())\n        };\n    }\n    // ==================== 认证数据访问 ====================\n    /**\r\n   * 认证数据访问器\r\n   */ get auth() {\n        return {\n            // 认证相关\n            login: (credentials)=>this.executeWithLogging(\"AuthService\", \"login\", credentials, ()=>this.authService.login(credentials)),\n            logout: (refreshToken)=>this.executeWithLogging(\"AuthService\", \"logout\", {\n                    refreshToken\n                }, ()=>this.authService.logout(refreshToken)),\n            refreshToken: (request)=>this.executeWithLogging(\"AuthService\", \"refreshToken\", request, ()=>this.authService.refreshToken(request)),\n            validateToken: (token)=>this.executeWithLogging(\"AuthService\", \"validateToken\", {\n                    token\n                }, ()=>this.authService.validateToken(token)),\n            // 用户管理\n            getUserById: (id)=>this.executeWithLogging(\"AuthService\", \"getUserById\", {\n                    id\n                }, ()=>this.authService.getUserById(id)),\n            getUserByUsername: (username)=>this.executeWithLogging(\"AuthService\", \"getUserByUsername\", {\n                    username\n                }, ()=>this.authService.getUserByUsername(username)),\n            createUser: (userData)=>this.executeWithLogging(\"AuthService\", \"createUser\", userData, ()=>this.authService.createUser(userData)),\n            updateUser: (id, updates)=>this.executeWithLogging(\"AuthService\", \"updateUser\", {\n                    id,\n                    updates\n                }, ()=>this.authService.updateUser(id, updates)),\n            deleteUser: (id)=>this.executeWithLogging(\"AuthService\", \"deleteUser\", {\n                    id\n                }, ()=>this.authService.deleteUser(id)),\n            // 会话管理\n            createSession: (userId, sessionData)=>this.executeWithLogging(\"AuthService\", \"createSession\", {\n                    userId,\n                    sessionData\n                }, ()=>this.authService.createSession(userId, sessionData)),\n            getSession: (sessionId)=>this.executeWithLogging(\"AuthService\", \"getSession\", {\n                    sessionId\n                }, ()=>this.authService.getSession(sessionId)),\n            updateSession: (sessionId, updates)=>this.executeWithLogging(\"AuthService\", \"updateSession\", {\n                    sessionId,\n                    updates\n                }, ()=>this.authService.updateSession(sessionId, updates)),\n            deleteSession: (sessionId)=>this.executeWithLogging(\"AuthService\", \"deleteSession\", {\n                    sessionId\n                }, ()=>this.authService.deleteSession(sessionId)),\n            getUserSessions: (userId)=>this.executeWithLogging(\"AuthService\", \"getUserSessions\", {\n                    userId\n                }, ()=>this.authService.getUserSessions(userId)),\n            // 密码管理\n            changePassword: (userId, currentPassword, newPassword)=>this.executeWithLogging(\"AuthService\", \"changePassword\", {\n                    userId\n                }, ()=>this.authService.changePassword(userId, currentPassword, newPassword)),\n            resetPassword: (userId, newPassword)=>this.executeWithLogging(\"AuthService\", \"resetPassword\", {\n                    userId\n                }, ()=>this.authService.resetPassword(userId, newPassword))\n        };\n    }\n    // ==================== Token管理数据访问 ====================\n    /**\r\n   * Token管理数据访问器\r\n   */ get tokenManagement() {\n        return {\n            // Token生成\n            generateAccessToken: (payload)=>this.executeWithLogging(\"TokenManagementService\", \"generateAccessToken\", payload, ()=>this.tokenManagementService.generateAccessToken(payload)),\n            generateRefreshToken: (userId, sessionId)=>this.executeWithLogging(\"TokenManagementService\", \"generateRefreshToken\", {\n                    userId,\n                    sessionId\n                }, ()=>this.tokenManagementService.generateRefreshToken(userId, sessionId)),\n            // Token验证\n            verifyAccessToken: (token)=>this.executeWithLogging(\"TokenManagementService\", \"verifyAccessToken\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.verifyAccessToken(token)),\n            verifyRefreshToken: (token)=>this.executeWithLogging(\"TokenManagementService\", \"verifyRefreshToken\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.verifyRefreshToken(token)),\n            // Token工具方法\n            extractUserInfo: (token)=>this.executeWithLogging(\"TokenManagementService\", \"extractUserInfo\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.extractUserInfo(token)),\n            shouldRefreshToken: (token, thresholdMinutes)=>this.executeWithLogging(\"TokenManagementService\", \"shouldRefreshToken\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length,\n                    thresholdMinutes\n                }, ()=>this.tokenManagementService.shouldRefreshToken(token, thresholdMinutes)),\n            getTokenRemainingTime: (token)=>this.executeWithLogging(\"TokenManagementService\", \"getTokenRemainingTime\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.getTokenRemainingTime(token)),\n            generateSessionId: ()=>this.executeWithLogging(\"TokenManagementService\", \"generateSessionId\", {}, ()=>this.tokenManagementService.generateSessionId()),\n            // 配置检查\n            checkConfiguration: ()=>this.executeWithLogging(\"TokenManagementService\", \"checkConfiguration\", {}, ()=>this.tokenManagementService.checkConfiguration())\n        };\n    }\n    // ==================== 角色权限数据访问 ====================\n    /**\r\n   * 角色权限数据访问器\r\n   */ get roles() {\n        return {\n            // 角色管理\n            getAll: ()=>this.executeWithLogging(\"RoleService\", \"getRoles\", {}, ()=>this.roleService.getRoles()),\n            getById: (id)=>this.executeWithLogging(\"RoleService\", \"getRoleById\", {\n                    id\n                }, ()=>this.roleService.getRoleById(id)),\n            getByCode: (code)=>this.executeWithLogging(\"RoleService\", \"getRoleByCode\", {\n                    code\n                }, ()=>this.roleService.getRoleByCode(code)),\n            create: (roleData)=>this.executeWithLogging(\"RoleService\", \"createRole\", roleData, ()=>this.roleService.createRole(roleData)),\n            update: (id, updates)=>this.executeWithLogging(\"RoleService\", \"updateRole\", {\n                    id,\n                    updates\n                }, ()=>this.roleService.updateRole(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"RoleService\", \"deleteRole\", {\n                    id\n                }, ()=>this.roleService.deleteRole(id)),\n            // 权限管理\n            getPermissions: ()=>this.executeWithLogging(\"RoleService\", \"getPermissions\", {}, ()=>this.roleService.getPermissions()),\n            getPermissionsByModule: (module)=>this.executeWithLogging(\"RoleService\", \"getPermissionsByModule\", {\n                    module\n                }, ()=>this.roleService.getPermissionsByModule(module)),\n            assignPermissions: (roleId, permissionIds)=>this.executeWithLogging(\"RoleService\", \"assignRolePermissions\", {\n                    roleId,\n                    permissionIds\n                }, ()=>this.roleService.assignRolePermissions(roleId, permissionIds))\n        };\n    }\n    /**\r\n   * 订单数据访问器\r\n   */ get orders() {\n        return {\n            getAll: (params)=>{\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] orders.getAll 被调用，管理器ID:\", this.__managerId);\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] 使用的OrderService实例ID:\", this.orderService.__serviceId);\n                return this.executeWithLogging(\"OrderService\", \"getOrders\", params, ()=>this.orderService.getOrders(params));\n            },\n            getById: (id)=>this.executeWithLogging(\"OrderService\", \"getOrderById\", {\n                    id\n                }, ()=>this.orderService.getOrderById(id)),\n            getByNumber: (orderNumber)=>this.executeWithLogging(\"OrderService\", \"getOrderByNumber\", {\n                    orderNumber\n                }, ()=>this.orderService.getOrderByNumber(orderNumber)),\n            create: (data)=>{\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] orders.create 被调用，管理器ID:\", this.__managerId);\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] 使用的OrderService实例ID:\", this.orderService.__serviceId);\n                return this.executeWithLogging(\"OrderService\", \"createOrder\", data, ()=>this.orderService.createOrder(data));\n            },\n            update: (id, updates)=>this.executeWithLogging(\"OrderService\", \"updateOrder\", {\n                    id,\n                    updates\n                }, ()=>this.orderService.updateOrder(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"OrderService\", \"deleteOrder\", {\n                    id\n                }, ()=>this.orderService.deleteOrder(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"OrderService\", \"getOrdersByStatus\", {\n                    status\n                }, ()=>this.orderService.getOrdersByStatus(status)),\n            getByCustomer: (customerId)=>this.executeWithLogging(\"OrderService\", \"getOrdersByCustomer\", {\n                    customerId\n                }, ()=>this.orderService.getOrdersByCustomer(customerId)),\n            getBySalesRep: (salesRepId)=>this.executeWithLogging(\"OrderService\", \"getOrdersBySalesRep\", {\n                    salesRepId\n                }, ()=>this.orderService.getOrdersBySalesRep(salesRepId)),\n            getByDateRange: (startDate, endDate)=>this.executeWithLogging(\"OrderService\", \"getOrdersByDateRange\", {\n                    startDate,\n                    endDate\n                }, ()=>this.orderService.getOrdersByDateRange(startDate, endDate))\n        };\n    }\n    /**\r\n   * 工作时间配置数据访问器\r\n   */ get workTime() {\n        return {\n            getConfigurations: ()=>this.executeWithLogging(\"WorkTimeService\", \"getConfigurations\", {}, ()=>this.workTimeService.getConfigurations()),\n            getById: (id)=>this.executeWithLogging(\"WorkTimeService\", \"getConfigurationById\", {\n                    id\n                }, ()=>this.workTimeService.getConfigurationById(id)),\n            create: (data)=>this.executeWithLogging(\"WorkTimeService\", \"createConfiguration\", data, ()=>this.workTimeService.createConfiguration(data)),\n            update: (id, data)=>this.executeWithLogging(\"WorkTimeService\", \"updateConfiguration\", {\n                    id,\n                    ...data\n                }, ()=>this.workTimeService.updateConfiguration(id, data)),\n            delete: (id)=>this.executeWithLogging(\"WorkTimeService\", \"deleteConfiguration\", {\n                    id\n                }, ()=>this.workTimeService.deleteConfiguration(id)),\n            addWorkTimeSlot: (configId, slot)=>this.executeWithLogging(\"WorkTimeService\", \"addWorkTimeSlot\", {\n                    configId,\n                    slot\n                }, ()=>this.workTimeService.addWorkTimeSlot(configId, slot)),\n            updateWorkTimeSlot: (configId, slotId, data)=>this.executeWithLogging(\"WorkTimeService\", \"updateWorkTimeSlot\", {\n                    configId,\n                    slotId,\n                    ...data\n                }, ()=>this.workTimeService.updateWorkTimeSlot(configId, slotId, data)),\n            deleteWorkTimeSlot: (configId, slotId)=>this.executeWithLogging(\"WorkTimeService\", \"deleteWorkTimeSlot\", {\n                    configId,\n                    slotId\n                }, ()=>this.workTimeService.deleteWorkTimeSlot(configId, slotId)),\n            calculateWorkingMinutes: (workTimeSlots, breakTimeSlots)=>{\n                const startTime = Date.now();\n                const result = this.workTimeService.calculateWorkingMinutes(workTimeSlots, breakTimeSlots);\n                const duration = Date.now() - startTime;\n                this.logAccess(\"WorkTimeService\", \"calculateWorkingMinutes\", {\n                    workTimeSlots,\n                    breakTimeSlots\n                }, duration, true);\n                return result;\n            },\n            validateTimeSlot: (startTime, endTime)=>{\n                const start = Date.now();\n                const result = this.workTimeService.validateTimeSlot(startTime, endTime);\n                const duration = Date.now() - start;\n                this.logAccess(\"WorkTimeService\", \"validateTimeSlot\", {\n                    startTime,\n                    endTime\n                }, duration, true);\n                return result;\n            },\n            getDefault: ()=>this.executeWithLogging(\"WorkTimeService\", \"getDefaultConfiguration\", {}, ()=>this.workTimeService.getDefaultConfiguration())\n        };\n    }\n    /**\r\n   * 生产订单数据访问器\r\n   */ get productionOrders() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductionOrderService\", \"getAll\", params, ()=>this.productionOrderService.getAll(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductionOrderService\", \"getById\", {\n                    id\n                }, ()=>this.productionOrderService.getById(id)),\n            getByOrderNumber: (orderNumber)=>this.executeWithLogging(\"ProductionOrderService\", \"getByOrderNumber\", {\n                    orderNumber\n                }, ()=>this.productionOrderService.getByOrderNumber(orderNumber)),\n            createFromMRP: (data)=>this.executeWithLogging(\"ProductionOrderService\", \"createFromMRP\", data, ()=>this.productionOrderService.createFromMRP(data)),\n            // 添加通用的create方法（用于测试）\n            create: (data)=>this.executeWithLogging(\"ProductionOrderService\", \"createFromMRP\", data, ()=>{\n                    // 为测试目的添加默认MRP字段\n                    const mrpData = {\n                        ...data,\n                        mrpExecutionId: data.mrpExecutionId || \"mrp_\".concat(Date.now()),\n                        mrpExecutedBy: data.mrpExecutedBy || \"test-user\",\n                        mrpExecutedAt: data.mrpExecutedAt || new Date().toISOString()\n                    };\n                    return this.productionOrderService.createFromMRP(mrpData);\n                }),\n            update: (id, data)=>this.executeWithLogging(\"ProductionOrderService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.productionOrderService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"ProductionOrderService\", \"delete\", {\n                    id\n                }, ()=>this.productionOrderService.delete(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"ProductionOrderService\", \"getByStatus\", {\n                    status\n                }, ()=>this.productionOrderService.getByStatus(status)),\n            getBySalesOrderId: (salesOrderId)=>this.executeWithLogging(\"ProductionOrderService\", \"getBySalesOrderId\", {\n                    salesOrderId\n                }, ()=>this.productionOrderService.getBySalesOrderId(salesOrderId)),\n            // 添加统计方法\n            getStatistics: ()=>this.executeWithLogging(\"ProductionOrderService\", \"getStatistics\", {}, ()=>this.productionOrderService.getStatistics())\n        };\n    }\n    /**\r\n   * 生产工单数据访问器\r\n   */ get productionWorkOrders() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getAll\", params, ()=>this.productionWorkOrderService.getAll(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getById\", {\n                    id\n                }, ()=>this.productionWorkOrderService.getById(id)),\n            getByBatchNumber: (batchNumber)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByBatchNumber\", {\n                    batchNumber\n                }, ()=>this.productionWorkOrderService.getByBatchNumber(batchNumber)),\n            create: (data)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"create\", data, ()=>this.productionWorkOrderService.create(data)),\n            update: (id, data)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.productionWorkOrderService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"delete\", {\n                    id\n                }, ()=>this.productionWorkOrderService.delete(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByStatus\", {\n                    status\n                }, ()=>this.productionWorkOrderService.getByStatus(status)),\n            getBySourceOrderId: (sourceOrderId)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getBySourceOrderId\", {\n                    sourceOrderId\n                }, ()=>this.productionWorkOrderService.getBySourceOrderId(sourceOrderId)),\n            getByWorkstation: (workstation)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByWorkstation\", {\n                    workstation\n                }, ()=>this.productionWorkOrderService.getByWorkstation(workstation))\n        };\n    }\n    /**\r\n   * 工位数据访问器\r\n   * 🔧 修复：为工位数据添加特殊的缓存策略，确保排程算法获取最新状态\r\n   */ get workstations() {\n        return {\n            // 查询方法 - 使用内置缓存系统\n            getAll: (params)=>this.executeWithLogging(\"WorkstationService\", \"getAll\", params, ()=>this.workstationService.getAll()),\n            getWorkstations: (params)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstations\", params, ()=>this.workstationService.getWorkstations(params)),\n            getById: (id)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstationById\", {\n                    id\n                }, ()=>this.workstationService.getWorkstationById(id)),\n            getActiveWorkstations: ()=>this.executeWithLogging(\"WorkstationService\", \"getActiveWorkstations\", {}, ()=>this.workstationService.getActiveWorkstations()),\n            // CRUD操作\n            create: (data)=>this.executeWithLogging(\"WorkstationService\", \"create\", data, ()=>this.workstationService.create(data)),\n            update: (id, data)=>this.executeWithLogging(\"WorkstationService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.workstationService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"WorkstationService\", \"delete\", {\n                    id\n                }, ()=>this.workstationService.delete(id)),\n            // 状态管理\n            getStatus: (id)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstationStatus\", {\n                    id\n                }, ()=>this.workstationService.getWorkstationStatus(id)),\n            updateStatus: (id, status)=>this.executeWithLogging(\"WorkstationService\", \"updateWorkstationStatus\", {\n                    id,\n                    ...status\n                }, ()=>this.workstationService.updateWorkstationStatus(id, status)),\n            // 队列管理\n            addToQueue: (workstationId, batchNumber)=>this.executeWithLogging(\"WorkstationService\", \"addToQueue\", {\n                    workstationId,\n                    batchNumber\n                }, ()=>this.workstationService.addToQueue(workstationId, batchNumber)),\n            migrateBatchNumberFormats: ()=>this.executeWithLogging(\"WorkstationService\", \"migrateBatchNumberFormats\", {}, ()=>this.workstationService.migrateBatchNumberFormats()),\n            // 🔧 统一的工位重置方法\n            resetAllWorkstationsToIdle: ()=>this.executeWithLogging(\"WorkstationService\", \"resetAllWorkstationsToIdle\", {}, ()=>this.workstationService.resetAllWorkstationsToIdle())\n        };\n    }\n    // ==================== 管理功能 ====================\n    /**\r\n   * 获取访问日志\r\n   */ getAccessLogs(limit) {\n        return limit ? this.accessLogs.slice(-limit) : [\n            ...this.accessLogs\n        ];\n    }\n    /**\r\n   * 清除访问日志\r\n   */ clearAccessLogs() {\n        this.accessLogs = [];\n    }\n    /**\r\n   * 获取统计信息\r\n   */ getStatistics() {\n        const totalRequests = this.accessLogs.length;\n        const successfulRequests = this.accessLogs.filter((log)=>log.success).length;\n        const failedRequests = totalRequests - successfulRequests;\n        const averageDuration = totalRequests > 0 ? this.accessLogs.reduce((sum, log)=>sum + log.duration, 0) / totalRequests : 0;\n        return {\n            totalRequests,\n            successfulRequests,\n            failedRequests,\n            successRate: totalRequests > 0 ? successfulRequests / totalRequests * 100 : 0,\n            averageDuration: Math.round(averageDuration),\n            config: this.config\n        };\n    }\n    /**\r\n   * 初始化数据变更通知机制\r\n   */ initializeDataChangeNotification() {\n    // 这里可以添加额外的初始化逻辑\n    // 数据变更通知器已经在导入时自动初始化\n    }\n    /**\r\n   * 获取数据变更通知器\r\n   */ getDataChangeNotifier() {\n        return _DataChangeNotifier__WEBPACK_IMPORTED_MODULE_13__.dataChangeNotifier;\n    }\n    // 优先级同步服务已删除\n    /**\r\n   * 成本计算数据访问器\r\n   */ get costCalculations() {\n        return {\n            getAllCalculations: (params)=>this.executeWithLogging(\"CostCalculationService\", \"getAllCalculations\", params, ()=>this.costCalculationService.getAllCalculations(params)),\n            getCalculationById: (id)=>this.executeWithLogging(\"CostCalculationService\", \"getCalculationById\", {\n                    id\n                }, ()=>this.costCalculationService.getCalculationById(id)),\n            createCalculation: (data)=>this.executeWithLogging(\"CostCalculationService\", \"createCalculation\", data, ()=>this.costCalculationService.createCalculation(data)),\n            updateCalculation: (id, data)=>this.executeWithLogging(\"CostCalculationService\", \"updateCalculation\", {\n                    id,\n                    ...data\n                }, ()=>this.costCalculationService.updateCalculation(id, data)),\n            deleteCalculation: (id)=>this.executeWithLogging(\"CostCalculationService\", \"deleteCalculation\", {\n                    id\n                }, ()=>this.costCalculationService.deleteCalculation(id)),\n            getCalculationsByProduct: (productModelCode)=>this.executeWithLogging(\"CostCalculationService\", \"getCalculationsByProduct\", {\n                    productModelCode\n                }, ()=>this.costCalculationService.getCalculationsByProduct(productModelCode)),\n            getPendingReconciliations: ()=>this.executeWithLogging(\"CostCalculationService\", \"getPendingReconciliations\", {}, ()=>this.costCalculationService.getPendingReconciliations()),\n            getStatistics: ()=>this.executeWithLogging(\"CostCalculationService\", \"getStatistics\", {}, ()=>this.costCalculationService.getStatistics()),\n            getCostSummary: ()=>this.executeWithLogging(\"CostCalculationService\", \"getCostSummary\", {}, ()=>this.costCalculationService.getCostSummary())\n        };\n    }\n    // 旧缓存管理方法已移除，使用内置缓存系统\n    // 移除重复的方法定义，使用下面的统一缓存管理方法\n    // invalidateCache(tags: string[]): void {\n    //   this.cacheManager.deleteByTags(tags)\n    // }\n    /**\r\n   * 更新配置\r\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        // 如果禁用缓存，清空现有缓存\n        if (newConfig.enableCaching === false) {\n            this.cache.clear();\n            this.requestCache.clear();\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存已禁用并清空\");\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 配置已更新:\", this.config);\n        }\n    }\n    // ==================== 缓存管理接口 ====================\n    /**\r\n   * 清除特定服务的缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearServiceCache(serviceName, operation) {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return 0;\n        }\n        // ✅ 架构合规：使用内置缓存系统清理服务缓存\n        let deletedCount = 0;\n        const pattern = \"\".concat(serviceName, \":\");\n        for (const [key] of Array.from(this.cache)){\n            if (key.startsWith(pattern)) {\n                this.cache.delete(key);\n                deletedCount++;\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除服务缓存: \".concat(serviceName).concat(operation ? \" (\".concat(operation, \")\") : \"\", \"，删除了 \").concat(deletedCount, \" 个缓存项\"));\n        }\n        return deletedCount;\n    }\n    /**\r\n   * 清除特定数据类型的缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearDataTypeCache(dataType, affectedIds) {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return 0;\n        }\n        let deletedCount = 0;\n        const serviceMap = {\n            \"products\": \"ProductService\",\n            \"orders\": \"OrderService\",\n            \"workstations\": \"WorkstationService\",\n            \"statistics\": \"StatisticsService\"\n        };\n        const serviceName = serviceMap[dataType] || dataType;\n        for (const [key] of Array.from(this.cache)){\n            if (key.startsWith(\"\".concat(serviceName, \":\"))) {\n                // 如果指定了特定ID，只删除相关的缓存\n                if (affectedIds && affectedIds.length > 0) {\n                    const hasMatchingId = affectedIds.some((id)=>key.includes(id));\n                    if (hasMatchingId) {\n                        this.cache.delete(key);\n                        deletedCount++;\n                    }\n                } else {\n                    // 删除所有该数据类型的缓存\n                    this.cache.delete(key);\n                    deletedCount++;\n                }\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除数据类型缓存: \".concat(dataType, \"，影响ID: \").concat((affectedIds === null || affectedIds === void 0 ? void 0 : affectedIds.join(\", \")) || \"全部\", \"，删除了 \").concat(deletedCount, \" 个缓存项\"));\n        }\n        return deletedCount;\n    }\n    /**\r\n   * 清除所有缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearAllCache() {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return;\n        }\n        // ✅ 架构合规：使用内置缓存系统清理所有缓存\n        const beforeSize = this.cache.size;\n        this.cache.clear();\n        this.requestCache.clear();\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除所有缓存，删除了 \".concat(beforeSize, \" 个缓存项\"));\n        }\n    }\n    /**\r\n   * 获取缓存统计信息 - 优化版\r\n   * 符合数据调用规范的监控接口\r\n   */ getCacheStatistics() {\n        // ✅ 架构合规：使用内置缓存系统获取统计信息\n        const entries = Array.from(this.cache.values());\n        const totalSize = entries.reduce((sum, entry)=>sum + entry.estimatedSize, 0);\n        const averageEntrySize = entries.length > 0 ? totalSize / entries.length : 0;\n        // 计算优先级分布\n        const priorityDistribution = {\n            critical: 0,\n            high: 0,\n            medium: 0,\n            low: 0\n        };\n        entries.forEach((entry)=>{\n            priorityDistribution[entry.priority]++;\n        });\n        return {\n            enabled: this.config.enableCaching,\n            size: this.cache.size,\n            hits: this.cacheStats.hits,\n            misses: this.cacheStats.misses,\n            hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0,\n            totalRequests: this.cacheStats.totalRequests,\n            // ✅ 优化：新增统计信息\n            evictions: this.cacheStats.evictions,\n            prewarmHits: this.cacheStats.prewarmHits,\n            memoryPressure: this.memoryPressure,\n            averageEntrySize,\n            priorityDistribution\n        };\n    }\n    /**\r\n   * 更新缓存配置\r\n   */ updateCacheConfig(newConfig) {\n        this.cacheConfig = {\n            ...this.cacheConfig,\n            ...newConfig\n        };\n        console.log(\"[DataAccessManager] 缓存配置已更新\");\n    }\n    // ✅ 架构合规：移除违规的CacheStrategyManager方法，使用内置缓存系统替代\n    /**\r\n   * 批量操作方法\r\n   */ get batch() {\n        return {\n            // 批量创建生产订单\n            createProductionOrders: (orders)=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__.performanceOptimizer.batchCreateProductionOrders(orders),\n            // 批量更新生产订单\n            updateProductionOrders: (updates)=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__.performanceOptimizer.batchUpdateProductionOrders(updates),\n            // 批量创建生产工单 - 使用并发控制优化\n            createProductionWorkOrders: async (workOrders)=>{\n                const startTime = performance.now();\n                try {\n                    // 创建任务数组\n                    const tasks = workOrders.map((workOrder)=>async ()=>{\n                            const result = await this.productionWorkOrders.create(workOrder);\n                            if (result.status !== \"success\") {\n                                throw new Error(result.message || \"创建工单失败\");\n                            }\n                            return result.data;\n                        });\n                    const taskNames = workOrders.map((_, index)=>\"批量创建工单-\".concat(index + 1));\n                    const batchResult = await _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__.batchOperationController.executeBatch(tasks, taskNames);\n                    const duration = performance.now() - startTime;\n                    const { successful, failed } = batchResult;\n                    const successfulData = successful.map((result)=>result.data).filter(Boolean);\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.createProductionWorkOrders\", duration, failed.length === 0);\n                    return {\n                        status: \"success\",\n                        data: successfulData,\n                        message: \"成功批量创建\".concat(successfulData.length, \"个生产工单\").concat(failed.length > 0 ? \"，\".concat(failed.length, \"个失败\") : \"\"),\n                        batchResult: {\n                            successful: successful.length,\n                            failed: failed.length,\n                            successRate: batchResult.successRate,\n                            totalDuration: duration\n                        }\n                    };\n                } catch (error) {\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.createProductionWorkOrders\", duration, false);\n                    throw error;\n                }\n            },\n            // 批量更新工位状态\n            updateWorkstationStatuses: async (updates)=>{\n                const startTime = performance.now();\n                try {\n                    const results = await Promise.all(updates.map((param)=>{\n                        let { id, status } = param;\n                        return this.workstations.updateStatus(id, status);\n                    }));\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.updateWorkstationStatuses\", duration, true);\n                    // 使用内置缓存系统清理相关缓存\n                    this.clearDataTypeCache(\"workstations\", updates.map((u)=>u.id));\n                    return {\n                        status: \"success\",\n                        data: results.filter((r)=>r.status === \"success\").map((r)=>r.data),\n                        message: \"成功批量更新\".concat(results.length, \"个工位状态\")\n                    };\n                } catch (error) {\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.updateWorkstationStatuses\", duration, false);\n                    throw error;\n                }\n            }\n        };\n    }\n    /**\r\n   * 性能监控方法\r\n   */ get performance() {\n        return {\n            // 获取性能指标\n            getMetrics: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.getMetrics(),\n            // 获取性能警告\n            getAlerts: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.getAlerts(),\n            // 获取优化建议\n            getSuggestions: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.getOptimizationSuggestions(),\n            // 生成性能报告\n            generateReport: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.generatePerformanceReport(),\n            // 重置性能指标\n            reset: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.reset(),\n            // 优化缓存策略\n            optimizeCache: ()=>{\n                // 使用内置缓存系统进行优化\n                this.evictOldestEntries();\n                console.log(\"[DataAccessManager] 缓存优化完成\");\n            },\n            // 获取缓存性能指标\n            getCacheMetrics: ()=>{\n                return this.getCacheStatistics();\n            },\n            // 获取性能优化器指标\n            getOptimizerMetrics: ()=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__.performanceOptimizer.getPerformanceMetrics()\n        };\n    }\n    // 旧的缓存管理方法已移除，使用内置缓存系统的统一接口\n    /**\r\n   * 性能监控管理方法\r\n   */ getPerformanceMetrics() {\n        // 更新方法统计到性能指标中\n        this.performanceMetrics.methodStats = this.methodStats;\n        return {\n            ...this.performanceMetrics\n        };\n    }\n    getRealTimeMetrics() {\n        const batchControllerStatus = _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__.batchOperationController.getStatus();\n        // 分析系统健康状况\n        const systemHealth = this.analyzeSystemHealth();\n        return {\n            currentConcurrency: batchControllerStatus.running,\n            queueLength: batchControllerStatus.queued,\n            recentCalls: [\n                ...this.recentCalls.slice(-50)\n            ],\n            systemHealth\n        };\n    }\n    analyzeSystemHealth() {\n        const issues = [];\n        const recommendations = [];\n        // 检查错误率\n        const recentErrorRate = this.recentCalls.length > 0 ? this.recentCalls.filter((call)=>call.status === \"error\").length / this.recentCalls.length : 0;\n        if (recentErrorRate > 0.1) {\n            issues.push(\"错误率过高: \".concat((recentErrorRate * 100).toFixed(2), \"%\"));\n            recommendations.push(\"检查网络连接和服务状态\");\n        }\n        // 检查响应时间\n        if (this.performanceMetrics.averageResponseTime > 2000) {\n            issues.push(\"平均响应时间过长: \".concat(this.performanceMetrics.averageResponseTime.toFixed(0), \"ms\"));\n            recommendations.push(\"考虑优化查询或增加缓存\");\n        }\n        // 检查缓存命中率\n        if (this.performanceMetrics.cacheHitRate < 0.3) {\n            issues.push(\"缓存命中率较低: \".concat((this.performanceMetrics.cacheHitRate * 100).toFixed(2), \"%\"));\n            recommendations.push(\"检查缓存策略和TTL设置\");\n        }\n        // 检查并发队列\n        const batchStatus = _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__.batchOperationController.getStatus();\n        if (batchStatus.queued > 10) {\n            issues.push(\"批量操作队列积压: \".concat(batchStatus.queued, \"个任务\"));\n            recommendations.push(\"考虑增加并发数或优化任务处理\");\n        }\n        // 确定系统状态\n        let status = \"healthy\";\n        if (issues.length > 0) {\n            status = recentErrorRate > 0.2 || this.performanceMetrics.averageResponseTime > 5000 ? \"critical\" : \"warning\";\n        }\n        return {\n            status,\n            issues,\n            recommendations\n        };\n    }\n    resetPerformanceMetrics() {\n        this.initializePerformanceMonitoring();\n        console.log(\"[DataAccessManager] 性能指标已重置\");\n    }\n    getSlowQueries() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n        return this.performanceMetrics.slowQueries.sort((a, b)=>b.duration - a.duration).slice(0, limit);\n    }\n    getMethodStatistics() {\n        return Array.from(this.methodStats.entries()).map((param)=>{\n            let [method, stats] = param;\n            return {\n                method,\n                calls: stats.calls,\n                averageTime: stats.averageTime,\n                successRate: stats.successRate,\n                lastCall: new Date(stats.lastCall).toLocaleString()\n            };\n        }).sort((a, b)=>b.calls - a.calls) // 按调用次数排序\n        ;\n    }\n    exportPerformanceReport() {\n        return {\n            timestamp: new Date().toISOString(),\n            summary: this.getPerformanceMetrics(),\n            realTime: this.getRealTimeMetrics(),\n            topMethods: this.getMethodStatistics().slice(0, 10),\n            slowQueries: this.getSlowQueries(10)\n        };\n    }\n    // ==================== 批量操作 ====================\n    /**\r\n   * 批量操作访问器\r\n   * 提供批量创建、更新、删除等操作\r\n   */ get batch() {\n        return {\n            // 批量创建生产订单\n            createProductionOrders: async (orders)=>{\n                const results = [];\n                const errors = [];\n                for (const order of orders){\n                    try {\n                        const result = await this.productionOrders.create(order);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(result.message || \"创建失败\");\n                        }\n                    } catch (error) {\n                        errors.push(error instanceof Error ? error.message : \"未知错误\");\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功创建\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"创建过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量更新生产订单\n            updateProductionOrders: async (updates)=>{\n                const results = [];\n                const errors = [];\n                for (const { id, data } of updates){\n                    try {\n                        const result = await this.productionOrders.update(id, data);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"更新失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功更新\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"更新过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量删除生产订单\n            deleteProductionOrders: async (ids)=>{\n                const successIds = [];\n                const errors = [];\n                for (const id of ids){\n                    try {\n                        const result = await this.productionOrders.delete(id);\n                        if (result.status === \"success\") {\n                            successIds.push(id);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"删除失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: true,\n                        message: \"成功删除\".concat(successIds.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: false,\n                        message: \"删除过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量查询生产订单\n            getProductionOrders: async (ids)=>{\n                const results = [];\n                const errors = [];\n                for (const id of ids){\n                    try {\n                        const result = await this.productionOrders.getById(id);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"查询失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功查询\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"查询过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            }\n        };\n    }\n    /**\r\n   * 获取系统配置\r\n   */ getConfig() {\n        return {\n            ...this.systemConfig\n        };\n    }\n    /**\r\n   * 更新系统配置\r\n   */ updateConfig(config) {\n        this.systemConfig = {\n            ...this.systemConfig,\n            ...config\n        };\n        console.log(\"[DataAccessManager] 系统配置已更新:\", config);\n        // 根据配置更新对应的系统行为\n        if (config.enableCaching !== undefined) {\n            console.log(\"[DataAccessManager] 缓存状态: \".concat(config.enableCaching ? \"已启用\" : \"已禁用\"));\n        }\n        if (config.cacheTimeout !== undefined) {\n            console.log(\"[DataAccessManager] 缓存超时时间: \".concat(config.cacheTimeout, \"ms\"));\n        }\n        if (config.enableLogging !== undefined) {\n            console.log(\"[DataAccessManager] 日志状态: \".concat(config.enableLogging ? \"已启用\" : \"已禁用\"));\n        }\n        if (config.logLevel !== undefined) {\n            console.log(\"[DataAccessManager] 日志级别: \".concat(config.logLevel));\n        }\n    }\n    /**\r\n   * 重置配置到默认值\r\n   */ resetConfig() {\n        this.systemConfig = {\n            enableCaching: true,\n            cacheTimeout: 5 * 60 * 1000,\n            enableLogging: true,\n            logLevel: \"INFO\",\n            enablePerformanceMonitoring: true,\n            maxConcurrentRequests: 10,\n            retryAttempts: 3,\n            retryDelay: 1000\n        };\n        console.log(\"[DataAccessManager] 系统配置已重置\");\n    }\n    constructor(config = {}){\n        this.accessLogs = [];\n        // ✅ 优化：增强缓存管理属性\n        this.cache = new Map();\n        this.cacheConfig = DEFAULT_CACHE_CONFIG;\n        this.requestCache = new Map();\n        this.cacheStats = {\n            hits: 0,\n            misses: 0,\n            totalRequests: 0,\n            evictions: 0,\n            prewarmHits: 0\n        };\n        // ✅ 优化：智能缓存管理\n        this.cacheAccessHistory = new Map() // 访问时间历史\n        ;\n        this.prewarmTimer = null;\n        this.lastMemoryCheck = 0;\n        this.memoryPressure = false;\n        // 性能监控相关属性\n        this.performanceMetrics = {\n            totalCalls: 0,\n            successCalls: 0,\n            errorCalls: 0,\n            averageResponseTime: 0,\n            minResponseTime: 0,\n            maxResponseTime: 0,\n            cacheHitRate: 0,\n            slowQueries: [],\n            methodStats: new Map(),\n            hourlyStats: []\n        };\n        this.recentCalls = [];\n        this.methodStats = new Map();\n        // 🔧 优化：批量性能报告\n        this.performanceBatch = [];\n        this.batchReportTimer = null;\n        this.BATCH_REPORT_INTERVAL = 5000 // 5秒批量报告一次\n        ;\n        // ==================== 配置管理 ====================\n        /**\r\n   * 系统配置管理\r\n   * 提供动态配置更新和查询功能\r\n   */ this.systemConfig = {\n            enableCaching: true,\n            cacheTimeout: 5 * 60 * 1000,\n            enableLogging: true,\n            logLevel: \"INFO\",\n            enablePerformanceMonitoring: true,\n            maxConcurrentRequests: 10,\n            retryAttempts: 3,\n            retryDelay: 1000\n        };\n        this.config = {\n            ...DEFAULT_CONFIG,\n            ...config\n        };\n        this.__managerId = Math.random().toString(36).substr(2, 9);\n        console.log(\"✅ [DataAccessManager] 创建实例，ID:\", this.__managerId, \"内置缓存系统已启用\");\n        // 初始化内置缓存系统\n        if (this.config.enableCaching) {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 内置缓存系统已启用\");\n            // ✅ 优化：启动智能缓存管理\n            this.initializeAdvancedCaching();\n        } else {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 缓存已禁用\");\n        }\n        // 初始化性能监控\n        this.initializePerformanceMonitoring();\n        // 初始化服务实例\n        this.productService = _ProductDataAccessService__WEBPACK_IMPORTED_MODULE_0__.ProductDataAccessService.getInstance();\n        this.customerService = _CustomerDataAccessService__WEBPACK_IMPORTED_MODULE_1__.CustomerDataAccessService.getInstance();\n        this.employeeService = _EmployeeDataAccessService__WEBPACK_IMPORTED_MODULE_2__.EmployeeDataAccessService.getInstance();\n        this.inventoryService = _InventoryDataAccessService__WEBPACK_IMPORTED_MODULE_3__.InventoryDataAccessService.getInstance();\n        this.orderService = _OrderDataAccessService__WEBPACK_IMPORTED_MODULE_4__.OrderDataAccessService.getInstance();\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 初始化OrderService，管理器ID:\", this.__managerId);\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] OrderService实例ID:\", this.orderService.__serviceId);\n        this.workTimeService = _WorkTimeDataAccessService__WEBPACK_IMPORTED_MODULE_5__.WorkTimeDataAccessService.getInstance();\n        this.productionOrderService = _ProductionOrderDataAccessService__WEBPACK_IMPORTED_MODULE_6__.ProductionOrderDataAccessService.getInstance();\n        this.productionWorkOrderService = _ProductionWorkOrderDataAccessService__WEBPACK_IMPORTED_MODULE_7__.ProductionWorkOrderDataAccessService.getInstance();\n        this.workstationService = _WorkstationDataAccessService__WEBPACK_IMPORTED_MODULE_8__.WorkstationDataAccessService.getInstance();\n        this.costCalculationService = _CostCalculationDataAccessService__WEBPACK_IMPORTED_MODULE_9__.CostCalculationDataAccessService.getInstance();\n        this.authService = _AuthDataAccessService__WEBPACK_IMPORTED_MODULE_10__.AuthDataAccessService.getInstance();\n        this.roleService = new _RoleDataAccessService__WEBPACK_IMPORTED_MODULE_11__.RoleDataAccessService();\n        this.tokenManagementService = _TokenManagementService__WEBPACK_IMPORTED_MODULE_12__.TokenManagementService.getInstance();\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 认证服务、角色服务和Token管理服务已初始化\");\n        // 初始化数据变更通知机制\n        this.initializeDataChangeNotification();\n        // 优先级同步服务已删除\n        if (true) {\n            console.log({\n                version: _DataAccessLayer__WEBPACK_IMPORTED_MODULE_17__.API_VERSION,\n                config: this.config,\n                dataChangeNotifier: \"已启用\"\n            });\n        }\n    }\n}\n// 创建单例实例\nconst dataAccessManager = DataAccessManager.getInstance();\n// 🔧 将DataAccessManager暴露到全局作用域（用于调试和测试）\nif (true) {\n    window.dataAccessManager = dataAccessManager;\n    console.log(\"\\uD83D\\uDD27 [DataAccessManager] 已暴露到全局作用域 window.dataAccessManager\");\n}\nconst { products, customers, employees, inventory, productionOrders, productionWorkOrders, workstations, costCalculations, auth, tokenManagement } = dataAccessManager;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9kYXRhQWNjZXNzL0RhdGFBY2Nlc3NNYW5hZ2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7Q0FHQyxHQUVvRTtBQUNFO0FBQ0E7QUFDRTtBQUNSO0FBQ007QUFDYztBQUNRO0FBQ2hCO0FBQ1E7QUFDTDtBQUNBO0FBQ2Y7QUFDUjtBQUN6RCw0Q0FBNEM7QUFDaUI7QUFDZ0I7QUFDN0UsNkJBQTZCO0FBQ3dDO0FBcURyRSxhQUFhO0FBUWE7QUFDMkI7QUF3RXJEOztDQUVDLEdBQ00sTUFBTW1CLHVCQUFvQztJQUMvQ0MsZUFBZTtJQUNmQyxZQUFZLElBQUksS0FBSztJQUNyQkMsU0FBUztJQUNUQyxZQUFZO1FBQ1ZDLFFBQVE7WUFBRUMsS0FBSyxJQUFJLEtBQUs7WUFBTUMsU0FBUztZQUFNQyxVQUFVO1lBQVFDLFlBQVk7UUFBSTtRQUMvRUMsVUFBVTtZQUFFSixLQUFLLEtBQUssS0FBSztZQUFNQyxTQUFTO1lBQU1DLFVBQVU7WUFBVUMsWUFBWTtRQUFJO1FBQ3BGRSxjQUFjO1lBQUVMLEtBQUssS0FBSztZQUFNQyxTQUFTO1lBQU1DLFVBQVU7WUFBWUMsWUFBWTtRQUFHO1FBQ3BGRyxZQUFZO1lBQUVOLEtBQUssS0FBSztZQUFNQyxTQUFTO1lBQU1DLFVBQVU7WUFBT0MsWUFBWTtRQUFJO1FBQzlFSSxXQUFXO1lBQUVQLEtBQUssS0FBSyxLQUFLO1lBQU1DLFNBQVM7WUFBTUMsVUFBVTtZQUFVQyxZQUFZO1FBQUk7UUFDckZLLFdBQVc7WUFBRVIsS0FBSyxLQUFLLEtBQUs7WUFBTUMsU0FBUztZQUFNQyxVQUFVO1lBQVVDLFlBQVk7UUFBSSxFQUFJLE9BQU87SUFDbEc7SUFDQU0sWUFBWTtRQUNWUixTQUFTO1FBQ1RTLGdCQUFnQixNQUFNLE1BQU07SUFDOUI7SUFDQSxnQkFBZ0I7SUFDaEJDLGVBQWU7UUFDYlYsU0FBUztRQUNUVyxXQUFXO1FBQ1hDLFlBQVk7WUFDVkMsZUFBZTtZQUNmQyxnQkFBZ0IsS0FBSyxlQUFlO1FBQ3RDO0lBQ0Y7SUFDQSxjQUFjO0lBQ2RDLFlBQVk7UUFDVmYsU0FBUztRQUNUSCxZQUFZO1lBQUM7WUFBZ0I7WUFBVTtTQUFXO1FBQ2xEbUIsa0JBQWtCLE1BQU0sVUFBVTtJQUNwQztBQUNGLEVBQUM7QUFFRDs7Q0FFQyxHQUNELE1BQU1DLGlCQUFtQztJQUN2Q0MsZUFBZTtJQUNmeEIsZUFBZTtJQUNmQyxZQUFZLElBQUksS0FBSztJQUNyQndCLGNBQWM7SUFDZEMsZUFBZTtJQUNmQyxZQUFZLEtBQUssS0FBSztBQUN4QjtBQXNDQTs7Q0FFQyxHQUNNLE1BQU1DO0lBbUlYOztHQUVDLEdBQ0QsT0FBY0MsWUFBWUMsTUFBa0MsRUFBcUI7UUFDL0UsSUFBSSxDQUFDRixrQkFBa0JHLFFBQVEsRUFBRTtZQUMvQkgsa0JBQWtCRyxRQUFRLEdBQUcsSUFBSUgsa0JBQWtCRTtRQUNyRDtRQUNBLE9BQU9GLGtCQUFrQkcsUUFBUTtJQUNuQztJQUVBLGFBQWE7SUFFYjs7R0FFQyxHQUNELFVBQ0VFLE9BQWUsRUFDZkMsTUFBYyxFQUNkQyxNQUFXLEVBQ1hDLFFBQWdCLEVBQ2hCQyxPQUFnQixFQUNoQkMsS0FBYyxFQUNkQyxTQUFtQixFQUNiO1FBQ04sSUFBSSxDQUFDLElBQUksQ0FBQ1QsTUFBTSxDQUFDTixhQUFhLEVBQUU7UUFFaEMsTUFBTWdCLE1BQWlCO1lBQ3JCLGVBQWU7WUFDZkMsV0FBVzNDLGdFQUFrQkEsQ0FBQzRDLEdBQUc7WUFDakNUO1lBQ0FDO1lBQ0FDO1lBQ0FDO1lBQ0FDO1lBQ0FDO1FBQ0Y7UUFFQSxJQUFJLENBQUNLLFVBQVUsQ0FBQ0MsSUFBSSxDQUFDSjtRQUVyQixjQUFjO1FBQ2QsSUFBSSxJQUFJLENBQUNHLFVBQVUsQ0FBQ0UsTUFBTSxHQUFHLE1BQU07WUFDakMsSUFBSSxDQUFDRixVQUFVLEdBQUcsSUFBSSxDQUFDQSxVQUFVLENBQUNHLEtBQUssQ0FBQyxDQUFDO1FBQzNDO1FBRUEscUJBQXFCO1FBQ3JCLElBQUlDLElBQXlCLEVBQWU7WUFDMUMsSUFBSSxDQUFDQyxxQkFBcUIsQ0FBQ2YsU0FBU0MsUUFBUUUsVUFBVUMsU0FBU0UsYUFBYTtRQUM5RTtJQUNGO0lBRUEsaUJBQWlCO0lBRWpCOztHQUVDLEdBQ0Qsc0JBQThCTixPQUFlLEVBQUVDLE1BQWMsRUFBRUUsUUFBZ0IsRUFBRUMsT0FBZ0IsRUFBRUUsU0FBa0IsRUFBUTtRQUMzSCxJQUFJLENBQUNVLGdCQUFnQixDQUFDTCxJQUFJLENBQUM7WUFDekJYO1lBQ0FDO1lBQ0FFO1lBQ0FDO1lBQ0FFO1lBQ0FFLFdBQVdTLEtBQUtSLEdBQUc7UUFDckI7UUFFQSxZQUFZO1FBQ1osSUFBSSxDQUFDLElBQUksQ0FBQ1MsZ0JBQWdCLEVBQUU7WUFDMUIsSUFBSSxDQUFDQSxnQkFBZ0IsR0FBR0MsV0FBVztnQkFDakMsSUFBSSxDQUFDQyxxQkFBcUI7WUFDNUIsR0FBRyxJQUFJLENBQUNDLHFCQUFxQjtRQUMvQjtJQUNGO0lBRUE7O0dBRUMsR0FDRCx3QkFBc0M7UUFDcEMsSUFBSSxJQUFJLENBQUNMLGdCQUFnQixDQUFDSixNQUFNLEtBQUssR0FBRztZQUN0QyxJQUFJLENBQUNNLGdCQUFnQixHQUFHO1lBQ3hCO1FBQ0Y7UUFFQSxPQUFPO1FBQ1AsTUFBTUksUUFBUTtZQUNaQyxpQkFBaUIsSUFBSSxDQUFDUCxnQkFBZ0IsQ0FBQ0osTUFBTTtZQUM3Q1ksY0FBYyxJQUFJLENBQUNSLGdCQUFnQixDQUFDUyxNQUFNLENBQUNDLENBQUFBLEtBQU1BLEdBQUd0QixPQUFPLEVBQUVRLE1BQU07WUFDbkVlLFdBQVcsSUFBSSxDQUFDWCxnQkFBZ0IsQ0FBQ1MsTUFBTSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHcEIsU0FBUyxFQUFFTSxNQUFNO1lBQ2xFZ0IsYUFBYUMsS0FBS0MsS0FBSyxDQUFDLElBQUksQ0FBQ2QsZ0JBQWdCLENBQUNlLE1BQU0sQ0FBQyxDQUFDQyxLQUFLTixLQUFPTSxNQUFNTixHQUFHdkIsUUFBUSxFQUFFLEtBQUssSUFBSSxDQUFDYSxnQkFBZ0IsQ0FBQ0osTUFBTTtZQUN0SHFCLFVBQVUsSUFBSUMsSUFBSSxJQUFJLENBQUNsQixnQkFBZ0IsQ0FBQ21CLEdBQUcsQ0FBQ1QsQ0FBQUEsS0FBTUEsR0FBRzFCLE9BQU8sR0FBR29DLElBQUk7WUFDbkVDLFdBQVc7Z0JBQ1RDLE9BQU8sSUFBSXJCLEtBQUtZLEtBQUtVLEdBQUcsSUFBSSxJQUFJLENBQUN2QixnQkFBZ0IsQ0FBQ21CLEdBQUcsQ0FBQ1QsQ0FBQUEsS0FBTUEsR0FBR2xCLFNBQVMsSUFBSWdDLGtCQUFrQjtnQkFDOUZDLEtBQUssSUFBSXhCLEtBQUtZLEtBQUthLEdBQUcsSUFBSSxJQUFJLENBQUMxQixnQkFBZ0IsQ0FBQ21CLEdBQUcsQ0FBQ1QsQ0FBQUEsS0FBTUEsR0FBR2xCLFNBQVMsSUFBSWdDLGtCQUFrQjtZQUM5RjtRQUNGO1FBRUFHLFFBQVFwQyxHQUFHLENBQUMsNENBQWtDZTtRQUU5QyxPQUFPO1FBQ1AsSUFBSSxDQUFDTixnQkFBZ0IsR0FBRyxFQUFFO1FBQzFCLElBQUksQ0FBQ0UsZ0JBQWdCLEdBQUc7SUFDMUI7SUFFQSxXQUFXO0lBRVg7O0dBRUMsR0FDRCxlQUF1QmpCLE1BQWMsRUFBVztRQUM5QyxnQ0FBZ0M7UUFDaEMsSUFBSUEsV0FBVyxXQUFXO1lBQ3hCLE9BQU87UUFDVDtRQUVBLGlCQUFpQjtRQUNqQixNQUFNNEMsY0FBYztZQUFDO1lBQU87WUFBUTtZQUFVO1lBQVE7WUFBYztTQUFjO1FBQ2xGLE9BQU9BLFlBQVlDLElBQUksQ0FBQ0MsQ0FBQUEsYUFBYzlDLE9BQU8rQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0Y7SUFDdEU7SUFFQTs7R0FFQyxHQUNELGtCQUEwQjlDLE1BQWMsRUFBRWtELE1BQVcsRUFBVztRQUM5RCxjQUFjO1FBQ2QsT0FBTyxJQUFJLENBQUNQLGNBQWMsQ0FBQzNDLFdBQVdrRCxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFDLE1BQU0sTUFBSztJQUMzRDtJQUVBOzs7R0FHQyxHQUNELGlCQUF5QnBELE9BQWUsRUFBRUMsTUFBYyxFQUFFQyxNQUFXLEVBQVU7UUFDN0UsbURBQW1EO1FBQ25ELE1BQU1vRCxVQUFVLEdBQWNyRCxPQUFYRCxTQUFRLEtBQVUsT0FBUEM7UUFFOUIsd0JBQXdCO1FBQ3hCLElBQUksQ0FBQ0MsVUFBVyxPQUFPQSxXQUFXLFlBQVlxRCxPQUFPQyxJQUFJLENBQUN0RCxRQUFRVSxNQUFNLEtBQUssR0FBSTtZQUMvRSxPQUFPMEM7UUFDVDtRQUVBLGFBQWE7UUFDYixJQUFJRztRQUNKLElBQUksT0FBT3ZELFdBQVcsVUFBVTtZQUM5QixvQkFBb0I7WUFDcEIsTUFBTXdELGVBQWVILE9BQU9DLElBQUksQ0FBQ3RELFFBQVF5RCxJQUFJLEdBQUc1QixNQUFNLENBQUMsQ0FBQ29CLFFBQVFTO2dCQUM5RFQsTUFBTSxDQUFDUyxJQUFJLEdBQUcxRCxNQUFNLENBQUMwRCxJQUFJO2dCQUN6QixPQUFPVDtZQUNULEdBQUcsQ0FBQztZQUNKTSxXQUFXSSxLQUFLQyxTQUFTLENBQUNKO1FBQzVCLE9BQU87WUFDTEQsV0FBV00sT0FBTzdEO1FBQ3BCO1FBRUEsT0FBTyxHQUFjOEQsT0FBWFYsU0FBUSxLQUF5RCxPQUF0RFUsTUFBTUEsQ0FBQ0MsSUFBSSxDQUFDUixVQUFVUyxRQUFRLENBQUMsVUFBVXJELEtBQUssQ0FBQyxHQUFHO0lBQ3pFO0lBSUE7O0dBRUMsR0FDRCx1QkFBK0JiLE9BQWUsRUFBMkQ7UUFDdkcsSUFBSUEsUUFBUWlELFFBQVEsQ0FBQyxvQkFBb0IsT0FBTztRQUNoRCxJQUFJakQsUUFBUWlELFFBQVEsQ0FBQyx3QkFBd0IsT0FBTztRQUNwRCxJQUFJakQsUUFBUWlELFFBQVEsQ0FBQyxtQkFBbUJqRCxRQUFRaUQsUUFBUSxDQUFDLGVBQWUsT0FBTyxTQUFTLGVBQWU7O1FBQ3ZHLElBQUlqRCxRQUFRaUQsUUFBUSxDQUFDLGdCQUFnQixPQUFPO1FBQzVDLE9BQU87SUFDVDtJQUlBOztHQUVDLEdBQ0QsbUJBQTJCb0IsWUFBb0IsRUFBVTtRQUN2RCxhQUFhO1FBQ2IsTUFBTUMsYUFBYSxJQUFJLENBQUM1RCxVQUFVLENBQUNlLE1BQU0sQ0FBQ2xCLENBQUFBLE1BQ3hDVSxLQUFLUixHQUFHLEtBQUssSUFBSVEsS0FBS1YsSUFBSUMsU0FBUyxFQUFFK0QsT0FBTyxLQUFLLElBQUksS0FBSyxLQUFLLFFBQVE7O1FBR3pFLE9BQU9ELFdBQVc3QyxNQUFNLENBQUNsQixDQUFBQSxNQUFPLEdBQWtCQSxPQUFmQSxJQUFJUCxPQUFPLEVBQUMsS0FBYyxPQUFYTyxJQUFJTixNQUFNLE1BQU9vRSxjQUFjekQsTUFBTTtJQUN6RjtJQUVBOztHQUVDLEdBQ0QsY0FBc0JYLE1BQWMsRUFBRWtELE1BQVcsRUFBVztZQUV0QkEsb0JBQUFBO1FBRHBDLGVBQWU7UUFDZixPQUFPbEQsT0FBT2dELFFBQVEsQ0FBQyxhQUFhRSxDQUFBQSxtQkFBQUEsOEJBQUFBLGVBQUFBLE9BQVFzQixJQUFJLGNBQVp0QixvQ0FBQUEscUJBQUFBLGFBQWN1QixLQUFLLGNBQW5CdkIseUNBQUFBLG1CQUFxQnZDLE1BQU0sSUFBRztJQUNwRTtJQUVBLHVEQUF1RDtJQUV2RDs7R0FFQyxHQUNELGFBQXdCZ0QsR0FBVyxFQUFZO1FBQzdDLE1BQU1nQixRQUFRLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxHQUFHLENBQUNsQjtRQUM3QixJQUFJLENBQUNnQixPQUFPLE9BQU87UUFFbkIsU0FBUztRQUNULElBQUkzRCxLQUFLUixHQUFHLEtBQUttRSxNQUFNRyxTQUFTLEVBQUU7WUFDaEMsSUFBSSxDQUFDRixLQUFLLENBQUNHLE1BQU0sQ0FBQ3BCO1lBQ2xCLE9BQU87UUFDVDtRQUVBZ0IsTUFBTUssV0FBVztRQUNqQkwsTUFBTU0sWUFBWSxHQUFHakUsS0FBS1IsR0FBRztRQUM3QixPQUFPbUUsTUFBTUgsSUFBSTtJQUNuQjtJQUVBOztHQUVDLEdBQ0QsV0FBc0JiLEdBQVcsRUFBRWEsSUFBTyxFQUFFekUsT0FBZSxFQUFFQyxNQUFlLEVBQVE7UUFDbEYsZ0JBQWdCO1FBQ2hCLElBQUksSUFBSSxDQUFDNEUsS0FBSyxDQUFDekMsSUFBSSxJQUFJLElBQUksQ0FBQ2dELFdBQVcsQ0FBQ25ILE9BQU8sRUFBRTtZQUMvQyxJQUFJLENBQUNvSCxpQkFBaUI7UUFDeEI7UUFFQSxNQUFNQyxXQUFXLElBQUksQ0FBQ0YsV0FBVyxDQUFDbEgsVUFBVSxDQUFDOEIsUUFBUSxJQUFJO1lBQ3ZENUIsS0FBSyxJQUFJLENBQUNnSCxXQUFXLENBQUNwSCxVQUFVO1lBQ2hDTSxVQUFVO1FBQ1o7UUFFQSxjQUFjO1FBQ2QsTUFBTWlILFdBQVcsSUFBSSxDQUFDQyxXQUFXLENBQUN4RixTQUFTQztRQUMzQyxNQUFNM0IsV0FBV2dILFNBQVNoSCxRQUFRLElBQUk7UUFDdEMsTUFBTW1ILGdCQUFnQixJQUFJLENBQUNDLGdCQUFnQixDQUFDakI7UUFDNUMsTUFBTWtCLGdCQUFnQixJQUFJLENBQUNDLG9CQUFvQixDQUFDaEMsS0FBSzVEO1FBRXJELE1BQU00RSxRQUFvQjtZQUN4Qkg7WUFDQU0sV0FBVzlELEtBQUtSLEdBQUcsS0FBSzZFLFNBQVNsSCxHQUFHO1lBQ3BDNkcsYUFBYTtZQUNiQyxjQUFjakUsS0FBS1IsR0FBRztZQUN0Qm9GLFdBQVc1RSxLQUFLUixHQUFHO1lBQ25CLGNBQWM7WUFDZG5DO1lBQ0FpSDtZQUNBRTtZQUNBRTtZQUNBRyxhQUFhLElBQUksQ0FBQ0MsaUJBQWlCLENBQUMvRixTQUFTQztRQUMvQztRQUVBLElBQUksQ0FBQzRFLEtBQUssQ0FBQ21CLEdBQUcsQ0FBQ3BDLEtBQUtnQjtRQUVwQixjQUFjO1FBQ2QsSUFBSSxDQUFDcUIsaUJBQWlCLENBQUNyQztJQUN6QjtJQUVBOztHQUVDLEdBQ0Qsb0JBQWtDO1FBQ2hDLE1BQU1zQyxVQUFVQyxNQUFNbEMsSUFBSSxDQUFDLElBQUksQ0FBQ1ksS0FBSyxDQUFDcUIsT0FBTztRQUM3QyxNQUFNekYsTUFBTVEsS0FBS1IsR0FBRztRQUVwQixnQkFBZ0I7UUFDaEIsT0FBUSxJQUFJLENBQUMyRSxXQUFXLENBQUNyRyxhQUFhLENBQUNDLFNBQVM7WUFDOUMsS0FBSztnQkFDSCxJQUFJLENBQUNvSCxVQUFVLENBQUNGO2dCQUNoQjtZQUNGLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDRyxVQUFVLENBQUNIO2dCQUNoQjtZQUNGLEtBQUs7WUFDTDtnQkFDRSxJQUFJLENBQUNJLGFBQWEsQ0FBQ0osU0FBU3pGO2dCQUM1QjtRQUNKO1FBRUEsSUFBSSxDQUFDOEYsVUFBVSxDQUFDQyxTQUFTO0lBQzNCO0lBRUE7O0dBRUMsR0FDRCxjQUFzQk4sT0FBb0MsRUFBRXpGLEdBQVcsRUFBUTtRQUM3RSxxQkFBcUI7UUFDckIsTUFBTWdHLFNBQVNQLFFBQVEvRCxHQUFHLENBQUM7Z0JBQUMsQ0FBQ3lCLEtBQUtnQixNQUFNO1lBQ3RDLElBQUk4QixRQUFRO1lBRVosNEJBQTRCO1lBQzVCLE1BQU1DLGlCQUFpQjtnQkFBRUMsVUFBVTtnQkFBS0MsTUFBTTtnQkFBSUMsUUFBUTtnQkFBSUMsS0FBSztZQUFHO1lBQ3RFTCxTQUFTQyxjQUFjLENBQUMvQixNQUFNdEcsUUFBUSxDQUFDLElBQUk7WUFFM0MsU0FBUztZQUNULE1BQU0wSSxNQUFNLENBQUN2RyxNQUFNbUUsTUFBTWlCLFNBQVMsSUFBSyxNQUFLLEtBQUssS0FBSyxLQUFNLEtBQUs7WUFBUjtZQUN6RCxNQUFNb0IsWUFBWXJDLE1BQU1LLFdBQVcsR0FBR3BELEtBQUthLEdBQUcsQ0FBQ3NFLEtBQUs7WUFDcEROLFNBQVM3RSxLQUFLVSxHQUFHLENBQUMwRSxZQUFZLElBQUk7WUFFbEMsU0FBUztZQUNULE1BQU1DLGdCQUFnQixDQUFDekcsTUFBTW1FLE1BQU1NLFlBQVksSUFBSyxNQUFLLEtBQUssS0FBTSxLQUFLO1lBQVI7WUFDakV3QixTQUFTN0UsS0FBS2EsR0FBRyxDQUFDLEtBQUt3RSxnQkFBZ0IsR0FBRztZQUUxQyxnQkFBZ0I7WUFDaEJSLFNBQVM3RSxLQUFLVSxHQUFHLENBQUNxQyxNQUFNYSxhQUFhLEdBQUcsTUFBTTtZQUU5QyxPQUFPO2dCQUFFN0I7Z0JBQUtnQjtnQkFBTzhCO1lBQU07UUFDN0I7UUFFQSxrQkFBa0I7UUFDbEJELE9BQU85QyxJQUFJLENBQUMsQ0FBQ3dELEdBQUdDLElBQU1ELEVBQUVULEtBQUssR0FBR1UsRUFBRVYsS0FBSztRQUN2QyxNQUFNVyxjQUFjeEYsS0FBS2EsR0FBRyxDQUFDYixLQUFLeUYsS0FBSyxDQUFDcEIsUUFBUXRGLE1BQU0sR0FBRyxPQUFPO1FBRWhFLElBQUssSUFBSTJHLElBQUksR0FBR0EsSUFBSUYsZUFBZUUsSUFBSWQsT0FBTzdGLE1BQU0sRUFBRTJHLElBQUs7WUFDekQsSUFBSSxDQUFDMUMsS0FBSyxDQUFDRyxNQUFNLENBQUN5QixNQUFNLENBQUNjLEVBQUUsQ0FBQzNELEdBQUc7WUFDL0IsSUFBSSxDQUFDNEQsa0JBQWtCLENBQUN4QyxNQUFNLENBQUN5QixNQUFNLENBQUNjLEVBQUUsQ0FBQzNELEdBQUc7UUFDOUM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsV0FBbUJzQyxPQUFvQyxFQUFRO1FBQzdEQSxRQUFRdkMsSUFBSSxDQUFDLENBQUN3RCxHQUFHQyxJQUFNRCxDQUFDLENBQUMsRUFBRSxDQUFDakMsWUFBWSxHQUFHa0MsQ0FBQyxDQUFDLEVBQUUsQ0FBQ2xDLFlBQVk7UUFDNUQsTUFBTW1DLGNBQWN4RixLQUFLeUYsS0FBSyxDQUFDcEIsUUFBUXRGLE1BQU0sR0FBRztRQUVoRCxJQUFLLElBQUkyRyxJQUFJLEdBQUdBLElBQUlGLGFBQWFFLElBQUs7WUFDcEMsSUFBSSxDQUFDMUMsS0FBSyxDQUFDRyxNQUFNLENBQUNrQixPQUFPLENBQUNxQixFQUFFLENBQUMsRUFBRTtZQUMvQixJQUFJLENBQUNDLGtCQUFrQixDQUFDeEMsTUFBTSxDQUFDa0IsT0FBTyxDQUFDcUIsRUFBRSxDQUFDLEVBQUU7UUFDOUM7SUFDRjtJQUVBOztHQUVDLEdBQ0QsV0FBbUJyQixPQUFvQyxFQUFRO1FBQzdEQSxRQUFRdkMsSUFBSSxDQUFDLENBQUN3RCxHQUFHQyxJQUFNRCxDQUFDLENBQUMsRUFBRSxDQUFDbEMsV0FBVyxHQUFHbUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQ25DLFdBQVc7UUFDMUQsTUFBTW9DLGNBQWN4RixLQUFLeUYsS0FBSyxDQUFDcEIsUUFBUXRGLE1BQU0sR0FBRztRQUVoRCxJQUFLLElBQUkyRyxJQUFJLEdBQUdBLElBQUlGLGFBQWFFLElBQUs7WUFDcEMsSUFBSSxDQUFDMUMsS0FBSyxDQUFDRyxNQUFNLENBQUNrQixPQUFPLENBQUNxQixFQUFFLENBQUMsRUFBRTtZQUMvQixJQUFJLENBQUNDLGtCQUFrQixDQUFDeEMsTUFBTSxDQUFDa0IsT0FBTyxDQUFDcUIsRUFBRSxDQUFDLEVBQUU7UUFDOUM7SUFDRjtJQUVBOztHQUVDLEdBQ0QscUJBQW1DO1FBQ2pDLE1BQU1yQixVQUFVQyxNQUFNbEMsSUFBSSxDQUFDLElBQUksQ0FBQ1ksS0FBSyxDQUFDcUIsT0FBTztRQUM3Q0EsUUFBUXZDLElBQUksQ0FBQyxDQUFDd0QsR0FBR0MsSUFBTUQsQ0FBQyxDQUFDLEVBQUUsQ0FBQ2pDLFlBQVksR0FBR2tDLENBQUMsQ0FBQyxFQUFFLENBQUNsQyxZQUFZO1FBRTVELGFBQWE7UUFDYixNQUFNbUMsY0FBY3hGLEtBQUt5RixLQUFLLENBQUNwQixRQUFRdEYsTUFBTSxHQUFHO1FBQ2hELElBQUssSUFBSTJHLElBQUksR0FBR0EsSUFBSUYsYUFBYUUsSUFBSztZQUNwQyxJQUFJLENBQUMxQyxLQUFLLENBQUNHLE1BQU0sQ0FBQ2tCLE9BQU8sQ0FBQ3FCLEVBQUUsQ0FBQyxFQUFFO1FBQ2pDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWNHLGlCQUNaMUgsT0FBZSxFQUNmQyxNQUFjLEVBQ2RDLE1BQVcsRUFDWHlILFFBQTBCLEVBQ2Q7UUFDWixvQkFBb0I7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQzlILE1BQU0sQ0FBQzlCLGFBQWEsRUFBRTtZQUM5QixPQUFPNEo7UUFDVDtRQUVBLGlCQUFpQjtRQUNqQixNQUFNOUUsY0FBYztZQUFDO1lBQU87WUFBUTtZQUFVO1lBQVE7U0FBYTtRQUNuRSxNQUFNK0UsY0FBYy9FLFlBQVlDLElBQUksQ0FBQ0MsQ0FBQUEsYUFDbkM5QyxPQUFPK0MsV0FBVyxHQUFHQyxRQUFRLENBQUNGO1FBR2hDLElBQUksQ0FBQzZFLGFBQWE7WUFDaEIsT0FBT0Q7UUFDVDtRQUVBLE1BQU1FLFdBQVcsSUFBSSxDQUFDeEUsZ0JBQWdCLENBQUNyRCxTQUFTQyxRQUFRQztRQUV4RCxPQUFPO1FBQ1AsSUFBSSxJQUFJLENBQUM0SCxZQUFZLENBQUNDLEdBQUcsQ0FBQ0YsV0FBVztZQUNuQyxPQUFPLElBQUksQ0FBQ0MsWUFBWSxDQUFDaEQsR0FBRyxDQUFDK0M7UUFDL0I7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTUcsY0FBYyxJQUFJLENBQUNuRCxLQUFLLENBQUNDLEdBQUcsQ0FBQytDO1FBQ25DLElBQUlHLGVBQWUvRyxLQUFLUixHQUFHLEtBQUt1SCxZQUFZakQsU0FBUyxFQUFFO1lBQ3JELElBQUksQ0FBQ3dCLFVBQVUsQ0FBQzBCLElBQUk7WUFDcEIsY0FBYztZQUNkRCxZQUFZL0MsV0FBVztZQUN2QitDLFlBQVk5QyxZQUFZLEdBQUdqRSxLQUFLUixHQUFHO1lBQ25DLElBQUksQ0FBQ3dGLGlCQUFpQixDQUFDNEI7WUFDdkIsT0FBT0csWUFBWXZELElBQUk7UUFDekI7UUFFQSxPQUFPO1FBQ1AsTUFBTXlELGlCQUFpQlA7UUFDdkIsSUFBSSxDQUFDRyxZQUFZLENBQUM5QixHQUFHLENBQUM2QixVQUFVSztRQUVoQyxJQUFJO1lBQ0YsTUFBTS9FLFNBQVMsTUFBTStFO1lBQ3JCLElBQUksQ0FBQzNCLFVBQVUsQ0FBQzRCLE1BQU07WUFFdEIsdUNBQXVDO1lBQ3ZDLElBQUksSUFBSSxDQUFDakYsaUJBQWlCLENBQUNqRCxRQUFRa0QsU0FBUztnQkFDMUMsSUFBSSxDQUFDZ0MsVUFBVSxDQUFDMEMsVUFBVTFFLFFBQVFuRCxTQUFTQztZQUM3QztZQUVBLE9BQU9rRDtRQUNULFNBQVU7WUFDUixJQUFJLENBQUMyRSxZQUFZLENBQUM5QyxNQUFNLENBQUM2QztRQUMzQjtJQUNGO0lBRUEsMERBQTBEO0lBRTFEOztHQUVDLEdBQ0QsNEJBQTBDO1FBQ3hDbEYsUUFBUXBDLEdBQUcsQ0FBQztRQUVaLFNBQVM7UUFDVCxJQUFJLElBQUksQ0FBQzZFLFdBQVcsQ0FBQ2hHLFVBQVUsQ0FBQ2YsT0FBTyxFQUFFO1lBQ3ZDLElBQUksQ0FBQ2dLLG9CQUFvQjtRQUMzQjtRQUVBLFdBQVc7UUFDWCxJQUFJLENBQUNDLHFCQUFxQjtRQUUxQjNGLFFBQVFwQyxHQUFHLENBQUM7SUFDZDtJQUVBOztHQUVDLEdBQ0QsdUJBQXFDO1FBQ25DLElBQUksSUFBSSxDQUFDZ0ksWUFBWSxFQUFFO1lBQ3JCQyxjQUFjLElBQUksQ0FBQ0QsWUFBWTtRQUNqQztRQUVBLElBQUksQ0FBQ0EsWUFBWSxHQUFHRSxZQUFZO1lBQzlCLElBQUksQ0FBQ0Msc0JBQXNCO1FBQzdCLEdBQUcsSUFBSSxDQUFDdEQsV0FBVyxDQUFDaEcsVUFBVSxDQUFDQyxnQkFBZ0I7UUFFL0MsV0FBVztRQUNYOEIsV0FBVyxJQUFNLElBQUksQ0FBQ3VILHNCQUFzQixJQUFJLE1BQU0sUUFBUTs7SUFDaEU7SUFFQTs7R0FFQyxHQUNELE1BQWNBLHlCQUF3QztRQUNwRCxJQUFJLElBQUksQ0FBQ0MsY0FBYyxFQUFFO1lBQ3ZCaEcsUUFBUXBDLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQW9DLFFBQVFwQyxHQUFHLENBQUM7UUFFWixJQUFJO1lBQ0YsS0FBSyxNQUFNK0UsWUFBWSxJQUFJLENBQUNGLFdBQVcsQ0FBQ2hHLFVBQVUsQ0FBQ2xCLFVBQVUsQ0FBRTtnQkFDN0QsTUFBTSxJQUFJLENBQUMwSyxlQUFlLENBQUN0RDtZQUM3QjtRQUNGLEVBQUUsT0FBT2pGLE9BQU87WUFDZHNDLFFBQVF0QyxLQUFLLENBQUMsaUNBQWlDQTtRQUNqRDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjdUksZ0JBQWdCdEQsUUFBZ0IsRUFBaUI7UUFDN0QsT0FBUUE7WUFDTixLQUFLO2dCQUNILE1BQU0sSUFBSSxDQUFDdUQsbUJBQW1CO2dCQUM5QjtZQUNGLEtBQUs7Z0JBQ0gsTUFBTSxJQUFJLENBQUNDLGFBQWE7Z0JBQ3hCO1lBQ0YsS0FBSztnQkFDSCxNQUFNLElBQUksQ0FBQ0MsZUFBZTtnQkFDMUI7UUFDSjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjRixzQkFBcUM7UUFDakQsSUFBSTtZQUNGLE1BQU0sSUFBSSxDQUFDcEssWUFBWSxDQUFDdUsscUJBQXFCO1lBQzdDLElBQUksQ0FBQ3pDLFVBQVUsQ0FBQzBDLFdBQVc7WUFDM0J0RyxRQUFRcEMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPRixPQUFPO1lBQ2RzQyxRQUFRdUcsSUFBSSxDQUFDLG9DQUFvQzdJO1FBQ25EO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWN5SSxnQkFBK0I7UUFDM0MsSUFBSTtZQUNGLE1BQU0sSUFBSSxDQUFDM0ssTUFBTSxDQUFDZ0wsTUFBTSxDQUFDO2dCQUFFQyxPQUFPO1lBQUcsR0FBRyxVQUFVOztZQUNsRCxJQUFJLENBQUM3QyxVQUFVLENBQUMwQyxXQUFXO1lBQzNCdEcsUUFBUXBDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT0YsT0FBTztZQUNkc0MsUUFBUXVHLElBQUksQ0FBQyxvQ0FBb0M3STtRQUNuRDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFjMEksa0JBQWlDO1FBQzdDLElBQUk7WUFDRixNQUFNLElBQUksQ0FBQ3ZLLFFBQVEsQ0FBQzZLLFNBQVM7WUFDN0IsSUFBSSxDQUFDOUMsVUFBVSxDQUFDMEMsV0FBVztZQUMzQnRHLFFBQVFwQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9GLE9BQU87WUFDZHNDLFFBQVF1RyxJQUFJLENBQUMsb0NBQW9DN0k7UUFDbkQ7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsd0JBQXNDO1FBQ3BDb0ksWUFBWTtZQUNWLElBQUksQ0FBQ2EsbUJBQW1CO1FBQzFCLEdBQUcsT0FBTyxXQUFXOztJQUN2QjtJQUVBOztHQUVDLEdBQ0Qsc0JBQW9DO1FBQ2xDLE1BQU03SSxNQUFNUSxLQUFLUixHQUFHO1FBQ3BCLElBQUlBLE1BQU0sSUFBSSxDQUFDOEksZUFBZSxHQUFHLE9BQU8sUUFBTyxZQUFZO1FBRTNELElBQUksQ0FBQ0EsZUFBZSxHQUFHOUk7UUFFdkIsSUFBSTtZQUNGLG9CQUFvQjtZQUNwQixNQUFNK0ksbUJBQW1CLElBQUksQ0FBQzNFLEtBQUssQ0FBQ3pDLElBQUksR0FBRyxJQUFJLENBQUNnRCxXQUFXLENBQUNuSCxPQUFPO1lBRW5FLElBQUl1TCxtQkFBbUIsSUFBSSxDQUFDcEUsV0FBVyxDQUFDckcsYUFBYSxDQUFDRSxVQUFVLENBQUNFLGNBQWMsRUFBRTtnQkFDL0UsSUFBSSxDQUFDd0osY0FBYyxHQUFHO2dCQUN0QixJQUFJLENBQUNjLDRCQUE0QjtZQUNuQyxPQUFPLElBQUlELG1CQUFtQixJQUFJLENBQUNwRSxXQUFXLENBQUNyRyxhQUFhLENBQUNFLFVBQVUsQ0FBQ0MsYUFBYSxFQUFFO2dCQUNyRixJQUFJLENBQUN5SixjQUFjLEdBQUc7Z0JBQ3RCLElBQUksQ0FBQ2UsbUJBQW1CO1lBQzFCLE9BQU87Z0JBQ0wsSUFBSSxDQUFDZixjQUFjLEdBQUc7WUFDeEI7UUFDRixFQUFFLE9BQU90SSxPQUFPO1lBQ2RzQyxRQUFRdUcsSUFBSSxDQUFDLG9DQUFvQzdJO1FBQ25EO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELCtCQUE2QztRQUMzQ3NDLFFBQVF1RyxJQUFJLENBQUM7UUFFYixTQUFTO1FBQ1QsSUFBSSxDQUFDN0QsaUJBQWlCO1FBRXRCLE9BQU87UUFDUCxJQUFJLElBQUksQ0FBQ2tELFlBQVksRUFBRTtZQUNyQkMsY0FBYyxJQUFJLENBQUNELFlBQVk7WUFDL0IsSUFBSSxDQUFDQSxZQUFZLEdBQUc7UUFDdEI7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsc0JBQW9DO1FBQ2xDNUYsUUFBUXVHLElBQUksQ0FBQztRQUViLFdBQVc7UUFDWCxJQUFJLENBQUNTLHVCQUF1QjtJQUM5QjtJQUVBOztHQUVDLEdBQ0QsMEJBQXdDO1FBQ3RDLE1BQU16RCxVQUFVQyxNQUFNbEMsSUFBSSxDQUFDLElBQUksQ0FBQ1ksS0FBSyxDQUFDcUIsT0FBTztRQUM3QyxJQUFJMEQsZUFBZTtRQUVuQixLQUFLLE1BQU0sQ0FBQ2hHLEtBQUtnQixNQUFNLElBQUlzQixRQUFTO1lBQ2xDLElBQUl0QixNQUFNdEcsUUFBUSxLQUFLLFNBQVMyQyxLQUFLUixHQUFHLEtBQUttRSxNQUFNRyxTQUFTLEdBQUcsT0FBTztnQkFDcEUsSUFBSSxDQUFDRixLQUFLLENBQUNHLE1BQU0sQ0FBQ3BCO2dCQUNsQixJQUFJLENBQUM0RCxrQkFBa0IsQ0FBQ3hDLE1BQU0sQ0FBQ3BCO2dCQUMvQmdHO1lBQ0Y7UUFDRjtRQUVBakgsUUFBUXBDLEdBQUcsQ0FBQyx1Q0FBMEMsT0FBYnFKLGNBQWE7SUFDeEQ7SUFFQTs7R0FFQyxHQUNELFlBQW9CNUosT0FBZSxFQUFFQyxNQUFlLEVBQVU7UUFDNUQsSUFBSSxDQUFDQSxRQUFRLE9BQU9EO1FBRXBCLElBQUlDLE9BQU9nRCxRQUFRLENBQUMsaUJBQWlCaEQsT0FBT2dELFFBQVEsQ0FBQyxnQkFBZ0IsT0FBTztRQUM1RSxJQUFJaEQsT0FBT2dELFFBQVEsQ0FBQyxXQUFXaEQsT0FBT2dELFFBQVEsQ0FBQyxXQUFXLE9BQU87UUFDakUsSUFBSWhELE9BQU9nRCxRQUFRLENBQUMsY0FBY2hELE9BQU9nRCxRQUFRLENBQUMsVUFBVSxPQUFPO1FBRW5FLE9BQU9qRDtJQUNUO0lBRUE7O0dBRUMsR0FDRCxxQkFBNkI0RCxHQUFXLEVBQUU1RCxPQUFlLEVBQXNDO1FBQzdGLE1BQU02SixVQUFVLElBQUksQ0FBQ3JDLGtCQUFrQixDQUFDMUMsR0FBRyxDQUFDbEIsUUFBUSxFQUFFO1FBRXRELElBQUlpRyxRQUFRakosTUFBTSxHQUFHLEdBQUc7WUFDdEIsV0FBVztZQUNYLElBQUlaLFlBQVksa0JBQWtCQSxZQUFZLGNBQWMsT0FBTztZQUNuRSxJQUFJQSxZQUFZLFlBQVlBLFlBQVksWUFBWSxPQUFPO1lBQzNELE9BQU87UUFDVDtRQUVBLFdBQVc7UUFDWCxNQUFNOEosaUJBQWlCRCxRQUFRcEksTUFBTSxDQUFDc0ksQ0FBQUEsT0FBUTlJLEtBQUtSLEdBQUcsS0FBS3NKLE9BQU8sS0FBSyxLQUFLLE1BQU1uSixNQUFNLENBQUMsT0FBTzs7UUFDaEcsSUFBSWtKLGlCQUFpQixJQUFJLE9BQU87UUFDaEMsSUFBSUEsaUJBQWlCLEdBQUcsT0FBTztRQUMvQixPQUFPO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELGlCQUF5QnJGLElBQVMsRUFBVTtRQUMxQyxJQUFJO1lBQ0YsTUFBTXVGLGFBQWFuRyxLQUFLQyxTQUFTLENBQUNXO1lBQ2xDLE9BQU91RixXQUFXcEosTUFBTSxHQUFHLEVBQUUsbUJBQW1COztRQUNsRCxFQUFFLFVBQU07WUFDTixPQUFPLEtBQUssT0FBTzs7UUFDckI7SUFDRjtJQUVBOztHQUVDLEdBQ0Qsa0JBQTBCWixPQUFlLEVBQUVDLE1BQWUsRUFBVztRQUNuRSxrQkFBa0I7UUFDbEIsSUFBSUEsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRZ0QsUUFBUSxDQUFDLG1CQUFpQmhELG1CQUFBQSw2QkFBQUEsT0FBUWdELFFBQVEsQ0FBQyxlQUFhaEQsbUJBQUFBLDZCQUFBQSxPQUFRZ0QsUUFBUSxDQUFDLGlCQUFnQjtZQUNuRyxPQUFPO1FBQ1Q7UUFFQSxhQUFhO1FBQ2IsSUFBSWpELFlBQVksZ0JBQWdCLE9BQU87UUFFdkMsT0FBTztJQUNUO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEI0RCxHQUFXLEVBQVE7UUFDM0MsTUFBTWlHLFVBQVUsSUFBSSxDQUFDckMsa0JBQWtCLENBQUMxQyxHQUFHLENBQUNsQixRQUFRLEVBQUU7UUFDdERpRyxRQUFRbEosSUFBSSxDQUFDTSxLQUFLUixHQUFHO1FBRXJCLGdCQUFnQjtRQUNoQixJQUFJb0osUUFBUWpKLE1BQU0sR0FBRyxLQUFLO1lBQ3hCaUosUUFBUUksTUFBTSxDQUFDLEdBQUdKLFFBQVFqSixNQUFNLEdBQUc7UUFDckM7UUFFQSxJQUFJLENBQUM0RyxrQkFBa0IsQ0FBQ3hCLEdBQUcsQ0FBQ3BDLEtBQUtpRztJQUNuQztJQUVBOztHQUVDLEdBQ0QsWUFBb0I3SixPQUFlLEVBQUVDLE1BQWMsRUFBVTtRQUMzRCxvQkFBb0I7UUFDcEIsTUFBTWtLLGdCQUEyQztZQUMvQ2hNLFFBQVEsSUFBSSxLQUFLO1lBQ2pCSyxVQUFVLEtBQUssS0FBSztZQUNwQkMsY0FBYyxLQUFLO1lBQ25CQyxZQUFZLEtBQUs7WUFDakJDLFdBQVcsS0FBSyxLQUFLO1lBQ3JCQyxXQUFXLEtBQUssS0FBSyxLQUFPLGFBQWE7UUFDM0M7UUFFQSxPQUFPdUwsYUFBYSxDQUFDbkssUUFBUSxJQUFJLElBQUksQ0FBQ0gsTUFBTSxDQUFDN0IsVUFBVSxJQUFJLElBQUksS0FBSyxLQUFLLFFBQVE7O0lBQ25GO0lBRUE7O0dBRUMsR0FDRCxrQ0FBZ0Q7UUFDOUMsSUFBSSxDQUFDcU0sa0JBQWtCLEdBQUc7WUFDeEJDLFlBQVk7WUFDWkMsY0FBYztZQUNkQyxZQUFZO1lBQ1pDLHFCQUFxQjtZQUNyQkMsaUJBQWlCQztZQUNqQkMsaUJBQWlCO1lBQ2pCQyxjQUFjO1lBQ2RDLGFBQWEsRUFBRTtZQUNmQyxhQUFhLElBQUlDO1lBQ2pCQyxhQUFhLEVBQUU7UUFDakI7UUFFQSxXQUFXO1FBQ1gsSUFBSSxDQUFDQywyQkFBMkI7SUFDbEM7SUFFQTs7R0FFQyxHQUNELDhCQUE0QztRQUMxQyxpQkFBaUI7UUFDakJ6QyxZQUFZO1lBQ1YsSUFBSSxDQUFDMEMsc0JBQXNCO1FBQzdCLEdBQUcsS0FBSyxLQUFLLE1BQU0sTUFBTTs7SUFDM0I7SUFFQTs7R0FFQyxHQUNELHlCQUF1QztRQUNyQyxNQUFNMUssTUFBTVEsS0FBS1IsR0FBRztRQUNwQixNQUFNMkssYUFBYTNLLE1BQU0sS0FBSyxLQUFLO1FBQ25DLE1BQU00SyxZQUFZNUssTUFBTSxLQUFLLEtBQUssS0FBSztRQUV2QyxrQkFBa0I7UUFDbEIsSUFBSSxDQUFDNkssV0FBVyxHQUFHLElBQUksQ0FBQ0EsV0FBVyxDQUFDN0osTUFBTSxDQUFDOEosQ0FBQUEsT0FBUUEsS0FBSy9LLFNBQVMsR0FBRzRLO1FBRXBFLHVCQUF1QjtRQUN2QixJQUFJLENBQUNmLGtCQUFrQixDQUFDUyxXQUFXLEdBQUcsSUFBSSxDQUFDVCxrQkFBa0IsQ0FBQ1MsV0FBVyxDQUN0RXJKLE1BQU0sQ0FBQytKLENBQUFBLFFBQVNBLE1BQU1oTCxTQUFTLEdBQUc2SyxXQUNsQ3hLLEtBQUssQ0FBQyxDQUFDO1FBRVYsU0FBUztRQUNULElBQUksQ0FBQzRLLGlCQUFpQjtJQUN4QjtJQUVBOztHQUVDLEdBQ0Qsb0JBQWtDO1FBQ2hDLE1BQU1oTCxNQUFNLElBQUlRO1FBQ2hCLE1BQU15SyxjQUFjLEdBQXdCM0gsT0FBckJ0RCxJQUFJa0wsV0FBVyxJQUFHLEtBQWtENUgsT0FBL0NBLE9BQU90RCxJQUFJbUwsUUFBUSxLQUFLLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLE1BQUssS0FBNkM5SCxPQUExQ0EsT0FBT3RELElBQUlxTCxPQUFPLElBQUlELFFBQVEsQ0FBQyxHQUFHLE1BQUssS0FBMkMsT0FBeEM5SCxPQUFPdEQsSUFBSXNMLFFBQVEsSUFBSUYsUUFBUSxDQUFDLEdBQUcsTUFBSztRQUU3SyxNQUFNVCxhQUFhbkssS0FBS1IsR0FBRyxLQUFLLEtBQUssS0FBSztRQUMxQyxNQUFNdUwsWUFBWSxJQUFJLENBQUNWLFdBQVcsQ0FBQzdKLE1BQU0sQ0FBQzhKLENBQUFBLE9BQVFBLEtBQUsvSyxTQUFTLEdBQUc0SztRQUVuRSxJQUFJWSxVQUFVcEwsTUFBTSxHQUFHLEdBQUc7WUFDeEIsTUFBTXFMLFlBQVlELFVBQVVqSyxNQUFNLENBQUMsQ0FBQ0MsS0FBS3VKLE9BQVN2SixNQUFNdUosS0FBS3BMLFFBQVEsRUFBRTtZQUN2RSxNQUFNK0wsYUFBYUYsVUFBVXZLLE1BQU0sQ0FBQzhKLENBQUFBLE9BQVFBLEtBQUtuSSxNQUFNLEtBQUssU0FBU3hDLE1BQU07WUFFM0UsTUFBTXVMLFdBQVc7Z0JBQ2ZDLE1BQU1WO2dCQUNOVyxPQUFPTCxVQUFVcEwsTUFBTTtnQkFDdkJnQixhQUFhcUssWUFBWUQsVUFBVXBMLE1BQU07Z0JBQ3pDMEwsV0FBV0osYUFBYUYsVUFBVXBMLE1BQU07WUFDMUM7WUFFQSxlQUFlO1lBQ2YsTUFBTTJMLGdCQUFnQixJQUFJLENBQUNsQyxrQkFBa0IsQ0FBQ1ksV0FBVyxDQUFDdUIsU0FBUyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLTCxJQUFJLEtBQUtWO1lBQzFGLElBQUlhLGlCQUFpQixHQUFHO2dCQUN0QixJQUFJLENBQUNsQyxrQkFBa0IsQ0FBQ1ksV0FBVyxDQUFDc0IsY0FBYyxHQUFHSjtZQUN2RCxPQUFPO2dCQUNMLElBQUksQ0FBQzlCLGtCQUFrQixDQUFDWSxXQUFXLENBQUN0SyxJQUFJLENBQUN3TDtZQUMzQztZQUVBLGNBQWM7WUFDZCxJQUFJLENBQUM5QixrQkFBa0IsQ0FBQ1ksV0FBVyxHQUFHLElBQUksQ0FBQ1osa0JBQWtCLENBQUNZLFdBQVcsQ0FBQ3BLLEtBQUssQ0FBQyxDQUFDO1FBQ25GO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELHlCQUNFd0QsWUFBb0IsRUFDcEJsRSxRQUFnQixFQUNoQkMsT0FBZ0IsRUFFVjtZQUROdU0sU0FBQUEsaUVBQWtCO1FBRWxCLFNBQVM7UUFDVCxJQUFJLENBQUN0QyxrQkFBa0IsQ0FBQ0MsVUFBVTtRQUNsQyxJQUFJbEssU0FBUztZQUNYLElBQUksQ0FBQ2lLLGtCQUFrQixDQUFDRSxZQUFZO1FBQ3RDLE9BQU87WUFDTCxJQUFJLENBQUNGLGtCQUFrQixDQUFDRyxVQUFVO1FBQ3BDO1FBRUEsV0FBVztRQUNYLElBQUksQ0FBQ0gsa0JBQWtCLENBQUNLLGVBQWUsR0FBRzdJLEtBQUtVLEdBQUcsQ0FBQyxJQUFJLENBQUM4SCxrQkFBa0IsQ0FBQ0ssZUFBZSxFQUFFdks7UUFDNUYsSUFBSSxDQUFDa0ssa0JBQWtCLENBQUNPLGVBQWUsR0FBRy9JLEtBQUthLEdBQUcsQ0FBQyxJQUFJLENBQUMySCxrQkFBa0IsQ0FBQ08sZUFBZSxFQUFFeks7UUFFNUYsV0FBVztRQUNYLE1BQU04TCxZQUFZLElBQUksQ0FBQzVCLGtCQUFrQixDQUFDSSxtQkFBbUIsR0FBSSxLQUFJLENBQUNKLGtCQUFrQixDQUFDQyxVQUFVLEdBQUcsS0FBS25LO1FBQzNHLElBQUksQ0FBQ2tLLGtCQUFrQixDQUFDSSxtQkFBbUIsR0FBR3dCLFlBQVksSUFBSSxDQUFDNUIsa0JBQWtCLENBQUNDLFVBQVU7UUFFNUYsVUFBVTtRQUNWLElBQUlxQyxRQUFRO1lBQ1YsTUFBTWhMLFlBQVksSUFBSSxDQUFDMEksa0JBQWtCLENBQUNDLFVBQVUsR0FBRyxJQUFJLENBQUNELGtCQUFrQixDQUFDUSxZQUFZLEdBQUc7WUFDOUYsSUFBSSxDQUFDUixrQkFBa0IsQ0FBQ1EsWUFBWSxHQUFHbEosWUFBWSxJQUFJLENBQUMwSSxrQkFBa0IsQ0FBQ0MsVUFBVTtRQUN2RixPQUFPO1lBQ0wsTUFBTTNJLFlBQVksSUFBSSxDQUFDMEksa0JBQWtCLENBQUNDLFVBQVUsR0FBRyxJQUFJLENBQUNELGtCQUFrQixDQUFDUSxZQUFZO1lBQzNGLElBQUksQ0FBQ1Isa0JBQWtCLENBQUNRLFlBQVksR0FBR2xKLFlBQVksSUFBSSxDQUFDMEksa0JBQWtCLENBQUNDLFVBQVU7UUFDdkY7UUFFQSxTQUFTO1FBQ1QsTUFBTXNDLGFBQWEsSUFBSSxDQUFDN0IsV0FBVyxDQUFDakcsR0FBRyxDQUFDVCxpQkFBaUI7WUFDdkRnSSxPQUFPO1lBQ1BKLFdBQVc7WUFDWHJLLGFBQWE7WUFDYmlMLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxRQUFRO1FBQ1Y7UUFFQUgsV0FBV1AsS0FBSztRQUNoQk8sV0FBV1gsU0FBUyxJQUFJOUw7UUFDeEJ5TSxXQUFXaEwsV0FBVyxHQUFHZ0wsV0FBV1gsU0FBUyxHQUFHVyxXQUFXUCxLQUFLO1FBQ2hFTyxXQUFXRSxRQUFRLEdBQUc3TCxLQUFLUixHQUFHO1FBRTlCLElBQUlMLFNBQVM7WUFDWHdNLFdBQVdDLFdBQVcsR0FBRyxDQUFDRCxXQUFXQyxXQUFXLEdBQUlELENBQUFBLFdBQVdQLEtBQUssR0FBRyxLQUFLLEtBQUtPLFdBQVdQLEtBQUs7UUFDbkcsT0FBTztZQUNMTyxXQUFXRyxNQUFNO1lBQ2pCSCxXQUFXQyxXQUFXLEdBQUcsV0FBWUEsV0FBVyxHQUFJRCxDQUFBQSxXQUFXUCxLQUFLLEdBQUcsS0FBTU8sV0FBV1AsS0FBSztRQUMvRjtRQUVBLElBQUksQ0FBQ3RCLFdBQVcsQ0FBQy9FLEdBQUcsQ0FBQzNCLGNBQWN1STtRQUVuQyxRQUFRO1FBQ1IsSUFBSXpNLFdBQVcsTUFBTTtZQUNuQixJQUFJLENBQUNrSyxrQkFBa0IsQ0FBQ1MsV0FBVyxDQUFDbkssSUFBSSxDQUFDO2dCQUN2Q1YsUUFBUW9FO2dCQUNSbkUsUUFBUSxDQUFDO2dCQUNUQztnQkFDQUssV0FBV1MsS0FBS1IsR0FBRztnQkFDbkJrTTtZQUNGO1lBRUEsY0FBYztZQUNkLElBQUksSUFBSSxDQUFDdEMsa0JBQWtCLENBQUNTLFdBQVcsQ0FBQ2xLLE1BQU0sR0FBRyxLQUFLO2dCQUNwRCxJQUFJLENBQUN5SixrQkFBa0IsQ0FBQ1MsV0FBVyxHQUFHLElBQUksQ0FBQ1Qsa0JBQWtCLENBQUNTLFdBQVcsQ0FBQ2pLLEtBQUssQ0FBQyxDQUFDO1lBQ25GO1FBQ0Y7UUFFQSxTQUFTO1FBQ1QsSUFBSSxDQUFDeUssV0FBVyxDQUFDM0ssSUFBSSxDQUFDO1lBQ3BCVixRQUFRb0U7WUFDUmxFO1lBQ0FpRCxRQUFRaEQsVUFBVSxZQUFZO1lBQzlCSSxXQUFXUyxLQUFLUixHQUFHO1lBQ25Ca007UUFDRjtRQUVBLGVBQWU7UUFDZixJQUFJLElBQUksQ0FBQ3JCLFdBQVcsQ0FBQzFLLE1BQU0sR0FBRyxNQUFNO1lBQ2xDLElBQUksQ0FBQzBLLFdBQVcsR0FBRyxJQUFJLENBQUNBLFdBQVcsQ0FBQ3pLLEtBQUssQ0FBQyxDQUFDO1FBQzdDO0lBQ0Y7SUFFQSxzQ0FBc0M7SUFFdEM7O0dBRUMsR0FDRCxNQUFjbU0sbUJBQ1poTixPQUFlLEVBQ2ZDLE1BQWMsRUFDZEMsTUFBVyxFQUNYeUgsUUFBMEIsRUFDZDtRQUNaLE1BQU1zRixZQUFZaE0sS0FBS1IsR0FBRztRQUMxQixNQUFNNEQsZUFBZSxHQUFjcEUsT0FBWEQsU0FBUSxLQUFVLE9BQVBDO1FBRW5DLElBQUk7WUFDRixXQUFXO1lBQ1gsTUFBTWtELFNBQVMsTUFBTSxJQUFJLENBQUN1RSxnQkFBZ0IsQ0FBQzFILFNBQVNDLFFBQVFDLFFBQVF5SDtZQUVwRSxNQUFNeEgsV0FBV2MsS0FBS1IsR0FBRyxLQUFLd007WUFFOUIsYUFBYTtZQUNiLElBQUksQ0FBQ1Asd0JBQXdCLENBQUNySSxjQUFjbEUsVUFBVSxNQUFNO1lBRTVELElBQUksQ0FBQ0osU0FBUyxDQUFDQyxTQUFTQyxRQUFRQyxRQUFRQyxVQUFVO1lBQ2xEekMsd0ZBQTRCQSxDQUFDd1AsYUFBYSxDQUFDN0ksY0FBY2xFLFVBQVUsTUFBTUQ7WUFFekUsT0FBT2lEO1FBQ1QsRUFBRSxPQUFPOUMsT0FBTztZQUNkLE1BQU1GLFdBQVdjLEtBQUtSLEdBQUcsS0FBS3dNO1lBQzlCLE1BQU1FLGVBQWU5TSxpQkFBaUIrTSxRQUFRL00sTUFBTWdOLE9BQU8sR0FBR3RKLE9BQU8xRDtZQUVyRSxhQUFhO1lBQ2IsSUFBSSxDQUFDcU0sd0JBQXdCLENBQUNySSxjQUFjbEUsVUFBVSxPQUFPO1lBRTdELElBQUksQ0FBQ0osU0FBUyxDQUFDQyxTQUFTQyxRQUFRQyxRQUFRQyxVQUFVLE9BQU9nTjtZQUN6RHpQLHdGQUE0QkEsQ0FBQ3dQLGFBQWEsQ0FBQzdJLGNBQWNsRSxVQUFVLE9BQU9EO1lBRTFFLE1BQU1HO1FBQ1I7SUFDRjtJQUVBLHdDQUF3QztJQUV4Qyx1Q0FBdUM7SUFFdkMsOENBQThDO0lBRTlDLHVCQUF1QjtJQUV2QixtREFBbUQ7SUFFbkQ7O0dBRUMsR0FDRCxJQUFJN0IsV0FBVztRQUNiLE9BQU87WUFDTDJLLFFBQVEsQ0FBQ2pKLFNBQWlCLElBQUksQ0FBQzhNLGtCQUFrQixDQUMvQyxrQkFBa0IsZUFBZTlNLFFBQ2pDLElBQU0sSUFBSSxDQUFDb04sY0FBYyxDQUFDQyxXQUFXLENBQUNyTjtZQUd4Q3NOLFNBQVMsQ0FBQ0MsS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM5QyxrQkFBa0Isa0JBQWtCO29CQUFFUztnQkFBRyxHQUN6QyxJQUFNLElBQUksQ0FBQ0gsY0FBYyxDQUFDSSxjQUFjLENBQUNEO1lBRzNDRSxXQUFXLENBQUNDLE9BQWlCLElBQUksQ0FBQ1osa0JBQWtCLENBQ2xELGtCQUFrQixvQkFBb0I7b0JBQUVZO2dCQUFLLEdBQzdDLElBQU0sSUFBSSxDQUFDTixjQUFjLENBQUNPLGdCQUFnQixDQUFDRDtZQUc3Q0UsUUFBUSxDQUFDckosT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDNUMsa0JBQWtCLGlCQUFpQnZJLE1BQ25DLElBQU0sSUFBSSxDQUFDNkksY0FBYyxDQUFDUyxhQUFhLENBQUN0SjtZQUcxQ3VKLFFBQVEsQ0FBQ1AsSUFBWVEsVUFBaUIsSUFBSSxDQUFDakIsa0JBQWtCLENBQzNELGtCQUFrQixpQkFBaUI7b0JBQUVTO29CQUFJUTtnQkFBUSxHQUNqRCxJQUFNLElBQUksQ0FBQ1gsY0FBYyxDQUFDWSxhQUFhLENBQUNULElBQUlRO1lBRzlDakosUUFBUSxDQUFDeUksS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM3QyxrQkFBa0IsaUJBQWlCO29CQUFFUztnQkFBRyxHQUN4QyxJQUFNLElBQUksQ0FBQ0gsY0FBYyxDQUFDYSxhQUFhLENBQUNWO1lBRzFDcEUsV0FBVyxJQUFNLElBQUksQ0FBQzJELGtCQUFrQixDQUN0QyxrQkFBa0IscUJBQXFCLENBQUMsR0FDeEMsSUFBTSxJQUFJLENBQUNNLGNBQWMsQ0FBQ2MsaUJBQWlCO1lBRzdDQyxlQUFlLENBQUNDLFdBQXFCLElBQUksQ0FBQ3RCLGtCQUFrQixDQUMxRCxrQkFBa0IseUJBQXlCO29CQUFFc0I7Z0JBQVMsR0FDdEQsSUFBTSxJQUFJLENBQUNoQixjQUFjLENBQUNpQixxQkFBcUIsQ0FBQ0Q7WUFHbERFLFFBQVEsQ0FBQ0MsVUFBb0IsSUFBSSxDQUFDekIsa0JBQWtCLENBQ2xELGtCQUFrQixrQkFBa0I7b0JBQUV5QjtnQkFBUSxHQUM5QyxJQUFNLElBQUksQ0FBQ25CLGNBQWMsQ0FBQ29CLGNBQWMsQ0FBQ0Q7WUFHM0NFLFdBQVcsQ0FBQ0MsU0FBbUIsSUFBSSxDQUFDNUIsa0JBQWtCLENBQ3BELGtCQUFrQixxQkFBcUI7b0JBQUU0QjtnQkFBTyxHQUNoRCxJQUFNLElBQUksQ0FBQ3RCLGNBQWMsQ0FBQ3VCLGlCQUFpQixDQUFDRDtZQUc5Q0UsY0FBYyxJQUFNLElBQUksQ0FBQzlCLGtCQUFrQixDQUN6QyxrQkFBa0IsMEJBQTBCLENBQUMsR0FDN0MsSUFBTSxJQUFJLENBQUNNLGNBQWMsQ0FBQ3lCLHNCQUFzQjtRQUVwRDtJQUNGO0lBRUEsbURBQW1EO0lBRW5EOztHQUVDLEdBQ0QsSUFBSXBRLFlBQVk7UUFDZCxPQUFPO1lBQ0x3SyxRQUFRLENBQUNqSixTQUFpQixJQUFJLENBQUM4TSxrQkFBa0IsQ0FDL0MsbUJBQW1CLGdCQUFnQjlNLFFBQ25DLElBQU0sSUFBSSxDQUFDOE8sZUFBZSxDQUFDQyxZQUFZLENBQUMvTztZQUcxQ3NOLFNBQVMsQ0FBQ0MsS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM5QyxtQkFBbUIsbUJBQW1CO29CQUFFUztnQkFBRyxHQUMzQyxJQUFNLElBQUksQ0FBQ3VCLGVBQWUsQ0FBQ0UsZUFBZSxDQUFDekI7WUFHN0NLLFFBQVEsQ0FBQ3JKLE9BQWMsSUFBSSxDQUFDdUksa0JBQWtCLENBQzVDLG1CQUFtQixrQkFBa0J2SSxNQUNyQyxJQUFNLElBQUksQ0FBQ3VLLGVBQWUsQ0FBQ0csY0FBYyxDQUFDMUs7WUFHNUN1SixRQUFRLENBQUNQLElBQVlRLFVBQWlCLElBQUksQ0FBQ2pCLGtCQUFrQixDQUMzRCxtQkFBbUIsa0JBQWtCO29CQUFFUztvQkFBSVE7Z0JBQVEsR0FDbkQsSUFBTSxJQUFJLENBQUNlLGVBQWUsQ0FBQ0ksY0FBYyxDQUFDM0IsSUFBSVE7WUFHaERqSixRQUFRLENBQUN5SSxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzdDLG1CQUFtQixrQkFBa0I7b0JBQUVTO2dCQUFHLEdBQzFDLElBQU0sSUFBSSxDQUFDdUIsZUFBZSxDQUFDSyxjQUFjLENBQUM1QjtZQUc1Q3BFLFdBQVcsSUFBTSxJQUFJLENBQUMyRCxrQkFBa0IsQ0FDdEMsbUJBQW1CLHNCQUFzQixDQUFDLEdBQzFDLElBQU0sSUFBSSxDQUFDZ0MsZUFBZSxDQUFDTSxrQkFBa0I7WUFHL0NkLFFBQVEsQ0FBQ0MsVUFBb0IsSUFBSSxDQUFDekIsa0JBQWtCLENBQ2xELG1CQUFtQixtQkFBbUI7b0JBQUV5QjtnQkFBUSxHQUNoRCxJQUFNLElBQUksQ0FBQ08sZUFBZSxDQUFDTyxlQUFlLENBQUNkO1FBRS9DO0lBQ0Y7SUFFQSxtREFBbUQ7SUFFbkQ7O0dBRUMsR0FDRCxJQUFJN1AsWUFBWTtRQUNkLE9BQU87WUFDTHVLLFFBQVEsQ0FBQ2pKLFNBQWlCLElBQUksQ0FBQzhNLGtCQUFrQixDQUMvQyxtQkFBbUIsZ0JBQWdCOU0sUUFDbkMsSUFBTSxJQUFJLENBQUNzUCxlQUFlLENBQUNDLFlBQVksQ0FBQ3ZQO1lBRzFDc04sU0FBUyxDQUFDQyxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzlDLG1CQUFtQixtQkFBbUI7b0JBQUVTO2dCQUFHLEdBQzNDLElBQU0sSUFBSSxDQUFDK0IsZUFBZSxDQUFDRSxlQUFlLENBQUNqQztZQUc3Q0ssUUFBUSxDQUFDckosT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDNUMsbUJBQW1CLGtCQUFrQnZJLE1BQ3JDLElBQU0sSUFBSSxDQUFDK0ssZUFBZSxDQUFDRyxjQUFjLENBQUNsTDtZQUc1Q3VKLFFBQVEsQ0FBQ1AsSUFBWVEsVUFBaUIsSUFBSSxDQUFDakIsa0JBQWtCLENBQzNELG1CQUFtQixrQkFBa0I7b0JBQUVTO29CQUFJUTtnQkFBUSxHQUNuRCxJQUFNLElBQUksQ0FBQ3VCLGVBQWUsQ0FBQ0ksY0FBYyxDQUFDbkMsSUFBSVE7WUFHaERqSixRQUFRLENBQUN5SSxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzdDLG1CQUFtQixrQkFBa0I7b0JBQUVTO2dCQUFHLEdBQzFDLElBQU0sSUFBSSxDQUFDK0IsZUFBZSxDQUFDSyxjQUFjLENBQUNwQztZQUc1Q3BFLFdBQVcsSUFBTSxJQUFJLENBQUMyRCxrQkFBa0IsQ0FDdEMsbUJBQW1CLHNCQUFzQixDQUFDLEdBQzFDLElBQU0sSUFBSSxDQUFDd0MsZUFBZSxDQUFDTSxrQkFBa0I7WUFHL0NDLGlCQUFpQixDQUFDQyxhQUF1QixJQUFJLENBQUNoRCxrQkFBa0IsQ0FDOUQsbUJBQW1CLDRCQUE0QjtvQkFBRWdEO2dCQUFXLEdBQzVELElBQU0sSUFBSSxDQUFDUixlQUFlLENBQUNTLHdCQUF3QixDQUFDRDtZQUd0REUsV0FBVyxDQUFDQyxPQUFpQixJQUFJLENBQUNuRCxrQkFBa0IsQ0FDbEQsbUJBQW1CLHNCQUFzQjtvQkFBRW1EO2dCQUFLLEdBQ2hELElBQU0sSUFBSSxDQUFDWCxlQUFlLENBQUNZLGtCQUFrQixDQUFDRDtZQUdoREUsVUFBVSxJQUFNLElBQUksQ0FBQ3JELGtCQUFrQixDQUNyQyxtQkFBbUIscUJBQXFCLENBQUMsR0FDekMsSUFBTSxJQUFJLENBQUN3QyxlQUFlLENBQUNjLGlCQUFpQjtRQUVoRDtJQUNGO0lBRUEsbURBQW1EO0lBRW5EOztHQUVDLEdBQ0QsSUFBSUMsWUFBWTtRQUNkLE9BQU87WUFDTHBILFFBQVEsQ0FBQ2pKLFNBQWlCLElBQUksQ0FBQzhNLGtCQUFrQixDQUMvQyxvQkFBb0IsdUJBQXVCOU0sUUFDM0MsSUFBTSxJQUFJLENBQUNzUSxnQkFBZ0IsQ0FBQ0MsbUJBQW1CLENBQUN2UTtZQUdsRHdRLGtCQUFrQixDQUFDQyxjQUF3QixJQUFJLENBQUMzRCxrQkFBa0IsQ0FDaEUsb0JBQW9CLDZCQUE2QjtvQkFBRTJEO2dCQUFZLEdBQy9ELElBQU0sSUFBSSxDQUFDSCxnQkFBZ0IsQ0FBQ0kseUJBQXlCLENBQUNEO1lBR3hEM0MsUUFBUSxDQUFDMkMsYUFBcUIxQyxVQUFpQixJQUFJLENBQUNqQixrQkFBa0IsQ0FDcEUsb0JBQW9CLG1CQUFtQjtvQkFBRTJEO29CQUFhMUM7Z0JBQVEsR0FDOUQsSUFBTSxJQUFJLENBQUN1QyxnQkFBZ0IsQ0FBQ0ssZUFBZSxDQUFDRixhQUFhMUM7WUFHM0Q2QyxhQUFhLENBQUNDLFlBQXVCLElBQUksQ0FBQy9ELGtCQUFrQixDQUMxRCxvQkFBb0IsdUJBQXVCO29CQUFFK0Q7Z0JBQVUsR0FDdkQsSUFBTSxJQUFJLENBQUNQLGdCQUFnQixDQUFDUSxtQkFBbUIsQ0FBQ0Q7WUFHbERFLFVBQVUsSUFBTSxJQUFJLENBQUNqRSxrQkFBa0IsQ0FDckMsb0JBQW9CLHFCQUFxQixDQUFDLEdBQzFDLElBQU0sSUFBSSxDQUFDd0QsZ0JBQWdCLENBQUNVLGlCQUFpQjtRQUVqRDtJQUNGO0lBRUEsbURBQW1EO0lBRW5EOztHQUVDLEdBQ0QsSUFBSUMsT0FBTztRQUNULE9BQU87WUFDTCxPQUFPO1lBQ1BDLE9BQU8sQ0FBQ0MsY0FBcUIsSUFBSSxDQUFDckUsa0JBQWtCLENBQ2xELGVBQWUsU0FBU3FFLGFBQ3hCLElBQU0sSUFBSSxDQUFDQyxXQUFXLENBQUNGLEtBQUssQ0FBQ0M7WUFHL0JFLFFBQVEsQ0FBQ0MsZUFBeUIsSUFBSSxDQUFDeEUsa0JBQWtCLENBQ3ZELGVBQWUsVUFBVTtvQkFBRXdFO2dCQUFhLEdBQ3hDLElBQU0sSUFBSSxDQUFDRixXQUFXLENBQUNDLE1BQU0sQ0FBQ0M7WUFHaENBLGNBQWMsQ0FBQ0MsVUFBaUIsSUFBSSxDQUFDekUsa0JBQWtCLENBQ3JELGVBQWUsZ0JBQWdCeUUsU0FDL0IsSUFBTSxJQUFJLENBQUNILFdBQVcsQ0FBQ0UsWUFBWSxDQUFDQztZQUd0Q0MsZUFBZSxDQUFDQyxRQUFrQixJQUFJLENBQUMzRSxrQkFBa0IsQ0FDdkQsZUFBZSxpQkFBaUI7b0JBQUUyRTtnQkFBTSxHQUN4QyxJQUFNLElBQUksQ0FBQ0wsV0FBVyxDQUFDSSxhQUFhLENBQUNDO1lBR3ZDLE9BQU87WUFDUEMsYUFBYSxDQUFDbkUsS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUNsRCxlQUFlLGVBQWU7b0JBQUVTO2dCQUFHLEdBQ25DLElBQU0sSUFBSSxDQUFDNkQsV0FBVyxDQUFDTSxXQUFXLENBQUNuRTtZQUdyQ29FLG1CQUFtQixDQUFDQyxXQUFxQixJQUFJLENBQUM5RSxrQkFBa0IsQ0FDOUQsZUFBZSxxQkFBcUI7b0JBQUU4RTtnQkFBUyxHQUMvQyxJQUFNLElBQUksQ0FBQ1IsV0FBVyxDQUFDTyxpQkFBaUIsQ0FBQ0M7WUFHM0NDLFlBQVksQ0FBQ0MsV0FBa0IsSUFBSSxDQUFDaEYsa0JBQWtCLENBQ3BELGVBQWUsY0FBY2dGLFVBQzdCLElBQU0sSUFBSSxDQUFDVixXQUFXLENBQUNTLFVBQVUsQ0FBQ0M7WUFHcENDLFlBQVksQ0FBQ3hFLElBQVlRLFVBQWlCLElBQUksQ0FBQ2pCLGtCQUFrQixDQUMvRCxlQUFlLGNBQWM7b0JBQUVTO29CQUFJUTtnQkFBUSxHQUMzQyxJQUFNLElBQUksQ0FBQ3FELFdBQVcsQ0FBQ1csVUFBVSxDQUFDeEUsSUFBSVE7WUFHeENpRSxZQUFZLENBQUN6RSxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQ2pELGVBQWUsY0FBYztvQkFBRVM7Z0JBQUcsR0FDbEMsSUFBTSxJQUFJLENBQUM2RCxXQUFXLENBQUNZLFVBQVUsQ0FBQ3pFO1lBR3BDLE9BQU87WUFDUDBFLGVBQWUsQ0FBQ0MsUUFBZ0JDLGNBQXFCLElBQUksQ0FBQ3JGLGtCQUFrQixDQUMxRSxlQUFlLGlCQUFpQjtvQkFBRW9GO29CQUFRQztnQkFBWSxHQUN0RCxJQUFNLElBQUksQ0FBQ2YsV0FBVyxDQUFDYSxhQUFhLENBQUNDLFFBQVFDO1lBRy9DQyxZQUFZLENBQUNDLFlBQXNCLElBQUksQ0FBQ3ZGLGtCQUFrQixDQUN4RCxlQUFlLGNBQWM7b0JBQUV1RjtnQkFBVSxHQUN6QyxJQUFNLElBQUksQ0FBQ2pCLFdBQVcsQ0FBQ2dCLFVBQVUsQ0FBQ0M7WUFHcENDLGVBQWUsQ0FBQ0QsV0FBbUJ0RSxVQUFpQixJQUFJLENBQUNqQixrQkFBa0IsQ0FDekUsZUFBZSxpQkFBaUI7b0JBQUV1RjtvQkFBV3RFO2dCQUFRLEdBQ3JELElBQU0sSUFBSSxDQUFDcUQsV0FBVyxDQUFDa0IsYUFBYSxDQUFDRCxXQUFXdEU7WUFHbER3RSxlQUFlLENBQUNGLFlBQXNCLElBQUksQ0FBQ3ZGLGtCQUFrQixDQUMzRCxlQUFlLGlCQUFpQjtvQkFBRXVGO2dCQUFVLEdBQzVDLElBQU0sSUFBSSxDQUFDakIsV0FBVyxDQUFDbUIsYUFBYSxDQUFDRjtZQUd2Q0csaUJBQWlCLENBQUNOLFNBQW1CLElBQUksQ0FBQ3BGLGtCQUFrQixDQUMxRCxlQUFlLG1CQUFtQjtvQkFBRW9GO2dCQUFPLEdBQzNDLElBQU0sSUFBSSxDQUFDZCxXQUFXLENBQUNvQixlQUFlLENBQUNOO1lBR3pDLE9BQU87WUFDUE8sZ0JBQWdCLENBQUNQLFFBQWdCUSxpQkFBeUJDLGNBQXdCLElBQUksQ0FBQzdGLGtCQUFrQixDQUN2RyxlQUFlLGtCQUFrQjtvQkFBRW9GO2dCQUFPLEdBQzFDLElBQU0sSUFBSSxDQUFDZCxXQUFXLENBQUNxQixjQUFjLENBQUNQLFFBQVFRLGlCQUFpQkM7WUFHakVDLGVBQWUsQ0FBQ1YsUUFBZ0JTLGNBQXdCLElBQUksQ0FBQzdGLGtCQUFrQixDQUM3RSxlQUFlLGlCQUFpQjtvQkFBRW9GO2dCQUFPLEdBQ3pDLElBQU0sSUFBSSxDQUFDZCxXQUFXLENBQUN3QixhQUFhLENBQUNWLFFBQVFTO1FBRWpEO0lBQ0Y7SUFFQSx3REFBd0Q7SUFFeEQ7O0dBRUMsR0FDRCxJQUFJRSxrQkFBa0I7UUFDcEIsT0FBTztZQUNMLFVBQVU7WUFDVkMscUJBQXFCLENBQUNDLFVBQWlCLElBQUksQ0FBQ2pHLGtCQUFrQixDQUM1RCwwQkFBMEIsdUJBQXVCaUcsU0FDakQsSUFBTSxJQUFJLENBQUNDLHNCQUFzQixDQUFDRixtQkFBbUIsQ0FBQ0M7WUFHeERFLHNCQUFzQixDQUFDZixRQUFnQkcsWUFBc0IsSUFBSSxDQUFDdkYsa0JBQWtCLENBQ2xGLDBCQUEwQix3QkFBd0I7b0JBQUVvRjtvQkFBUUc7Z0JBQVUsR0FDdEUsSUFBTSxJQUFJLENBQUNXLHNCQUFzQixDQUFDQyxvQkFBb0IsQ0FBQ2YsUUFBUUc7WUFHakUsVUFBVTtZQUNWYSxtQkFBbUIsQ0FBQ3pCLFFBQWtCLElBQUksQ0FBQzNFLGtCQUFrQixDQUMzRCwwQkFBMEIscUJBQXFCO29CQUFFcUcsV0FBVyxFQUFFMUIsa0JBQUFBLDRCQUFBQSxNQUFPL1EsTUFBTTtnQkFBQyxHQUM1RSxJQUFNLElBQUksQ0FBQ3NTLHNCQUFzQixDQUFDRSxpQkFBaUIsQ0FBQ3pCO1lBR3REMkIsb0JBQW9CLENBQUMzQixRQUFrQixJQUFJLENBQUMzRSxrQkFBa0IsQ0FDNUQsMEJBQTBCLHNCQUFzQjtvQkFBRXFHLFdBQVcsRUFBRTFCLGtCQUFBQSw0QkFBQUEsTUFBTy9RLE1BQU07Z0JBQUMsR0FDN0UsSUFBTSxJQUFJLENBQUNzUyxzQkFBc0IsQ0FBQ0ksa0JBQWtCLENBQUMzQjtZQUd2RCxZQUFZO1lBQ1o0QixpQkFBaUIsQ0FBQzVCLFFBQWtCLElBQUksQ0FBQzNFLGtCQUFrQixDQUN6RCwwQkFBMEIsbUJBQW1CO29CQUFFcUcsV0FBVyxFQUFFMUIsa0JBQUFBLDRCQUFBQSxNQUFPL1EsTUFBTTtnQkFBQyxHQUMxRSxJQUFNLElBQUksQ0FBQ3NTLHNCQUFzQixDQUFDSyxlQUFlLENBQUM1QjtZQUdwRDZCLG9CQUFvQixDQUFDN0IsT0FBZThCLG1CQUE4QixJQUFJLENBQUN6RyxrQkFBa0IsQ0FDdkYsMEJBQTBCLHNCQUFzQjtvQkFBRXFHLFdBQVcsRUFBRTFCLGtCQUFBQSw0QkFBQUEsTUFBTy9RLE1BQU07b0JBQUU2UztnQkFBaUIsR0FDL0YsSUFBTSxJQUFJLENBQUNQLHNCQUFzQixDQUFDTSxrQkFBa0IsQ0FBQzdCLE9BQU84QjtZQUc5REMsdUJBQXVCLENBQUMvQixRQUFrQixJQUFJLENBQUMzRSxrQkFBa0IsQ0FDL0QsMEJBQTBCLHlCQUF5QjtvQkFBRXFHLFdBQVcsRUFBRTFCLGtCQUFBQSw0QkFBQUEsTUFBTy9RLE1BQU07Z0JBQUMsR0FDaEYsSUFBTSxJQUFJLENBQUNzUyxzQkFBc0IsQ0FBQ1EscUJBQXFCLENBQUMvQjtZQUcxRGdDLG1CQUFtQixJQUFNLElBQUksQ0FBQzNHLGtCQUFrQixDQUM5QywwQkFBMEIscUJBQXFCLENBQUMsR0FDaEQsSUFBTSxJQUFJLENBQUNrRyxzQkFBc0IsQ0FBQ1MsaUJBQWlCO1lBR3JELE9BQU87WUFDUEMsb0JBQW9CLElBQU0sSUFBSSxDQUFDNUcsa0JBQWtCLENBQy9DLDBCQUEwQixzQkFBc0IsQ0FBQyxHQUNqRCxJQUFNLElBQUksQ0FBQ2tHLHNCQUFzQixDQUFDVSxrQkFBa0I7UUFFeEQ7SUFDRjtJQUVBLHFEQUFxRDtJQUVyRDs7R0FFQyxHQUNELElBQUlDLFFBQVE7UUFDVixPQUFPO1lBQ0wsT0FBTztZQUNQMUssUUFBUSxJQUFNLElBQUksQ0FBQzZELGtCQUFrQixDQUNuQyxlQUFlLFlBQVksQ0FBQyxHQUM1QixJQUFNLElBQUksQ0FBQzhHLFdBQVcsQ0FBQ0MsUUFBUTtZQUdqQ3ZHLFNBQVMsQ0FBQ0MsS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM5QyxlQUFlLGVBQWU7b0JBQUVTO2dCQUFHLEdBQ25DLElBQU0sSUFBSSxDQUFDcUcsV0FBVyxDQUFDRSxXQUFXLENBQUN2RztZQUdyQ0UsV0FBVyxDQUFDQyxPQUFpQixJQUFJLENBQUNaLGtCQUFrQixDQUNsRCxlQUFlLGlCQUFpQjtvQkFBRVk7Z0JBQUssR0FDdkMsSUFBTSxJQUFJLENBQUNrRyxXQUFXLENBQUNHLGFBQWEsQ0FBQ3JHO1lBR3ZDRSxRQUFRLENBQUNvRyxXQUFrQixJQUFJLENBQUNsSCxrQkFBa0IsQ0FDaEQsZUFBZSxjQUFja0gsVUFDN0IsSUFBTSxJQUFJLENBQUNKLFdBQVcsQ0FBQ0ssVUFBVSxDQUFDRDtZQUdwQ2xHLFFBQVEsQ0FBQ1AsSUFBWVEsVUFBaUIsSUFBSSxDQUFDakIsa0JBQWtCLENBQzNELGVBQWUsY0FBYztvQkFBRVM7b0JBQUlRO2dCQUFRLEdBQzNDLElBQU0sSUFBSSxDQUFDNkYsV0FBVyxDQUFDTSxVQUFVLENBQUMzRyxJQUFJUTtZQUd4Q2pKLFFBQVEsQ0FBQ3lJLEtBQWUsSUFBSSxDQUFDVCxrQkFBa0IsQ0FDN0MsZUFBZSxjQUFjO29CQUFFUztnQkFBRyxHQUNsQyxJQUFNLElBQUksQ0FBQ3FHLFdBQVcsQ0FBQ08sVUFBVSxDQUFDNUc7WUFHcEMsT0FBTztZQUNQNkcsZ0JBQWdCLElBQU0sSUFBSSxDQUFDdEgsa0JBQWtCLENBQzNDLGVBQWUsa0JBQWtCLENBQUMsR0FDbEMsSUFBTSxJQUFJLENBQUM4RyxXQUFXLENBQUNRLGNBQWM7WUFHdkNDLHdCQUF3QixDQUFDQyxTQUFtQixJQUFJLENBQUN4SCxrQkFBa0IsQ0FDakUsZUFBZSwwQkFBMEI7b0JBQUV3SDtnQkFBTyxHQUNsRCxJQUFNLElBQUksQ0FBQ1YsV0FBVyxDQUFDUyxzQkFBc0IsQ0FBQ0M7WUFHaERDLG1CQUFtQixDQUFDQyxRQUFnQkMsZ0JBQTRCLElBQUksQ0FBQzNILGtCQUFrQixDQUNyRixlQUFlLHlCQUF5QjtvQkFBRTBIO29CQUFRQztnQkFBYyxHQUNoRSxJQUFNLElBQUksQ0FBQ2IsV0FBVyxDQUFDYyxxQkFBcUIsQ0FBQ0YsUUFBUUM7UUFFekQ7SUFDRjtJQUVBOztHQUVDLEdBQ0QsSUFBSXhXLFNBQVM7UUFDWCxPQUFPO1lBQ0xnTCxRQUFRLENBQUNqSjtnQkFDUHlDLFFBQVFwQyxHQUFHLENBQUMsNkRBQW1ELElBQUssQ0FBU3NVLFdBQVc7Z0JBQ3hGbFMsUUFBUXBDLEdBQUcsQ0FBQyx5REFBK0MsSUFBSyxDQUFDdVUsWUFBWSxDQUFTQyxXQUFXO2dCQUNqRyxPQUFPLElBQUksQ0FBQy9ILGtCQUFrQixDQUM1QixnQkFBZ0IsYUFBYTlNLFFBQzdCLElBQU0sSUFBSSxDQUFDNFUsWUFBWSxDQUFDRSxTQUFTLENBQUM5VTtZQUV0QztZQUVBc04sU0FBUyxDQUFDQyxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzlDLGdCQUFnQixnQkFBZ0I7b0JBQUVTO2dCQUFHLEdBQ3JDLElBQU0sSUFBSSxDQUFDcUgsWUFBWSxDQUFDRyxZQUFZLENBQUN4SDtZQUd2Q3lILGFBQWEsQ0FBQ0MsY0FBd0IsSUFBSSxDQUFDbkksa0JBQWtCLENBQzNELGdCQUFnQixvQkFBb0I7b0JBQUVtSTtnQkFBWSxHQUNsRCxJQUFNLElBQUksQ0FBQ0wsWUFBWSxDQUFDTSxnQkFBZ0IsQ0FBQ0Q7WUFHM0NySCxRQUFRLENBQUNySjtnQkFDUDlCLFFBQVFwQyxHQUFHLENBQUMsNkRBQW1ELElBQUssQ0FBU3NVLFdBQVc7Z0JBQ3hGbFMsUUFBUXBDLEdBQUcsQ0FBQyx5REFBK0MsSUFBSyxDQUFDdVUsWUFBWSxDQUFTQyxXQUFXO2dCQUNqRyxPQUFPLElBQUksQ0FBQy9ILGtCQUFrQixDQUM1QixnQkFBZ0IsZUFBZXZJLE1BQy9CLElBQU0sSUFBSSxDQUFDcVEsWUFBWSxDQUFDTyxXQUFXLENBQUM1UTtZQUV4QztZQUVBdUosUUFBUSxDQUFDUCxJQUFZUSxVQUFpQixJQUFJLENBQUNqQixrQkFBa0IsQ0FDM0QsZ0JBQWdCLGVBQWU7b0JBQUVTO29CQUFJUTtnQkFBUSxHQUM3QyxJQUFNLElBQUksQ0FBQzZHLFlBQVksQ0FBQ1EsV0FBVyxDQUFDN0gsSUFBSVE7WUFHMUNqSixRQUFRLENBQUN5SSxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzdDLGdCQUFnQixlQUFlO29CQUFFUztnQkFBRyxHQUNwQyxJQUFNLElBQUksQ0FBQ3FILFlBQVksQ0FBQ1MsV0FBVyxDQUFDOUg7WUFHdEMrSCxhQUFhLENBQUNwUyxTQUFtQixJQUFJLENBQUM0SixrQkFBa0IsQ0FDdEQsZ0JBQWdCLHFCQUFxQjtvQkFBRTVKO2dCQUFPLEdBQzlDLElBQU0sSUFBSSxDQUFDMFIsWUFBWSxDQUFDVyxpQkFBaUIsQ0FBQ3JTO1lBRzVDc1MsZUFBZSxDQUFDQyxhQUF1QixJQUFJLENBQUMzSSxrQkFBa0IsQ0FDNUQsZ0JBQWdCLHVCQUF1QjtvQkFBRTJJO2dCQUFXLEdBQ3BELElBQU0sSUFBSSxDQUFDYixZQUFZLENBQUNjLG1CQUFtQixDQUFDRDtZQUc5Q0UsZUFBZSxDQUFDQyxhQUF1QixJQUFJLENBQUM5SSxrQkFBa0IsQ0FDNUQsZ0JBQWdCLHVCQUF1QjtvQkFBRThJO2dCQUFXLEdBQ3BELElBQU0sSUFBSSxDQUFDaEIsWUFBWSxDQUFDaUIsbUJBQW1CLENBQUNEO1lBRzlDRSxnQkFBZ0IsQ0FBQ0MsV0FBbUJDLFVBQW9CLElBQUksQ0FBQ2xKLGtCQUFrQixDQUM3RSxnQkFBZ0Isd0JBQXdCO29CQUFFaUo7b0JBQVdDO2dCQUFRLEdBQzdELElBQU0sSUFBSSxDQUFDcEIsWUFBWSxDQUFDcUIsb0JBQW9CLENBQUNGLFdBQVdDO1FBTTVEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELElBQUlFLFdBQVc7UUFDYixPQUFPO1lBQ0xDLG1CQUFtQixJQUFNLElBQUksQ0FBQ3JKLGtCQUFrQixDQUM5QyxtQkFBbUIscUJBQXFCLENBQUMsR0FDekMsSUFBTSxJQUFJLENBQUNzSixlQUFlLENBQUNELGlCQUFpQjtZQUc5QzdJLFNBQVMsQ0FBQ0MsS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM5QyxtQkFBbUIsd0JBQXdCO29CQUFFUztnQkFBRyxHQUNoRCxJQUFNLElBQUksQ0FBQzZJLGVBQWUsQ0FBQ0Msb0JBQW9CLENBQUM5STtZQUdsREssUUFBUSxDQUFDckosT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDNUMsbUJBQW1CLHVCQUF1QnZJLE1BQzFDLElBQU0sSUFBSSxDQUFDNlIsZUFBZSxDQUFDRSxtQkFBbUIsQ0FBQy9SO1lBR2pEdUosUUFBUSxDQUFDUCxJQUFZaEosT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDeEQsbUJBQW1CLHVCQUF1QjtvQkFBRVM7b0JBQUksR0FBR2hKLElBQUk7Z0JBQUMsR0FDeEQsSUFBTSxJQUFJLENBQUM2UixlQUFlLENBQUNHLG1CQUFtQixDQUFDaEosSUFBSWhKO1lBR3JETyxRQUFRLENBQUN5SSxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzdDLG1CQUFtQix1QkFBdUI7b0JBQUVTO2dCQUFHLEdBQy9DLElBQU0sSUFBSSxDQUFDNkksZUFBZSxDQUFDSSxtQkFBbUIsQ0FBQ2pKO1lBR2pEa0osaUJBQWlCLENBQUNDLFVBQWtCQyxPQUFjLElBQUksQ0FBQzdKLGtCQUFrQixDQUN2RSxtQkFBbUIsbUJBQW1CO29CQUFFNEo7b0JBQVVDO2dCQUFLLEdBQ3ZELElBQU0sSUFBSSxDQUFDUCxlQUFlLENBQUNLLGVBQWUsQ0FBQ0MsVUFBVUM7WUFHdkRDLG9CQUFvQixDQUFDRixVQUFrQkcsUUFBZ0J0UyxPQUFjLElBQUksQ0FBQ3VJLGtCQUFrQixDQUMxRixtQkFBbUIsc0JBQXNCO29CQUFFNEo7b0JBQVVHO29CQUFRLEdBQUd0UyxJQUFJO2dCQUFDLEdBQ3JFLElBQU0sSUFBSSxDQUFDNlIsZUFBZSxDQUFDUSxrQkFBa0IsQ0FBQ0YsVUFBVUcsUUFBUXRTO1lBR2xFdVMsb0JBQW9CLENBQUNKLFVBQWtCRyxTQUFtQixJQUFJLENBQUMvSixrQkFBa0IsQ0FDL0UsbUJBQW1CLHNCQUFzQjtvQkFBRTRKO29CQUFVRztnQkFBTyxHQUM1RCxJQUFNLElBQUksQ0FBQ1QsZUFBZSxDQUFDVSxrQkFBa0IsQ0FBQ0osVUFBVUc7WUFLMURFLHlCQUF5QixDQUFDQyxlQUFzQkM7Z0JBQzlDLE1BQU1sSyxZQUFZaE0sS0FBS1IsR0FBRztnQkFDMUIsTUFBTTBDLFNBQVMsSUFBSSxDQUFDbVQsZUFBZSxDQUFDVyx1QkFBdUIsQ0FBQ0MsZUFBZUM7Z0JBQzNFLE1BQU1oWCxXQUFXYyxLQUFLUixHQUFHLEtBQUt3TTtnQkFDOUIsSUFBSSxDQUFDbE4sU0FBUyxDQUFDLG1CQUFtQiwyQkFBMkI7b0JBQUVtWDtvQkFBZUM7Z0JBQWUsR0FBR2hYLFVBQVU7Z0JBQzFHLE9BQU9nRDtZQUNUO1lBRUFpVSxrQkFBa0IsQ0FBQ25LLFdBQW1Cb0s7Z0JBQ3BDLE1BQU0vVSxRQUFRckIsS0FBS1IsR0FBRztnQkFDdEIsTUFBTTBDLFNBQVMsSUFBSSxDQUFDbVQsZUFBZSxDQUFDYyxnQkFBZ0IsQ0FBQ25LLFdBQVdvSztnQkFDaEUsTUFBTWxYLFdBQVdjLEtBQUtSLEdBQUcsS0FBSzZCO2dCQUM5QixJQUFJLENBQUN2QyxTQUFTLENBQUMsbUJBQW1CLG9CQUFvQjtvQkFBRWtOO29CQUFXb0s7Z0JBQVEsR0FBR2xYLFVBQVU7Z0JBQ3hGLE9BQU9nRDtZQUNUO1lBRUFtVSxZQUFZLElBQU0sSUFBSSxDQUFDdEssa0JBQWtCLENBQ3ZDLG1CQUFtQiwyQkFBMkIsQ0FBQyxHQUMvQyxJQUFNLElBQUksQ0FBQ3NKLGVBQWUsQ0FBQ2lCLHVCQUF1QjtRQUV0RDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxJQUFJQyxtQkFBbUI7UUFDckIsT0FBTztZQUNMck8sUUFBUSxDQUFDakosU0FBaUIsSUFBSSxDQUFDOE0sa0JBQWtCLENBQy9DLDBCQUEwQixVQUFVOU0sUUFDcEMsSUFBTSxJQUFJLENBQUN1WCxzQkFBc0IsQ0FBQ3RPLE1BQU0sQ0FBQ2pKO1lBRzNDc04sU0FBUyxDQUFDQyxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQzlDLDBCQUEwQixXQUFXO29CQUFFUztnQkFBRyxHQUMxQyxJQUFNLElBQUksQ0FBQ2dLLHNCQUFzQixDQUFDakssT0FBTyxDQUFDQztZQUc1Q2lLLGtCQUFrQixDQUFDdkMsY0FBd0IsSUFBSSxDQUFDbkksa0JBQWtCLENBQ2hFLDBCQUEwQixvQkFBb0I7b0JBQUVtSTtnQkFBWSxHQUM1RCxJQUFNLElBQUksQ0FBQ3NDLHNCQUFzQixDQUFDQyxnQkFBZ0IsQ0FBQ3ZDO1lBR3JEd0MsZUFBZSxDQUFDbFQsT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDbkQsMEJBQTBCLGlCQUFpQnZJLE1BQzNDLElBQU0sSUFBSSxDQUFDZ1Qsc0JBQXNCLENBQUNFLGFBQWEsQ0FBQ2xUO1lBR2xELHNCQUFzQjtZQUN0QnFKLFFBQVEsQ0FBQ3JKLE9BQWMsSUFBSSxDQUFDdUksa0JBQWtCLENBQzVDLDBCQUEwQixpQkFBaUJ2SSxNQUMzQztvQkFDRSxpQkFBaUI7b0JBQ2pCLE1BQU1tVCxVQUFVO3dCQUNkLEdBQUduVCxJQUFJO3dCQUNQb1QsZ0JBQWdCcFQsS0FBS29ULGNBQWMsSUFBSSxPQUFrQixPQUFYNVcsS0FBS1IsR0FBRzt3QkFDdERxWCxlQUFlclQsS0FBS3FULGFBQWEsSUFBSTt3QkFDckNDLGVBQWV0VCxLQUFLc1QsYUFBYSxJQUFJLElBQUk5VyxPQUFPK1csV0FBVztvQkFDN0Q7b0JBQ0EsT0FBTyxJQUFJLENBQUNQLHNCQUFzQixDQUFDRSxhQUFhLENBQUNDO2dCQUNuRDtZQUtGNUosUUFBUSxDQUFDUCxJQUFZaEosT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDeEQsMEJBQTBCLFVBQVU7b0JBQUVTO29CQUFJLEdBQUdoSixJQUFJO2dCQUFDLEdBQ2xELElBQU0sSUFBSSxDQUFDZ1Qsc0JBQXNCLENBQUN6SixNQUFNLENBQUNQLElBQUloSjtZQUcvQ08sUUFBUSxDQUFDeUksS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM3QywwQkFBMEIsVUFBVTtvQkFBRVM7Z0JBQUcsR0FDekMsSUFBTSxJQUFJLENBQUNnSyxzQkFBc0IsQ0FBQ3pTLE1BQU0sQ0FBQ3lJO1lBRzNDK0gsYUFBYSxDQUFDcFMsU0FBbUIsSUFBSSxDQUFDNEosa0JBQWtCLENBQ3RELDBCQUEwQixlQUFlO29CQUFFNUo7Z0JBQU8sR0FDbEQsSUFBTSxJQUFJLENBQUNxVSxzQkFBc0IsQ0FBQ2pDLFdBQVcsQ0FBQ3BTO1lBR2hENlUsbUJBQW1CLENBQUNDLGVBQXlCLElBQUksQ0FBQ2xMLGtCQUFrQixDQUNsRSwwQkFBMEIscUJBQXFCO29CQUFFa0w7Z0JBQWEsR0FDOUQsSUFBTSxJQUFJLENBQUNULHNCQUFzQixDQUFDUSxpQkFBaUIsQ0FBQ0M7WUFHdEQsU0FBUztZQUNUQyxlQUFlLElBQU0sSUFBSSxDQUFDbkwsa0JBQWtCLENBQzFDLDBCQUEwQixpQkFBaUIsQ0FBQyxHQUM1QyxJQUFNLElBQUksQ0FBQ3lLLHNCQUFzQixDQUFDVSxhQUFhO1FBS25EO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELElBQUlDLHVCQUF1QjtRQUN6QixPQUFPO1lBQ0xqUCxRQUFRLENBQUNqSixTQUFpQixJQUFJLENBQUM4TSxrQkFBa0IsQ0FDL0MsOEJBQThCLFVBQVU5TSxRQUN4QyxJQUFNLElBQUksQ0FBQ21ZLDBCQUEwQixDQUFDbFAsTUFBTSxDQUFDako7WUFHL0NzTixTQUFTLENBQUNDLEtBQWUsSUFBSSxDQUFDVCxrQkFBa0IsQ0FDOUMsOEJBQThCLFdBQVc7b0JBQUVTO2dCQUFHLEdBQzlDLElBQU0sSUFBSSxDQUFDNEssMEJBQTBCLENBQUM3SyxPQUFPLENBQUNDO1lBR2hENkssa0JBQWtCLENBQUNDLGNBQXdCLElBQUksQ0FBQ3ZMLGtCQUFrQixDQUNoRSw4QkFBOEIsb0JBQW9CO29CQUFFdUw7Z0JBQVksR0FDaEUsSUFBTSxJQUFJLENBQUNGLDBCQUEwQixDQUFDQyxnQkFBZ0IsQ0FBQ0M7WUFHekR6SyxRQUFRLENBQUNySixPQUFjLElBQUksQ0FBQ3VJLGtCQUFrQixDQUM1Qyw4QkFBOEIsVUFBVXZJLE1BQ3hDLElBQU0sSUFBSSxDQUFDNFQsMEJBQTBCLENBQUN2SyxNQUFNLENBQUNySjtZQUcvQ3VKLFFBQVEsQ0FBQ1AsSUFBWWhKLE9BQWMsSUFBSSxDQUFDdUksa0JBQWtCLENBQ3hELDhCQUE4QixVQUFVO29CQUFFUztvQkFBSSxHQUFHaEosSUFBSTtnQkFBQyxHQUN0RCxJQUFNLElBQUksQ0FBQzRULDBCQUEwQixDQUFDckssTUFBTSxDQUFDUCxJQUFJaEo7WUFHbkRPLFFBQVEsQ0FBQ3lJLEtBQWUsSUFBSSxDQUFDVCxrQkFBa0IsQ0FDN0MsOEJBQThCLFVBQVU7b0JBQUVTO2dCQUFHLEdBQzdDLElBQU0sSUFBSSxDQUFDNEssMEJBQTBCLENBQUNyVCxNQUFNLENBQUN5STtZQUcvQytILGFBQWEsQ0FBQ3BTLFNBQW1CLElBQUksQ0FBQzRKLGtCQUFrQixDQUN0RCw4QkFBOEIsZUFBZTtvQkFBRTVKO2dCQUFPLEdBQ3RELElBQU0sSUFBSSxDQUFDaVYsMEJBQTBCLENBQUM3QyxXQUFXLENBQUNwUztZQUdwRG9WLG9CQUFvQixDQUFDQyxnQkFBMEIsSUFBSSxDQUFDekwsa0JBQWtCLENBQ3BFLDhCQUE4QixzQkFBc0I7b0JBQUV5TDtnQkFBYyxHQUNwRSxJQUFNLElBQUksQ0FBQ0osMEJBQTBCLENBQUNHLGtCQUFrQixDQUFDQztZQUczREMsa0JBQWtCLENBQUNDLGNBQXdCLElBQUksQ0FBQzNMLGtCQUFrQixDQUNoRSw4QkFBOEIsb0JBQW9CO29CQUFFMkw7Z0JBQVksR0FDaEUsSUFBTSxJQUFJLENBQUNOLDBCQUEwQixDQUFDSyxnQkFBZ0IsQ0FBQ0M7UUFFM0Q7SUFDRjtJQUVBOzs7R0FHQyxHQUNELElBQUlsYSxlQUFlO1FBQ2pCLE9BQU87WUFDTCxrQkFBa0I7WUFDbEIwSyxRQUFRLENBQUNqSixTQUFpQixJQUFJLENBQUM4TSxrQkFBa0IsQ0FDL0Msc0JBQXNCLFVBQVU5TSxRQUNoQyxJQUFNLElBQUksQ0FBQzBZLGtCQUFrQixDQUFDelAsTUFBTTtZQUd0QzBQLGlCQUFpQixDQUFDM1ksU0FBaUIsSUFBSSxDQUFDOE0sa0JBQWtCLENBQ3hELHNCQUFzQixtQkFBbUI5TSxRQUN6QyxJQUFNLElBQUksQ0FBQzBZLGtCQUFrQixDQUFDQyxlQUFlLENBQUMzWTtZQUdoRHNOLFNBQVMsQ0FBQ0MsS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM5QyxzQkFBc0Isc0JBQXNCO29CQUFFUztnQkFBRyxHQUNqRCxJQUFNLElBQUksQ0FBQ21MLGtCQUFrQixDQUFDRSxrQkFBa0IsQ0FBQ3JMO1lBR25EekUsdUJBQXVCLElBQU0sSUFBSSxDQUFDZ0Usa0JBQWtCLENBQ2xELHNCQUFzQix5QkFBeUIsQ0FBQyxHQUNoRCxJQUFNLElBQUksQ0FBQzRMLGtCQUFrQixDQUFDNVAscUJBQXFCO1lBR3JELFNBQVM7WUFDVDhFLFFBQVEsQ0FBQ3JKLE9BQWMsSUFBSSxDQUFDdUksa0JBQWtCLENBQzVDLHNCQUFzQixVQUFVdkksTUFDaEMsSUFBTSxJQUFJLENBQUNtVSxrQkFBa0IsQ0FBQzlLLE1BQU0sQ0FBQ3JKO1lBR3ZDdUosUUFBUSxDQUFDUCxJQUFZaEosT0FBYyxJQUFJLENBQUN1SSxrQkFBa0IsQ0FDeEQsc0JBQXNCLFVBQVU7b0JBQUVTO29CQUFJLEdBQUdoSixJQUFJO2dCQUFDLEdBQzlDLElBQU0sSUFBSSxDQUFDbVUsa0JBQWtCLENBQUM1SyxNQUFNLENBQUNQLElBQUloSjtZQUczQ08sUUFBUSxDQUFDeUksS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUM3QyxzQkFBc0IsVUFBVTtvQkFBRVM7Z0JBQUcsR0FDckMsSUFBTSxJQUFJLENBQUNtTCxrQkFBa0IsQ0FBQzVULE1BQU0sQ0FBQ3lJO1lBR3ZDLE9BQU87WUFDUHNMLFdBQVcsQ0FBQ3RMLEtBQWUsSUFBSSxDQUFDVCxrQkFBa0IsQ0FDaEQsc0JBQXNCLHdCQUF3QjtvQkFBRVM7Z0JBQUcsR0FDbkQsSUFBTSxJQUFJLENBQUNtTCxrQkFBa0IsQ0FBQ0ksb0JBQW9CLENBQUN2TDtZQUdyRHdMLGNBQWMsQ0FBQ3hMLElBQVlySyxTQUFnQixJQUFJLENBQUM0SixrQkFBa0IsQ0FDaEUsc0JBQXNCLDJCQUEyQjtvQkFBRVM7b0JBQUksR0FBR3JLLE1BQU07Z0JBQUMsR0FDakUsSUFBTSxJQUFJLENBQUN3VixrQkFBa0IsQ0FBQ00sdUJBQXVCLENBQUN6TCxJQUFJcks7WUFHNUQsT0FBTztZQUNQK1YsWUFBWSxDQUFDQyxlQUF1QmIsY0FBd0IsSUFBSSxDQUFDdkwsa0JBQWtCLENBQ2pGLHNCQUFzQixjQUFjO29CQUFFb007b0JBQWViO2dCQUFZLEdBQ2pFLElBQU0sSUFBSSxDQUFDSyxrQkFBa0IsQ0FBQ08sVUFBVSxDQUFDQyxlQUFlYjtZQUcxRGMsMkJBQTJCLElBQU0sSUFBSSxDQUFDck0sa0JBQWtCLENBQ3RELHNCQUFzQiw2QkFBNkIsQ0FBQyxHQUNwRCxJQUFNLElBQUksQ0FBQzRMLGtCQUFrQixDQUFDUyx5QkFBeUI7WUFHekQsZUFBZTtZQUNmQyw0QkFBNEIsSUFBTSxJQUFJLENBQUN0TSxrQkFBa0IsQ0FDdkQsc0JBQXNCLDhCQUE4QixDQUFDLEdBQ3JELElBQU0sSUFBSSxDQUFDNEwsa0JBQWtCLENBQUNVLDBCQUEwQjtRQUc1RDtJQUNGO0lBRUEsaURBQWlEO0lBRWpEOztHQUVDLEdBQ0RDLGNBQWNuUSxLQUFjLEVBQWU7UUFDekMsT0FBT0EsUUFBUSxJQUFJLENBQUMxSSxVQUFVLENBQUNHLEtBQUssQ0FBQyxDQUFDdUksU0FBUztlQUFJLElBQUksQ0FBQzFJLFVBQVU7U0FBQztJQUNyRTtJQUVBOztHQUVDLEdBQ0Q4WSxrQkFBd0I7UUFDdEIsSUFBSSxDQUFDOVksVUFBVSxHQUFHLEVBQUU7SUFDdEI7SUFFQTs7R0FFQyxHQUNEeVgsZ0JBQWdCO1FBQ2QsTUFBTXNCLGdCQUFnQixJQUFJLENBQUMvWSxVQUFVLENBQUNFLE1BQU07UUFDNUMsTUFBTThZLHFCQUFxQixJQUFJLENBQUNoWixVQUFVLENBQUNlLE1BQU0sQ0FBQ2xCLENBQUFBLE1BQU9BLElBQUlILE9BQU8sRUFBRVEsTUFBTTtRQUM1RSxNQUFNK1ksaUJBQWlCRixnQkFBZ0JDO1FBQ3ZDLE1BQU1FLGtCQUFrQkgsZ0JBQWdCLElBQ3BDLElBQUksQ0FBQy9ZLFVBQVUsQ0FBQ3FCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLekIsTUFBUXlCLE1BQU16QixJQUFJSixRQUFRLEVBQUUsS0FBS3NaLGdCQUM5RDtRQUVKLE9BQU87WUFDTEE7WUFDQUM7WUFDQUM7WUFDQTlNLGFBQWE0TSxnQkFBZ0IsSUFBSSxxQkFBc0JBLGdCQUFpQixNQUFNO1lBQzlFRyxpQkFBaUIvWCxLQUFLQyxLQUFLLENBQUM4WDtZQUM1Qi9aLFFBQVEsSUFBSSxDQUFDQSxNQUFNO1FBQ3JCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELG1DQUFpRDtJQUMvQyxpQkFBaUI7SUFDakIscUJBQXFCO0lBQ3ZCO0lBSUE7O0dBRUMsR0FDRGlhLHdCQUF3QjtRQUN0QixPQUFPdGMsb0VBQWtCQTtJQUMzQjtJQUVBLGFBQWE7SUFFYjs7R0FFQyxHQUNELElBQUl1YyxtQkFBbUI7UUFDckIsT0FBTztZQUNMQyxvQkFBb0IsQ0FBQzlaLFNBQWlCLElBQUksQ0FBQzhNLGtCQUFrQixDQUMzRCwwQkFBMEIsc0JBQXNCOU0sUUFDaEQsSUFBTSxJQUFJLENBQUMrWixzQkFBc0IsQ0FBQ0Qsa0JBQWtCLENBQUM5WjtZQUd2RGdhLG9CQUFvQixDQUFDek0sS0FBZSxJQUFJLENBQUNULGtCQUFrQixDQUN6RCwwQkFBMEIsc0JBQXNCO29CQUFFUztnQkFBRyxHQUNyRCxJQUFNLElBQUksQ0FBQ3dNLHNCQUFzQixDQUFDQyxrQkFBa0IsQ0FBQ3pNO1lBR3ZEME0sbUJBQW1CLENBQUMxVixPQUFjLElBQUksQ0FBQ3VJLGtCQUFrQixDQUN2RCwwQkFBMEIscUJBQXFCdkksTUFDL0MsSUFBTSxJQUFJLENBQUN3VixzQkFBc0IsQ0FBQ0UsaUJBQWlCLENBQUMxVjtZQUd0RDJWLG1CQUFtQixDQUFDM00sSUFBWWhKLE9BQWMsSUFBSSxDQUFDdUksa0JBQWtCLENBQ25FLDBCQUEwQixxQkFBcUI7b0JBQUVTO29CQUFJLEdBQUdoSixJQUFJO2dCQUFDLEdBQzdELElBQU0sSUFBSSxDQUFDd1Ysc0JBQXNCLENBQUNHLGlCQUFpQixDQUFDM00sSUFBSWhKO1lBRzFENFYsbUJBQW1CLENBQUM1TSxLQUFlLElBQUksQ0FBQ1Qsa0JBQWtCLENBQ3hELDBCQUEwQixxQkFBcUI7b0JBQUVTO2dCQUFHLEdBQ3BELElBQU0sSUFBSSxDQUFDd00sc0JBQXNCLENBQUNJLGlCQUFpQixDQUFDNU07WUFHdEQ2TSwwQkFBMEIsQ0FBQ0MsbUJBQTZCLElBQUksQ0FBQ3ZOLGtCQUFrQixDQUM3RSwwQkFBMEIsNEJBQTRCO29CQUFFdU47Z0JBQWlCLEdBQ3pFLElBQU0sSUFBSSxDQUFDTixzQkFBc0IsQ0FBQ0ssd0JBQXdCLENBQUNDO1lBRzdEQywyQkFBMkIsSUFBTSxJQUFJLENBQUN4TixrQkFBa0IsQ0FDdEQsMEJBQTBCLDZCQUE2QixDQUFDLEdBQ3hELElBQU0sSUFBSSxDQUFDaU4sc0JBQXNCLENBQUNPLHlCQUF5QjtZQUc3RHJDLGVBQWUsSUFBTSxJQUFJLENBQUNuTCxrQkFBa0IsQ0FDMUMsMEJBQTBCLGlCQUFpQixDQUFDLEdBQzVDLElBQU0sSUFBSSxDQUFDaU4sc0JBQXNCLENBQUM5QixhQUFhO1lBR2pEc0MsZ0JBQWdCLElBQU0sSUFBSSxDQUFDek4sa0JBQWtCLENBQzNDLDBCQUEwQixrQkFBa0IsQ0FBQyxHQUM3QyxJQUFNLElBQUksQ0FBQ2lOLHNCQUFzQixDQUFDUSxjQUFjO1FBRXBEO0lBQ0Y7SUFFQSxzQkFBc0I7SUFFdEIsMEJBQTBCO0lBRTFCLDBDQUEwQztJQUMxQyx5Q0FBeUM7SUFDekMsSUFBSTtJQUVKOztHQUVDLEdBQ0RDLGFBQWFDLFNBQW9DLEVBQVE7UUFDdkQsSUFBSSxDQUFDOWEsTUFBTSxHQUFHO1lBQUUsR0FBRyxJQUFJLENBQUNBLE1BQU07WUFBRSxHQUFHOGEsU0FBUztRQUFDO1FBRTdDLGdCQUFnQjtRQUNoQixJQUFJQSxVQUFVNWMsYUFBYSxLQUFLLE9BQU87WUFDckMsSUFBSSxDQUFDOEcsS0FBSyxDQUFDK1YsS0FBSztZQUNoQixJQUFJLENBQUM5UyxZQUFZLENBQUM4UyxLQUFLO1lBQ3ZCLElBQUksSUFBSSxDQUFDL2EsTUFBTSxDQUFDTixhQUFhLEVBQUU7Z0JBQzdCb0QsUUFBUXBDLEdBQUcsQ0FBQztZQUNkO1FBQ0Y7UUFFQSxJQUFJLElBQUksQ0FBQ1YsTUFBTSxDQUFDTixhQUFhLEVBQUU7WUFDN0JvRCxRQUFRcEMsR0FBRyxDQUFDLDhCQUE4QixJQUFJLENBQUNWLE1BQU07UUFDdkQ7SUFDRjtJQUVBLG1EQUFtRDtJQUVuRDs7O0dBR0MsR0FDRGdiLGtCQUFrQkMsV0FBbUIsRUFBRUMsU0FBa0IsRUFBVTtRQUNqRSxJQUFJLENBQUMsSUFBSSxDQUFDbGIsTUFBTSxDQUFDOUIsYUFBYSxFQUFFO1lBQzlCLElBQUksSUFBSSxDQUFDOEIsTUFBTSxDQUFDTixhQUFhLEVBQUU7Z0JBQzdCb0QsUUFBUXBDLEdBQUcsQ0FBQztZQUNkO1lBQ0EsT0FBTztRQUNUO1FBRUEsd0JBQXdCO1FBQ3hCLElBQUlxSixlQUFlO1FBQ25CLE1BQU1vUixVQUFVLEdBQWUsT0FBWkYsYUFBWTtRQUUvQixLQUFLLE1BQU0sQ0FBQ2xYLElBQUksSUFBSXVDLE1BQU1sQyxJQUFJLENBQUMsSUFBSSxDQUFDWSxLQUFLLEVBQUc7WUFDMUMsSUFBSWpCLElBQUlxWCxVQUFVLENBQUNELFVBQVU7Z0JBQzNCLElBQUksQ0FBQ25XLEtBQUssQ0FBQ0csTUFBTSxDQUFDcEI7Z0JBQ2xCZ0c7WUFDRjtRQUNGO1FBRUEsSUFBSSxJQUFJLENBQUMvSixNQUFNLENBQUNOLGFBQWEsRUFBRTtZQUM3Qm9ELFFBQVFwQyxHQUFHLENBQUMsK0JBQTZDd2EsT0FBZEQsYUFBd0RsUixPQUExQ21SLFlBQVksS0FBZSxPQUFWQSxXQUFVLE9BQUssSUFBRyxTQUFvQixPQUFiblIsY0FBYTtRQUNsSDtRQUVBLE9BQU9BO0lBQ1Q7SUFFQTs7O0dBR0MsR0FDRHNSLG1CQUFtQjNWLFFBQWdCLEVBQUU0VixXQUFzQixFQUFVO1FBQ25FLElBQUksQ0FBQyxJQUFJLENBQUN0YixNQUFNLENBQUM5QixhQUFhLEVBQUU7WUFDOUIsSUFBSSxJQUFJLENBQUM4QixNQUFNLENBQUNOLGFBQWEsRUFBRTtnQkFDN0JvRCxRQUFRcEMsR0FBRyxDQUFDO1lBQ2Q7WUFDQSxPQUFPO1FBQ1Q7UUFFQSxJQUFJcUosZUFBZTtRQUNuQixNQUFNd1IsYUFBd0M7WUFDNUMsWUFBWTtZQUNaLFVBQVU7WUFDVixnQkFBZ0I7WUFDaEIsY0FBYztRQUNoQjtRQUVBLE1BQU1OLGNBQWNNLFVBQVUsQ0FBQzdWLFNBQVMsSUFBSUE7UUFFNUMsS0FBSyxNQUFNLENBQUMzQixJQUFJLElBQUl1QyxNQUFNbEMsSUFBSSxDQUFDLElBQUksQ0FBQ1ksS0FBSyxFQUFHO1lBQzFDLElBQUlqQixJQUFJcVgsVUFBVSxDQUFDLEdBQWUsT0FBWkgsYUFBWSxPQUFLO2dCQUNyQyxxQkFBcUI7Z0JBQ3JCLElBQUlLLGVBQWVBLFlBQVl2YSxNQUFNLEdBQUcsR0FBRztvQkFDekMsTUFBTXlhLGdCQUFnQkYsWUFBWXJZLElBQUksQ0FBQzJLLENBQUFBLEtBQU03SixJQUFJWCxRQUFRLENBQUN3SztvQkFDMUQsSUFBSTROLGVBQWU7d0JBQ2pCLElBQUksQ0FBQ3hXLEtBQUssQ0FBQ0csTUFBTSxDQUFDcEI7d0JBQ2xCZ0c7b0JBQ0Y7Z0JBQ0YsT0FBTztvQkFDTCxlQUFlO29CQUNmLElBQUksQ0FBQy9FLEtBQUssQ0FBQ0csTUFBTSxDQUFDcEI7b0JBQ2xCZ0c7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsSUFBSSxJQUFJLENBQUMvSixNQUFNLENBQUNOLGFBQWEsRUFBRTtZQUM3Qm9ELFFBQVFwQyxHQUFHLENBQUMsaUNBQW1ENGEsT0FBbEI1VixVQUFTLFdBQWdEcUUsT0FBdkN1UixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFHLElBQUksQ0FBQyxVQUFTLE1BQUssU0FBb0IsT0FBYjFSLGNBQWE7UUFDckg7UUFFQSxPQUFPQTtJQUNUO0lBRUE7OztHQUdDLEdBQ0QyUixnQkFBc0I7UUFDcEIsSUFBSSxDQUFDLElBQUksQ0FBQzFiLE1BQU0sQ0FBQzlCLGFBQWEsRUFBRTtZQUM5QixJQUFJLElBQUksQ0FBQzhCLE1BQU0sQ0FBQ04sYUFBYSxFQUFFO2dCQUM3Qm9ELFFBQVFwQyxHQUFHLENBQUM7WUFDZDtZQUNBO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTWliLGFBQWEsSUFBSSxDQUFDM1csS0FBSyxDQUFDekMsSUFBSTtRQUNsQyxJQUFJLENBQUN5QyxLQUFLLENBQUMrVixLQUFLO1FBQ2hCLElBQUksQ0FBQzlTLFlBQVksQ0FBQzhTLEtBQUs7UUFFdkIsSUFBSSxJQUFJLENBQUMvYSxNQUFNLENBQUNOLGFBQWEsRUFBRTtZQUM3Qm9ELFFBQVFwQyxHQUFHLENBQUMsa0NBQTZDLE9BQVhpYixZQUFXO1FBQzNEO0lBQ0Y7SUFFQTs7O0dBR0MsR0FDREMscUJBQXNDO1FBQ3BDLHdCQUF3QjtRQUN4QixNQUFNdlYsVUFBVUMsTUFBTWxDLElBQUksQ0FBQyxJQUFJLENBQUNZLEtBQUssQ0FBQzZXLE1BQU07UUFDNUMsTUFBTUMsWUFBWXpWLFFBQVFuRSxNQUFNLENBQUMsQ0FBQ0MsS0FBSzRDLFFBQVU1QyxNQUFNNEMsTUFBTWEsYUFBYSxFQUFFO1FBQzVFLE1BQU1tVyxtQkFBbUIxVixRQUFRdEYsTUFBTSxHQUFHLElBQUkrYSxZQUFZelYsUUFBUXRGLE1BQU0sR0FBRztRQUUzRSxVQUFVO1FBQ1YsTUFBTWliLHVCQUF1QjtZQUFFalYsVUFBVTtZQUFHQyxNQUFNO1lBQUdDLFFBQVE7WUFBR0MsS0FBSztRQUFFO1FBQ3ZFYixRQUFRNFYsT0FBTyxDQUFDbFgsQ0FBQUE7WUFDZGlYLG9CQUFvQixDQUFDalgsTUFBTXRHLFFBQVEsQ0FBQztRQUN0QztRQUVBLE9BQU87WUFDTEQsU0FBUyxJQUFJLENBQUN3QixNQUFNLENBQUM5QixhQUFhO1lBQ2xDcUUsTUFBTSxJQUFJLENBQUN5QyxLQUFLLENBQUN6QyxJQUFJO1lBQ3JCNkYsTUFBTSxJQUFJLENBQUMxQixVQUFVLENBQUMwQixJQUFJO1lBQzFCRSxRQUFRLElBQUksQ0FBQzVCLFVBQVUsQ0FBQzRCLE1BQU07WUFDOUI0VCxTQUFTLElBQUksQ0FBQ3hWLFVBQVUsQ0FBQzBCLElBQUksR0FBSSxLQUFJLENBQUMxQixVQUFVLENBQUMwQixJQUFJLEdBQUcsSUFBSSxDQUFDMUIsVUFBVSxDQUFDNEIsTUFBTSxLQUFLO1lBQ25Gc1IsZUFBZSxJQUFJLENBQUNsVCxVQUFVLENBQUNrVCxhQUFhO1lBQzVDLGNBQWM7WUFDZGpULFdBQVcsSUFBSSxDQUFDRCxVQUFVLENBQUNDLFNBQVM7WUFDcEN5QyxhQUFhLElBQUksQ0FBQzFDLFVBQVUsQ0FBQzBDLFdBQVc7WUFDeENOLGdCQUFnQixJQUFJLENBQUNBLGNBQWM7WUFDbkNpVDtZQUNBQztRQUNGO0lBQ0Y7SUFFQTs7R0FFQyxHQUNERyxrQkFBa0JyQixTQUErQixFQUFRO1FBQ3ZELElBQUksQ0FBQ3ZWLFdBQVcsR0FBRztZQUFFLEdBQUcsSUFBSSxDQUFDQSxXQUFXO1lBQUUsR0FBR3VWLFNBQVM7UUFBQztRQUN2RGhZLFFBQVFwQyxHQUFHLENBQUM7SUFDZDtJQUVBLGdEQUFnRDtJQUVoRDs7R0FFQyxHQUNELElBQUkwYixRQUFRO1FBQ1YsT0FBTztZQUNMLFdBQVc7WUFDWEMsd0JBQXdCLENBQUMvZCxTQUN2QlYsd0VBQW9CQSxDQUFDMGUsMkJBQTJCLENBQUNoZTtZQUVuRCxXQUFXO1lBQ1hpZSx3QkFBd0IsQ0FBQ25PLFVBQ3ZCeFEsd0VBQW9CQSxDQUFDNGUsMkJBQTJCLENBQUNwTztZQUVuRCxzQkFBc0I7WUFDdEJxTyw0QkFBNEIsT0FBT0M7Z0JBQ2pDLE1BQU10UCxZQUFZdVAsWUFBWS9iLEdBQUc7Z0JBQ2pDLElBQUk7b0JBQ0YsU0FBUztvQkFDVCxNQUFNZ2MsUUFBUUYsV0FBV3BhLEdBQUcsQ0FBQ3VhLENBQUFBLFlBQWE7NEJBQ3hDLE1BQU12WixTQUFTLE1BQU0sSUFBSSxDQUFDaVYsb0JBQW9CLENBQUN0SyxNQUFNLENBQUM0Tzs0QkFDdEQsSUFBSXZaLE9BQU9DLE1BQU0sS0FBSyxXQUFXO2dDQUMvQixNQUFNLElBQUlnSyxNQUFNakssT0FBT2tLLE9BQU8sSUFBSTs0QkFDcEM7NEJBQ0EsT0FBT2xLLE9BQU9zQixJQUFJO3dCQUNwQjtvQkFFQSxNQUFNa1ksWUFBWUosV0FBV3BhLEdBQUcsQ0FBQyxDQUFDeWEsR0FBR0MsUUFBVSxVQUFvQixPQUFWQSxRQUFRO29CQUNqRSxNQUFNQyxjQUFjLE1BQU1uZixnRkFBd0JBLENBQUNvZixZQUFZLENBQUNOLE9BQU9FO29CQUV2RSxNQUFNeGMsV0FBV3FjLFlBQVkvYixHQUFHLEtBQUt3TTtvQkFDckMsTUFBTSxFQUFFK1AsVUFBVSxFQUFFQyxNQUFNLEVBQUUsR0FBR0g7b0JBQy9CLE1BQU1JLGlCQUFpQkYsV0FBVzdhLEdBQUcsQ0FBQ2dCLENBQUFBLFNBQVVBLE9BQU9zQixJQUFJLEVBQUdoRCxNQUFNLENBQUMwYjtvQkFFckV6Zix3RkFBNEJBLENBQUN3UCxhQUFhLENBQUMsb0NBQW9DL00sVUFBVThjLE9BQU9yYyxNQUFNLEtBQUs7b0JBRTNHLE9BQU87d0JBQ0x3QyxRQUFRO3dCQUNScUIsTUFBTXlZO3dCQUNON1AsU0FBUyxTQUFzQzRQLE9BQTdCQyxlQUFldGMsTUFBTSxFQUFDLFNBQXVELE9BQWhEcWMsT0FBT3JjLE1BQU0sR0FBRyxJQUFJLElBQWtCLE9BQWRxYyxPQUFPcmMsTUFBTSxFQUFDLFNBQU87d0JBQzVGa2MsYUFBYTs0QkFDWEUsWUFBWUEsV0FBV3BjLE1BQU07NEJBQzdCcWMsUUFBUUEsT0FBT3JjLE1BQU07NEJBQ3JCaU0sYUFBYWlRLFlBQVlqUSxXQUFXOzRCQUNwQ3VRLGVBQWVqZDt3QkFDakI7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPRSxPQUFPO29CQUNkLE1BQU1GLFdBQVdxYyxZQUFZL2IsR0FBRyxLQUFLd007b0JBQ3JDdlAsd0ZBQTRCQSxDQUFDd1AsYUFBYSxDQUFDLG9DQUFvQy9NLFVBQVU7b0JBQ3pGLE1BQU1FO2dCQUNSO1lBQ0Y7WUFFQSxXQUFXO1lBQ1hnZCwyQkFBMkIsT0FBT3BQO2dCQUNoQyxNQUFNaEIsWUFBWXVQLFlBQVkvYixHQUFHO2dCQUNqQyxJQUFJO29CQUNGLE1BQU02YyxVQUFVLE1BQU1DLFFBQVFDLEdBQUcsQ0FDL0J2UCxRQUFROUwsR0FBRyxDQUFDOzRCQUFDLEVBQUVzTCxFQUFFLEVBQUVySyxNQUFNLEVBQUU7K0JBQUssSUFBSSxDQUFDM0UsWUFBWSxDQUFDd2EsWUFBWSxDQUFDeEwsSUFBSXJLOztvQkFFckUsTUFBTWpELFdBQVdxYyxZQUFZL2IsR0FBRyxLQUFLd007b0JBQ3JDdlAsd0ZBQTRCQSxDQUFDd1AsYUFBYSxDQUFDLG1DQUFtQy9NLFVBQVU7b0JBRXhGLGlCQUFpQjtvQkFDakIsSUFBSSxDQUFDK2Esa0JBQWtCLENBQUMsZ0JBQWdCak4sUUFBUTlMLEdBQUcsQ0FBQ3NiLENBQUFBLElBQUtBLEVBQUVoUSxFQUFFO29CQUU3RCxPQUFPO3dCQUNMckssUUFBUTt3QkFDUnFCLE1BQU02WSxRQUFRN2IsTUFBTSxDQUFDaWMsQ0FBQUEsSUFBS0EsRUFBRXRhLE1BQU0sS0FBSyxXQUFXakIsR0FBRyxDQUFDdWIsQ0FBQUEsSUFBS0EsRUFBRWpaLElBQUk7d0JBQ2pFNEksU0FBUyxTQUF3QixPQUFmaVEsUUFBUTFjLE1BQU0sRUFBQztvQkFDbkM7Z0JBQ0YsRUFBRSxPQUFPUCxPQUFPO29CQUNkLE1BQU1GLFdBQVdxYyxZQUFZL2IsR0FBRyxLQUFLd007b0JBQ3JDdlAsd0ZBQTRCQSxDQUFDd1AsYUFBYSxDQUFDLG1DQUFtQy9NLFVBQVU7b0JBQ3hGLE1BQU1FO2dCQUNSO1lBQ0Y7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxJQUFJbWMsY0FBYztRQUNoQixPQUFPO1lBQ0wsU0FBUztZQUNUbUIsWUFBWSxJQUFNamdCLHdGQUE0QkEsQ0FBQ2lnQixVQUFVO1lBRXpELFNBQVM7WUFDVEMsV0FBVyxJQUFNbGdCLHdGQUE0QkEsQ0FBQ2tnQixTQUFTO1lBRXZELFNBQVM7WUFDVEMsZ0JBQWdCLElBQU1uZ0Isd0ZBQTRCQSxDQUFDb2dCLDBCQUEwQjtZQUU3RSxTQUFTO1lBQ1RDLGdCQUFnQixJQUFNcmdCLHdGQUE0QkEsQ0FBQ3NnQix5QkFBeUI7WUFFNUUsU0FBUztZQUNUQyxPQUFPLElBQU12Z0Isd0ZBQTRCQSxDQUFDdWdCLEtBQUs7WUFFL0MsU0FBUztZQUNUQyxlQUFlO2dCQUNiLGVBQWU7Z0JBQ2YsSUFBSSxDQUFDelcsa0JBQWtCO2dCQUN2QjlFLFFBQVFwQyxHQUFHLENBQUM7WUFDZDtZQUVBLFdBQVc7WUFDWDRkLGlCQUFpQjtnQkFDZixPQUFPLElBQUksQ0FBQzFDLGtCQUFrQjtZQUNoQztZQUVBLFlBQVk7WUFDWjJDLHFCQUFxQixJQUFNM2dCLHdFQUFvQkEsQ0FBQzRnQixxQkFBcUI7UUFDdkU7SUFDRjtJQUVBLDRCQUE0QjtJQUU1Qjs7R0FFQyxHQUNEQSx3QkFBNEM7UUFDMUMsZUFBZTtRQUNmLElBQUksQ0FBQ2hVLGtCQUFrQixDQUFDVSxXQUFXLEdBQUcsSUFBSSxDQUFDQSxXQUFXO1FBQ3RELE9BQU87WUFBRSxHQUFHLElBQUksQ0FBQ1Ysa0JBQWtCO1FBQUM7SUFDdEM7SUFFQWlVLHFCQUFzQztRQUNwQyxNQUFNQyx3QkFBd0I1Z0IsZ0ZBQXdCQSxDQUFDb2IsU0FBUztRQUVoRSxXQUFXO1FBQ1gsTUFBTXlGLGVBQWUsSUFBSSxDQUFDQyxtQkFBbUI7UUFFN0MsT0FBTztZQUNMQyxvQkFBb0JILHNCQUFzQkksT0FBTztZQUNqREMsYUFBYUwsc0JBQXNCTSxNQUFNO1lBQ3pDdlQsYUFBYTttQkFBSSxJQUFJLENBQUNBLFdBQVcsQ0FBQ3pLLEtBQUssQ0FBQyxDQUFDO2FBQUk7WUFDN0MyZDtRQUNGO0lBQ0Y7SUFFUUMsc0JBQXVEO1FBQzdELE1BQU1LLFNBQW1CLEVBQUU7UUFDM0IsTUFBTUMsa0JBQTRCLEVBQUU7UUFFcEMsUUFBUTtRQUNSLE1BQU1DLGtCQUFrQixJQUFJLENBQUMxVCxXQUFXLENBQUMxSyxNQUFNLEdBQUcsSUFDOUMsSUFBSSxDQUFDMEssV0FBVyxDQUFDN0osTUFBTSxDQUFDOEosQ0FBQUEsT0FBUUEsS0FBS25JLE1BQU0sS0FBSyxTQUFTeEMsTUFBTSxHQUFHLElBQUksQ0FBQzBLLFdBQVcsQ0FBQzFLLE1BQU0sR0FDekY7UUFFSixJQUFJb2Usa0JBQWtCLEtBQUs7WUFDekJGLE9BQU9uZSxJQUFJLENBQUMsVUFBNkMsT0FBbkMsQ0FBQ3FlLGtCQUFrQixHQUFFLEVBQUdDLE9BQU8sQ0FBQyxJQUFHO1lBQ3pERixnQkFBZ0JwZSxJQUFJLENBQUM7UUFDdkI7UUFFQSxTQUFTO1FBQ1QsSUFBSSxJQUFJLENBQUMwSixrQkFBa0IsQ0FBQ0ksbUJBQW1CLEdBQUcsTUFBTTtZQUN0RHFVLE9BQU9uZSxJQUFJLENBQUMsYUFBb0UsT0FBdkQsSUFBSSxDQUFDMEosa0JBQWtCLENBQUNJLG1CQUFtQixDQUFDd1UsT0FBTyxDQUFDLElBQUc7WUFDaEZGLGdCQUFnQnBlLElBQUksQ0FBQztRQUN2QjtRQUVBLFVBQVU7UUFDVixJQUFJLElBQUksQ0FBQzBKLGtCQUFrQixDQUFDUSxZQUFZLEdBQUcsS0FBSztZQUM5Q2lVLE9BQU9uZSxJQUFJLENBQUMsWUFBb0UsT0FBeEQsQ0FBQyxJQUFJLENBQUMwSixrQkFBa0IsQ0FBQ1EsWUFBWSxHQUFHLEdBQUUsRUFBR29VLE9BQU8sQ0FBQyxJQUFHO1lBQ2hGRixnQkFBZ0JwZSxJQUFJLENBQUM7UUFDdkI7UUFFQSxTQUFTO1FBQ1QsTUFBTXVlLGNBQWN2aEIsZ0ZBQXdCQSxDQUFDb2IsU0FBUztRQUN0RCxJQUFJbUcsWUFBWUwsTUFBTSxHQUFHLElBQUk7WUFDM0JDLE9BQU9uZSxJQUFJLENBQUMsYUFBZ0MsT0FBbkJ1ZSxZQUFZTCxNQUFNLEVBQUM7WUFDNUNFLGdCQUFnQnBlLElBQUksQ0FBQztRQUN2QjtRQUVBLFNBQVM7UUFDVCxJQUFJeUMsU0FBNkM7UUFDakQsSUFBSTBiLE9BQU9sZSxNQUFNLEdBQUcsR0FBRztZQUNyQndDLFNBQVM0YixrQkFBa0IsT0FBTyxJQUFJLENBQUMzVSxrQkFBa0IsQ0FBQ0ksbUJBQW1CLEdBQUcsT0FBTyxhQUFhO1FBQ3RHO1FBRUEsT0FBTztZQUFFckg7WUFBUTBiO1lBQVFDO1FBQWdCO0lBQzNDO0lBRUFJLDBCQUFnQztRQUM5QixJQUFJLENBQUMvVSwrQkFBK0I7UUFDcEN6SCxRQUFRcEMsR0FBRyxDQUFDO0lBQ2Q7SUFFQTZlLGlCQUFzRTtZQUF2RGhXLFFBQUFBLGlFQUFnQjtRQUM3QixPQUFPLElBQUksQ0FBQ2lCLGtCQUFrQixDQUFDUyxXQUFXLENBQ3ZDbkgsSUFBSSxDQUFDLENBQUN3RCxHQUFHQyxJQUFNQSxFQUFFakgsUUFBUSxHQUFHZ0gsRUFBRWhILFFBQVEsRUFDdENVLEtBQUssQ0FBQyxHQUFHdUk7SUFDZDtJQUVBaVcsc0JBTUc7UUFDRCxPQUFPbFosTUFBTWxDLElBQUksQ0FBQyxJQUFJLENBQUM4RyxXQUFXLENBQUM3RSxPQUFPLElBQ3ZDL0QsR0FBRyxDQUFDO2dCQUFDLENBQUNsQyxRQUFRcUIsTUFBTTttQkFBTTtnQkFDekJyQjtnQkFDQW9NLE9BQU8vSyxNQUFNK0ssS0FBSztnQkFDbEJ6SyxhQUFhTixNQUFNTSxXQUFXO2dCQUM5QmlMLGFBQWF2TCxNQUFNdUwsV0FBVztnQkFDOUJDLFVBQVUsSUFBSTdMLEtBQUtLLE1BQU13TCxRQUFRLEVBQUV3UyxjQUFjO1lBQ25EO1dBQ0MzYixJQUFJLENBQUMsQ0FBQ3dELEdBQUdDLElBQU1BLEVBQUVpRixLQUFLLEdBQUdsRixFQUFFa0YsS0FBSyxFQUFFLFVBQVU7O0lBQ2pEO0lBRUFrVCwwQkFNRTtRQUNBLE9BQU87WUFDTC9lLFdBQVcsSUFBSVMsT0FBTytXLFdBQVc7WUFDakN3SCxTQUFTLElBQUksQ0FBQ25CLHFCQUFxQjtZQUNuQ29CLFVBQVUsSUFBSSxDQUFDbkIsa0JBQWtCO1lBQ2pDb0IsWUFBWSxJQUFJLENBQUNMLG1CQUFtQixHQUFHeGUsS0FBSyxDQUFDLEdBQUc7WUFDaERpSyxhQUFhLElBQUksQ0FBQ3NVLGNBQWMsQ0FBQztRQUNuQztJQUNGO0lBRUEsaURBQWlEO0lBRWpEOzs7R0FHQyxHQUNELElBQUluRCxRQUFRO1FBQ1YsT0FBTztZQUNMLFdBQVc7WUFDWEMsd0JBQXdCLE9BQU8vZDtnQkFDN0IsTUFBTW1mLFVBQVUsRUFBRTtnQkFDbEIsTUFBTXZRLFNBQVMsRUFBRTtnQkFFakIsS0FBSyxNQUFNNFMsU0FBU3hoQixPQUFRO29CQUMxQixJQUFJO3dCQUNGLE1BQU1nRixTQUFTLE1BQU0sSUFBSSxDQUFDcVUsZ0JBQWdCLENBQUMxSixNQUFNLENBQUM2Ujt3QkFDbEQsSUFBSXhjLE9BQU9DLE1BQU0sS0FBSyxXQUFXOzRCQUMvQmthLFFBQVEzYyxJQUFJLENBQUN3QyxPQUFPc0IsSUFBSTt3QkFDMUIsT0FBTzs0QkFDTHNJLE9BQU9wTSxJQUFJLENBQUN3QyxPQUFPa0ssT0FBTyxJQUFJO3dCQUNoQztvQkFDRixFQUFFLE9BQU9oTixPQUFPO3dCQUNkME0sT0FBT3BNLElBQUksQ0FBQ04saUJBQWlCK00sUUFBUS9NLE1BQU1nTixPQUFPLEdBQUc7b0JBQ3ZEO2dCQUNGO2dCQUVBLElBQUlOLE9BQU9uTSxNQUFNLEtBQUssR0FBRztvQkFDdkIsT0FBTzt3QkFDTHdDLFFBQVE7d0JBQ1JxQixNQUFNNlk7d0JBQ05qUSxTQUFTLE9BQXNCLE9BQWZpUSxRQUFRMWMsTUFBTSxFQUFDO29CQUNqQztnQkFDRixPQUFPO29CQUNMLE9BQU87d0JBQ0x3QyxRQUFRO3dCQUNScUIsTUFBTTZZO3dCQUNOalEsU0FBUyxjQUFnQyxPQUFsQk4sT0FBT3VPLElBQUksQ0FBQzt3QkFDbkN2TztvQkFDRjtnQkFDRjtZQUNGO1lBRUEsV0FBVztZQUNYcVAsd0JBQXdCLE9BQU9uTztnQkFDN0IsTUFBTXFQLFVBQVUsRUFBRTtnQkFDbEIsTUFBTXZRLFNBQVMsRUFBRTtnQkFFakIsS0FBSyxNQUFNLEVBQUVVLEVBQUUsRUFBRWhKLElBQUksRUFBRSxJQUFJd0osUUFBUztvQkFDbEMsSUFBSTt3QkFDRixNQUFNOUssU0FBUyxNQUFNLElBQUksQ0FBQ3FVLGdCQUFnQixDQUFDeEosTUFBTSxDQUFDUCxJQUFJaEo7d0JBQ3RELElBQUl0QixPQUFPQyxNQUFNLEtBQUssV0FBVzs0QkFDL0JrYSxRQUFRM2MsSUFBSSxDQUFDd0MsT0FBT3NCLElBQUk7d0JBQzFCLE9BQU87NEJBQ0xzSSxPQUFPcE0sSUFBSSxDQUFDLE1BQWF3QyxPQUFQc0ssSUFBRyxNQUE2QixPQUF6QnRLLE9BQU9rSyxPQUFPLElBQUk7d0JBQzdDO29CQUNGLEVBQUUsT0FBT2hOLE9BQU87d0JBQ2QwTSxPQUFPcE0sSUFBSSxDQUFDLE1BQWFOLE9BQVBvTixJQUFHLE1BQW9ELE9BQWhEcE4saUJBQWlCK00sUUFBUS9NLE1BQU1nTixPQUFPLEdBQUc7b0JBQ3BFO2dCQUNGO2dCQUVBLElBQUlOLE9BQU9uTSxNQUFNLEtBQUssR0FBRztvQkFDdkIsT0FBTzt3QkFDTHdDLFFBQVE7d0JBQ1JxQixNQUFNNlk7d0JBQ05qUSxTQUFTLE9BQXNCLE9BQWZpUSxRQUFRMWMsTUFBTSxFQUFDO29CQUNqQztnQkFDRixPQUFPO29CQUNMLE9BQU87d0JBQ0x3QyxRQUFRO3dCQUNScUIsTUFBTTZZO3dCQUNOalEsU0FBUyxjQUFnQyxPQUFsQk4sT0FBT3VPLElBQUksQ0FBQzt3QkFDbkN2TztvQkFDRjtnQkFDRjtZQUNGO1lBRUEsV0FBVztZQUNYNlMsd0JBQXdCLE9BQU9DO2dCQUM3QixNQUFNQyxhQUFhLEVBQUU7Z0JBQ3JCLE1BQU0vUyxTQUFTLEVBQUU7Z0JBRWpCLEtBQUssTUFBTVUsTUFBTW9TLElBQUs7b0JBQ3BCLElBQUk7d0JBQ0YsTUFBTTFjLFNBQVMsTUFBTSxJQUFJLENBQUNxVSxnQkFBZ0IsQ0FBQ3hTLE1BQU0sQ0FBQ3lJO3dCQUNsRCxJQUFJdEssT0FBT0MsTUFBTSxLQUFLLFdBQVc7NEJBQy9CMGMsV0FBV25mLElBQUksQ0FBQzhNO3dCQUNsQixPQUFPOzRCQUNMVixPQUFPcE0sSUFBSSxDQUFDLE1BQWF3QyxPQUFQc0ssSUFBRyxNQUE2QixPQUF6QnRLLE9BQU9rSyxPQUFPLElBQUk7d0JBQzdDO29CQUNGLEVBQUUsT0FBT2hOLE9BQU87d0JBQ2QwTSxPQUFPcE0sSUFBSSxDQUFDLE1BQWFOLE9BQVBvTixJQUFHLE1BQW9ELE9BQWhEcE4saUJBQWlCK00sUUFBUS9NLE1BQU1nTixPQUFPLEdBQUc7b0JBQ3BFO2dCQUNGO2dCQUVBLElBQUlOLE9BQU9uTSxNQUFNLEtBQUssR0FBRztvQkFDdkIsT0FBTzt3QkFDTHdDLFFBQVE7d0JBQ1JxQixNQUFNO3dCQUNONEksU0FBUyxPQUF5QixPQUFsQnlTLFdBQVdsZixNQUFNLEVBQUM7b0JBQ3BDO2dCQUNGLE9BQU87b0JBQ0wsT0FBTzt3QkFDTHdDLFFBQVE7d0JBQ1JxQixNQUFNO3dCQUNONEksU0FBUyxjQUFnQyxPQUFsQk4sT0FBT3VPLElBQUksQ0FBQzt3QkFDbkN2TztvQkFDRjtnQkFDRjtZQUNGO1lBRUEsV0FBVztZQUNYZ1QscUJBQXFCLE9BQU9GO2dCQUMxQixNQUFNdkMsVUFBVSxFQUFFO2dCQUNsQixNQUFNdlEsU0FBUyxFQUFFO2dCQUVqQixLQUFLLE1BQU1VLE1BQU1vUyxJQUFLO29CQUNwQixJQUFJO3dCQUNGLE1BQU0xYyxTQUFTLE1BQU0sSUFBSSxDQUFDcVUsZ0JBQWdCLENBQUNoSyxPQUFPLENBQUNDO3dCQUNuRCxJQUFJdEssT0FBT0MsTUFBTSxLQUFLLFdBQVc7NEJBQy9Ca2EsUUFBUTNjLElBQUksQ0FBQ3dDLE9BQU9zQixJQUFJO3dCQUMxQixPQUFPOzRCQUNMc0ksT0FBT3BNLElBQUksQ0FBQyxNQUFhd0MsT0FBUHNLLElBQUcsTUFBNkIsT0FBekJ0SyxPQUFPa0ssT0FBTyxJQUFJO3dCQUM3QztvQkFDRixFQUFFLE9BQU9oTixPQUFPO3dCQUNkME0sT0FBT3BNLElBQUksQ0FBQyxNQUFhTixPQUFQb04sSUFBRyxNQUFvRCxPQUFoRHBOLGlCQUFpQitNLFFBQVEvTSxNQUFNZ04sT0FBTyxHQUFHO29CQUNwRTtnQkFDRjtnQkFFQSxJQUFJTixPQUFPbk0sTUFBTSxLQUFLLEdBQUc7b0JBQ3ZCLE9BQU87d0JBQ0x3QyxRQUFRO3dCQUNScUIsTUFBTTZZO3dCQUNOalEsU0FBUyxPQUFzQixPQUFmaVEsUUFBUTFjLE1BQU0sRUFBQztvQkFDakM7Z0JBQ0YsT0FBTztvQkFDTCxPQUFPO3dCQUNMd0MsUUFBUTt3QkFDUnFCLE1BQU02WTt3QkFDTmpRLFNBQVMsY0FBZ0MsT0FBbEJOLE9BQU91TyxJQUFJLENBQUM7d0JBQ25Ddk87b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO0lBQ0Y7SUFtQkE7O0dBRUMsR0FDRGlULFlBQVk7UUFDVixPQUFPO1lBQUUsR0FBRyxJQUFJLENBQUNDLFlBQVk7UUFBQztJQUNoQztJQUVBOztHQUVDLEdBQ0R2RixhQUFhN2EsTUFBeUMsRUFBRTtRQUN0RCxJQUFJLENBQUNvZ0IsWUFBWSxHQUFHO1lBQUUsR0FBRyxJQUFJLENBQUNBLFlBQVk7WUFBRSxHQUFHcGdCLE1BQU07UUFBQztRQUN0RDhDLFFBQVFwQyxHQUFHLENBQUMsZ0NBQWdDVjtRQUU1QyxnQkFBZ0I7UUFDaEIsSUFBSUEsT0FBTzlCLGFBQWEsS0FBS21pQixXQUFXO1lBQ3RDdmQsUUFBUXBDLEdBQUcsQ0FBQyw2QkFBa0UsT0FBckNWLE9BQU85QixhQUFhLEdBQUcsUUFBUTtRQUMxRTtRQUVBLElBQUk4QixPQUFPc2dCLFlBQVksS0FBS0QsV0FBVztZQUNyQ3ZkLFFBQVFwQyxHQUFHLENBQUMsK0JBQW1ELE9BQXBCVixPQUFPc2dCLFlBQVksRUFBQztRQUNqRTtRQUVBLElBQUl0Z0IsT0FBT04sYUFBYSxLQUFLMmdCLFdBQVc7WUFDdEN2ZCxRQUFRcEMsR0FBRyxDQUFDLDZCQUFrRSxPQUFyQ1YsT0FBT04sYUFBYSxHQUFHLFFBQVE7UUFDMUU7UUFFQSxJQUFJTSxPQUFPdWdCLFFBQVEsS0FBS0YsV0FBVztZQUNqQ3ZkLFFBQVFwQyxHQUFHLENBQUMsNkJBQTZDLE9BQWhCVixPQUFPdWdCLFFBQVE7UUFDMUQ7SUFDRjtJQUVBOztHQUVDLEdBQ0RDLGNBQWM7UUFDWixJQUFJLENBQUNKLFlBQVksR0FBRztZQUNsQmxpQixlQUFlO1lBQ2ZvaUIsY0FBYyxJQUFJLEtBQUs7WUFDdkI1Z0IsZUFBZTtZQUNmNmdCLFVBQVU7WUFDVkUsNkJBQTZCO1lBQzdCQyx1QkFBdUI7WUFDdkI5Z0IsZUFBZTtZQUNmQyxZQUFZO1FBQ2Q7UUFDQWlELFFBQVFwQyxHQUFHLENBQUM7SUFDZDtJQTkzRUEsWUFBb0JWLFNBQW9DLENBQUMsQ0FBQyxDQUFFO2FBMUVwRGEsYUFBMEIsRUFBRTtRQUNwQyxnQkFBZ0I7YUFDUm1FLFFBQWlDLElBQUltRzthQUNyQzVGLGNBQTJCdEg7YUFDM0JnSyxlQUFlLElBQUlrRDthQUNuQnpFLGFBQWE7WUFDbkIwQixNQUFNO1lBQ05FLFFBQVE7WUFDUnNSLGVBQWU7WUFDZmpULFdBQVc7WUFDWHlDLGFBQWE7UUFDZjtRQUNBLGNBQWM7YUFDTnpCLHFCQUE0QyxJQUFJd0QsTUFBTSxTQUFTOzthQUMvRHpDLGVBQXNDO2FBQ3RDZ0Isa0JBQWtCO2FBQ2xCWixpQkFBaUI7UUFFekIsV0FBVzthQUNIMEIscUJBQXlDO1lBQy9DQyxZQUFZO1lBQ1pDLGNBQWM7WUFDZEMsWUFBWTtZQUNaQyxxQkFBcUI7WUFDckJDLGlCQUFpQjtZQUNqQkUsaUJBQWlCO1lBQ2pCQyxjQUFjO1lBQ2RDLGFBQWEsRUFBRTtZQUNmQyxhQUFhLElBQUlDO1lBQ2pCQyxhQUFhLEVBQUU7UUFDakI7YUFDUUssY0FNSCxFQUFFO2FBQ0NQLGNBT0gsSUFBSUM7UUFFVCxlQUFlO2FBQ1BoSyxtQkFPSCxFQUFFO2FBQ0NFLG1CQUEwQzthQUNqQ0csd0JBQXdCLEtBQUssV0FBVzs7UUErMEV6RCxpREFBaUQ7UUFFakQ7OztHQUdDLFFBQ080ZSxlQUFlO1lBQ3JCbGlCLGVBQWU7WUFDZm9pQixjQUFjLElBQUksS0FBSztZQUN2QjVnQixlQUFlO1lBQ2Y2Z0IsVUFBVTtZQUNWRSw2QkFBNkI7WUFDN0JDLHVCQUF1QjtZQUN2QjlnQixlQUFlO1lBQ2ZDLFlBQVk7UUFDZDtRQTUwRUUsSUFBSSxDQUFDRyxNQUFNLEdBQUc7WUFBRSxHQUFHUCxjQUFjO1lBQUUsR0FBR08sTUFBTTtRQUFDO1FBSzVDLElBQUssQ0FBU2dWLFdBQVcsR0FBR2hULEtBQUsyZSxNQUFNLEdBQUd0YyxRQUFRLENBQUMsSUFBSXVjLE1BQU0sQ0FBQyxHQUFHO1FBQ2xFOWQsUUFBUXBDLEdBQUcsQ0FBQyxrQ0FBa0MsSUFBSyxDQUFTc1UsV0FBVyxFQUFFO1FBRXpFLFlBQVk7UUFDWixJQUFJLElBQUksQ0FBQ2hWLE1BQU0sQ0FBQzlCLGFBQWEsRUFBRTtZQUM3QjRFLFFBQVFwQyxHQUFHLENBQUM7WUFFWixnQkFBZ0I7WUFDaEIsSUFBSSxDQUFDNkgseUJBQXlCO1FBQ2hDLE9BQU87WUFDTHpGLFFBQVFwQyxHQUFHLENBQUM7UUFDZDtRQUVBLFVBQVU7UUFDVixJQUFJLENBQUM2SiwrQkFBK0I7UUFFcEMsVUFBVTtRQUNWLElBQUksQ0FBQ2tELGNBQWMsR0FBRzNRLCtFQUF3QkEsQ0FBQ2lELFdBQVc7UUFDMUQsSUFBSSxDQUFDb1AsZUFBZSxHQUFHcFMsaUZBQXlCQSxDQUFDZ0QsV0FBVztRQUM1RCxJQUFJLENBQUM0UCxlQUFlLEdBQUczUyxpRkFBeUJBLENBQUMrQyxXQUFXO1FBQzVELElBQUksQ0FBQzRRLGdCQUFnQixHQUFHMVQsbUZBQTBCQSxDQUFDOEMsV0FBVztRQUM5RCxJQUFJLENBQUNrVixZQUFZLEdBQUcvWCwyRUFBc0JBLENBQUM2QyxXQUFXO1FBQ3REK0MsUUFBUXBDLEdBQUcsQ0FBQywyREFBaUQsSUFBSyxDQUFTc1UsV0FBVztRQUN0RmxTLFFBQVFwQyxHQUFHLENBQUMsc0RBQTRDLElBQUssQ0FBQ3VVLFlBQVksQ0FBU0MsV0FBVztRQUM5RixJQUFJLENBQUN1QixlQUFlLEdBQUd0WixpRkFBeUJBLENBQUM0QyxXQUFXO1FBQzVELElBQUksQ0FBQzZYLHNCQUFzQixHQUFHeGEsK0ZBQWdDQSxDQUFDMkMsV0FBVztRQUMxRSxJQUFJLENBQUN5WSwwQkFBMEIsR0FBR25iLHVHQUFvQ0EsQ0FBQzBDLFdBQVc7UUFDbEYsSUFBSSxDQUFDZ1osa0JBQWtCLEdBQUd6Yix1RkFBNEJBLENBQUN5QyxXQUFXO1FBQ2xFLElBQUksQ0FBQ3FhLHNCQUFzQixHQUFHN2MsK0ZBQWdDQSxDQUFDd0MsV0FBVztRQUMxRSxJQUFJLENBQUMwUixXQUFXLEdBQUdqVSwwRUFBcUJBLENBQUN1QyxXQUFXO1FBQ3BELElBQUksQ0FBQ2tVLFdBQVcsR0FBRyxJQUFJeFcsMEVBQXFCQTtRQUM1QyxJQUFJLENBQUM0VixzQkFBc0IsR0FBRzNWLDRFQUFzQkEsQ0FBQ3FDLFdBQVc7UUFDaEUrQyxRQUFRcEMsR0FBRyxDQUFDO1FBRVosY0FBYztRQUNkLElBQUksQ0FBQ3NaLGdDQUFnQztRQUVyQyxhQUFhO1FBRWIsSUFBSS9ZLElBQXlCLEVBQWU7WUFDMUM2QixRQUFRcEMsR0FBRyxDQUFDO2dCQUNWbWdCLFNBQVM5aUIsMERBQVdBO2dCQUNwQmlDLFFBQVEsSUFBSSxDQUFDQSxNQUFNO2dCQUNuQnJDLG9CQUFvQjtZQUN0QjtRQUNGO0lBQ0Y7QUEyMEVGO0FBRUEsU0FBUztBQUNGLE1BQU1takIsb0JBQW9CaGhCLGtCQUFrQkMsV0FBVyxHQUFFO0FBRWhFLHlDQUF5QztBQUN6QyxJQUFJLElBQWtCLEVBQWE7SUFDaENnaEIsT0FBZUQsaUJBQWlCLEdBQUdBO0lBQ3BDaGUsUUFBUXBDLEdBQUcsQ0FBQztBQUNkO0FBRU8sTUFBTSxFQUNYL0IsUUFBUSxFQUNSRyxTQUFTLEVBQ1RDLFNBQVMsRUFDVDJSLFNBQVMsRUFDVGlILGdCQUFnQixFQUNoQlksb0JBQW9CLEVBQ3BCM1osWUFBWSxFQUNac2IsZ0JBQWdCLEVBQ2hCNUksSUFBSSxFQUNKNEIsZUFBZSxFQUNoQixHQUFHNE4sa0JBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zZXJ2aWNlcy9kYXRhQWNjZXNzL0RhdGFBY2Nlc3NNYW5hZ2VyLnRzPzkzZWUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIOe7n+S4gOaVsOaNruiuv+mXrueuoeeQhuWZqFxyXG4gKiDmj5DkvpvljZXkuIDlhaXlj6Pngrnorr/pl67miYDmnInmlbDmja7mnI3liqHvvIzlrp7njrDmoIflh4bljJbnmoTot6jmqKHlnZfmlbDmja7osIPnlKhcclxuICovXHJcblxyXG5pbXBvcnQgeyBQcm9kdWN0RGF0YUFjY2Vzc1NlcnZpY2UgfSBmcm9tICcuL1Byb2R1Y3REYXRhQWNjZXNzU2VydmljZSdcclxuaW1wb3J0IHsgQ3VzdG9tZXJEYXRhQWNjZXNzU2VydmljZSB9IGZyb20gJy4vQ3VzdG9tZXJEYXRhQWNjZXNzU2VydmljZSdcclxuaW1wb3J0IHsgRW1wbG95ZWVEYXRhQWNjZXNzU2VydmljZSB9IGZyb20gJy4vRW1wbG95ZWVEYXRhQWNjZXNzU2VydmljZSdcclxuaW1wb3J0IHsgSW52ZW50b3J5RGF0YUFjY2Vzc1NlcnZpY2UgfSBmcm9tICcuL0ludmVudG9yeURhdGFBY2Nlc3NTZXJ2aWNlJ1xyXG5pbXBvcnQgeyBPcmRlckRhdGFBY2Nlc3NTZXJ2aWNlIH0gZnJvbSAnLi9PcmRlckRhdGFBY2Nlc3NTZXJ2aWNlJ1xyXG5pbXBvcnQgeyBXb3JrVGltZURhdGFBY2Nlc3NTZXJ2aWNlIH0gZnJvbSAnLi9Xb3JrVGltZURhdGFBY2Nlc3NTZXJ2aWNlJ1xyXG5pbXBvcnQgeyBQcm9kdWN0aW9uT3JkZXJEYXRhQWNjZXNzU2VydmljZSB9IGZyb20gJy4vUHJvZHVjdGlvbk9yZGVyRGF0YUFjY2Vzc1NlcnZpY2UnXHJcbmltcG9ydCB7IFByb2R1Y3Rpb25Xb3JrT3JkZXJEYXRhQWNjZXNzU2VydmljZSB9IGZyb20gJy4vUHJvZHVjdGlvbldvcmtPcmRlckRhdGFBY2Nlc3NTZXJ2aWNlJ1xyXG5pbXBvcnQgeyBXb3Jrc3RhdGlvbkRhdGFBY2Nlc3NTZXJ2aWNlIH0gZnJvbSAnLi9Xb3Jrc3RhdGlvbkRhdGFBY2Nlc3NTZXJ2aWNlJ1xyXG5pbXBvcnQgeyBDb3N0Q2FsY3VsYXRpb25EYXRhQWNjZXNzU2VydmljZSB9IGZyb20gJy4vQ29zdENhbGN1bGF0aW9uRGF0YUFjY2Vzc1NlcnZpY2UnXHJcbmltcG9ydCB7IEF1dGhEYXRhQWNjZXNzU2VydmljZSwgSUF1dGhEYXRhQWNjZXNzIH0gZnJvbSAnLi9BdXRoRGF0YUFjY2Vzc1NlcnZpY2UnXHJcbmltcG9ydCB7IFJvbGVEYXRhQWNjZXNzU2VydmljZSwgSVJvbGVEYXRhQWNjZXNzIH0gZnJvbSAnLi9Sb2xlRGF0YUFjY2Vzc1NlcnZpY2UnXHJcbmltcG9ydCB7IFRva2VuTWFuYWdlbWVudFNlcnZpY2UgfSBmcm9tICcuL1Rva2VuTWFuYWdlbWVudFNlcnZpY2UnXHJcbmltcG9ydCB7IGRhdGFDaGFuZ2VOb3RpZmllciB9IGZyb20gJy4vRGF0YUNoYW5nZU5vdGlmaWVyJ1xyXG4vLyDinIUg5p625p6E5ZCI6KeE77ya5bey56e76Zmk54us56uLQ2FjaGVTdHJhdGVneU1hbmFnZXLvvIzkvb/nlKjlhoXnva7nvJPlrZjns7vnu59cclxuaW1wb3J0IHsgcGVyZm9ybWFuY2VPcHRpbWl6ZXIgfSBmcm9tICcuL1BlcmZvcm1hbmNlT3B0aW1pemVyJ1xyXG5pbXBvcnQgeyBkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yIH0gZnJvbSAnLi9EYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yJ1xyXG4vLyDinIUg5p625p6E5ZCI6KeE77ya56e76Zmk6L+d6KeE55qE54us56uL57yT5a2Y566h55CG5Zmo77yM5L2/55So5YaF572u57yT5a2YXHJcbmltcG9ydCB7IGJhdGNoT3BlcmF0aW9uQ29udHJvbGxlciB9IGZyb20gJ0AvdXRpbHMvY29uY3VycmVuY3lDb250cm9sJ1xyXG5cclxuLyoqXHJcbiAqIOaAp+iDveebkeaOp+aMh+agh+aOpeWPo1xyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBQZXJmb3JtYW5jZU1ldHJpY3Mge1xyXG4gIHRvdGFsQ2FsbHM6IG51bWJlclxyXG4gIHN1Y2Nlc3NDYWxsczogbnVtYmVyXHJcbiAgZXJyb3JDYWxsczogbnVtYmVyXHJcbiAgYXZlcmFnZVJlc3BvbnNlVGltZTogbnVtYmVyXHJcbiAgbWluUmVzcG9uc2VUaW1lOiBudW1iZXJcclxuICBtYXhSZXNwb25zZVRpbWU6IG51bWJlclxyXG4gIGNhY2hlSGl0UmF0ZTogbnVtYmVyXHJcbiAgc2xvd1F1ZXJpZXM6IEFycmF5PHtcclxuICAgIG1ldGhvZDogc3RyaW5nXHJcbiAgICBwYXJhbXM6IGFueVxyXG4gICAgZHVyYXRpb246IG51bWJlclxyXG4gICAgdGltZXN0YW1wOiBudW1iZXJcclxuICAgIGNhY2hlZD86IGJvb2xlYW5cclxuICB9PlxyXG4gIG1ldGhvZFN0YXRzOiBNYXA8c3RyaW5nLCB7XHJcbiAgICBjYWxsczogbnVtYmVyXHJcbiAgICB0b3RhbFRpbWU6IG51bWJlclxyXG4gICAgYXZlcmFnZVRpbWU6IG51bWJlclxyXG4gICAgc3VjY2Vzc1JhdGU6IG51bWJlclxyXG4gICAgbGFzdENhbGw6IG51bWJlclxyXG4gIH0+XHJcbiAgaG91cmx5U3RhdHM6IEFycmF5PHtcclxuICAgIGhvdXI6IHN0cmluZ1xyXG4gICAgY2FsbHM6IG51bWJlclxyXG4gICAgYXZlcmFnZVRpbWU6IG51bWJlclxyXG4gICAgZXJyb3JSYXRlOiBudW1iZXJcclxuICB9PlxyXG59XHJcblxyXG4vKipcclxuICog5a6e5pe25oCn6IO955uR5o6n5o6l5Y+jXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIFJlYWxUaW1lTWV0cmljcyB7XHJcbiAgY3VycmVudENvbmN1cnJlbmN5OiBudW1iZXJcclxuICBxdWV1ZUxlbmd0aDogbnVtYmVyXHJcbiAgcmVjZW50Q2FsbHM6IEFycmF5PHtcclxuICAgIG1ldGhvZDogc3RyaW5nXHJcbiAgICBkdXJhdGlvbjogbnVtYmVyXHJcbiAgICBzdGF0dXM6ICdzdWNjZXNzJyB8ICdlcnJvcidcclxuICAgIHRpbWVzdGFtcDogbnVtYmVyXHJcbiAgfT5cclxuICBzeXN0ZW1IZWFsdGg6IHtcclxuICAgIHN0YXR1czogJ2hlYWx0aHknIHwgJ3dhcm5pbmcnIHwgJ2NyaXRpY2FsJ1xyXG4gICAgaXNzdWVzOiBzdHJpbmdbXVxyXG4gICAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXVxyXG4gIH1cclxufVxyXG4vLyDkvJjlhYjnuqflkIzmraXmnI3liqHlt7LliKDpmaRcclxuaW1wb3J0IHtcclxuICBJUHJvZHVjdERhdGFBY2Nlc3MsXHJcbiAgSUN1c3RvbWVyRGF0YUFjY2VzcyxcclxuICBJRW1wbG95ZWVEYXRhQWNjZXNzLFxyXG4gIElJbnZlbnRvcnlEYXRhQWNjZXNzLFxyXG4gIElPcmRlckRhdGFBY2Nlc3MsXHJcbiAgQVBJX1ZFUlNJT05cclxufSBmcm9tICcuL0RhdGFBY2Nlc3NMYXllcidcclxuaW1wb3J0IHsgdGltZXN0YW1wR2VuZXJhdG9yIH0gZnJvbSAnQC91dGlscy9idXNpbmVzcydcclxuaW1wb3J0IHsgSVdvcmtUaW1lRGF0YUFjY2VzcyB9IGZyb20gJy4vV29ya1RpbWVEYXRhQWNjZXNzU2VydmljZSdcclxuaW1wb3J0IHsgSVByb2R1Y3Rpb25PcmRlckRhdGFBY2Nlc3MgfSBmcm9tICcuL1Byb2R1Y3Rpb25PcmRlckRhdGFBY2Nlc3NTZXJ2aWNlJ1xyXG5pbXBvcnQgeyBJUHJvZHVjdGlvbldvcmtPcmRlckRhdGFBY2Nlc3MgfSBmcm9tICcuL1Byb2R1Y3Rpb25Xb3JrT3JkZXJEYXRhQWNjZXNzU2VydmljZSdcclxuaW1wb3J0IHsgSVdvcmtzdGF0aW9uRGF0YUFjY2VzcyB9IGZyb20gJy4vRGF0YUFjY2Vzc0xheWVyJ1xyXG5pbXBvcnQgeyBJQ29zdENhbGN1bGF0aW9uRGF0YUFjY2VzcyB9IGZyb20gJy4vQ29zdENhbGN1bGF0aW9uRGF0YUFjY2Vzc1NlcnZpY2UnXHJcblxyXG4vKipcclxuICog57yT5a2Y5p2h55uu5o6l5Y+jIC0g5LyY5YyW54mIXHJcbiAqL1xyXG5pbnRlcmZhY2UgQ2FjaGVFbnRyeTxUID0gYW55PiB7XHJcbiAgZGF0YTogVFxyXG4gIGV4cGlyZXNBdDogbnVtYmVyXHJcbiAgYWNjZXNzQ291bnQ6IG51bWJlclxyXG4gIGxhc3RBY2Nlc3NlZDogbnVtYmVyXHJcbiAgY3JlYXRlZEF0OiBudW1iZXJcclxuICAvLyDinIUg5LyY5YyW77ya5pm66IO96L+H5pyf562W55Wl55u45YWz5a2X5q61XHJcbiAgcHJpb3JpdHk6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCcgfCAnY3JpdGljYWwnXHJcbiAgZGF0YVR5cGU6IHN0cmluZ1xyXG4gIGVzdGltYXRlZFNpemU6IG51bWJlclxyXG4gIGFjY2Vzc1BhdHRlcm46ICdmcmVxdWVudCcgfCAnb2NjYXNpb25hbCcgfCAncmFyZSdcclxuICByZWZyZXNoYWJsZTogYm9vbGVhblxyXG59XHJcblxyXG4vKipcclxuICog57yT5a2Y6YWN572u5o6l5Y+jIC0g5LyY5YyW54mIXHJcbiAqL1xyXG5leHBvcnQgaW50ZXJmYWNlIENhY2hlQ29uZmlnIHtcclxuICBlbmFibGVDYWNoaW5nOiBib29sZWFuXHJcbiAgZGVmYXVsdFRUTDogbnVtYmVyXHJcbiAgbWF4U2l6ZTogbnVtYmVyXHJcbiAgc3RyYXRlZ2llczoge1xyXG4gICAgW3NlcnZpY2VOYW1lOiBzdHJpbmddOiB7XHJcbiAgICAgIHR0bDogbnVtYmVyXHJcbiAgICAgIGVuYWJsZWQ6IGJvb2xlYW5cclxuICAgICAgcHJpb3JpdHk/OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnIHwgJ2NyaXRpY2FsJ1xyXG4gICAgICBtYXhFbnRyaWVzPzogbnVtYmVyXHJcbiAgICB9XHJcbiAgfVxyXG4gIG1vbml0b3Jpbmc6IHtcclxuICAgIGVuYWJsZWQ6IGJvb2xlYW5cclxuICAgIHJlcG9ydEludGVydmFsOiBudW1iZXJcclxuICB9XHJcbiAgLy8g4pyFIOS8mOWMlu+8muaZuuiDvee8k+WtmOeuoeeQhumFjee9rlxyXG4gIHNtYXJ0RXZpY3Rpb246IHtcclxuICAgIGVuYWJsZWQ6IGJvb2xlYW5cclxuICAgIGFsZ29yaXRobTogJ2xydScgfCAnbGZ1JyB8ICdhZGFwdGl2ZSdcclxuICAgIHRocmVzaG9sZHM6IHtcclxuICAgICAgbWVtb3J5V2FybmluZzogbnVtYmVyIC8vIOWGheWtmOS9v+eUqOeOh+itpuWRiumYiOWAvFxyXG4gICAgICBtZW1vcnlDcml0aWNhbDogbnVtYmVyIC8vIOWGheWtmOS9v+eUqOeOh+e0p+aApemYiOWAvFxyXG4gICAgfVxyXG4gIH1cclxuICAvLyDinIUg5LyY5YyW77ya57yT5a2Y6aKE54Ot6YWN572uXHJcbiAgcHJld2FybWluZzoge1xyXG4gICAgZW5hYmxlZDogYm9vbGVhblxyXG4gICAgc3RyYXRlZ2llczogc3RyaW5nW11cclxuICAgIHNjaGVkdWxlSW50ZXJ2YWw6IG51bWJlclxyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIOaVsOaNruiuv+mXrueuoeeQhuWZqOmFjee9riAtIOeugOWMlueJiFxyXG4gKi9cclxuZXhwb3J0IGludGVyZmFjZSBEYXRhQWNjZXNzQ29uZmlnIHtcclxuICBlbmFibGVMb2dnaW5nOiBib29sZWFuXHJcbiAgZW5hYmxlQ2FjaGluZzogYm9vbGVhblxyXG4gIGRlZmF1bHRUVEw6IG51bWJlclxyXG4gIG1heENhY2hlU2l6ZTogbnVtYmVyXHJcbiAgcmV0cnlBdHRlbXB0czogbnVtYmVyXHJcbiAgcmV0cnlEZWxheTogbnVtYmVyXHJcbn1cclxuXHJcbi8qKlxyXG4gKiDpu5jorqTnvJPlrZjphY3nva4gLSDkvJjljJbniYhcclxuICovXHJcbmV4cG9ydCBjb25zdCBERUZBVUxUX0NBQ0hFX0NPTkZJRzogQ2FjaGVDb25maWcgPSB7XHJcbiAgZW5hYmxlQ2FjaGluZzogdHJ1ZSxcclxuICBkZWZhdWx0VFRMOiA1ICogNjAgKiAxMDAwLCAvLyA15YiG6ZKfXHJcbiAgbWF4U2l6ZTogMTAwMCxcclxuICBzdHJhdGVnaWVzOiB7XHJcbiAgICBvcmRlcnM6IHsgdHRsOiAyICogNjAgKiAxMDAwLCBlbmFibGVkOiB0cnVlLCBwcmlvcml0eTogJ2hpZ2gnLCBtYXhFbnRyaWVzOiAyMDAgfSwgICAgICAvLyAy5YiG6ZKfXHJcbiAgICBwcm9kdWN0czogeyB0dGw6IDEwICogNjAgKiAxMDAwLCBlbmFibGVkOiB0cnVlLCBwcmlvcml0eTogJ21lZGl1bScsIG1heEVudHJpZXM6IDMwMCB9LCAgIC8vIDEw5YiG6ZKfXHJcbiAgICB3b3Jrc3RhdGlvbnM6IHsgdHRsOiAzMCAqIDEwMDAsIGVuYWJsZWQ6IHRydWUsIHByaW9yaXR5OiAnY3JpdGljYWwnLCBtYXhFbnRyaWVzOiA1MCB9LCAgICAvLyAzMOenklxyXG4gICAgc3RhdGlzdGljczogeyB0dGw6IDMwICogMTAwMCwgZW5hYmxlZDogdHJ1ZSwgcHJpb3JpdHk6ICdsb3cnLCBtYXhFbnRyaWVzOiAxMDAgfSwgICAgICAvLyAzMOenklxyXG4gICAgY3VzdG9tZXJzOiB7IHR0bDogMTUgKiA2MCAqIDEwMDAsIGVuYWJsZWQ6IHRydWUsIHByaW9yaXR5OiAnbWVkaXVtJywgbWF4RW50cmllczogMTUwIH0sICAvLyAxNeWIhumSn1xyXG4gICAgZW1wbG95ZWVzOiB7IHR0bDogMzAgKiA2MCAqIDEwMDAsIGVuYWJsZWQ6IHRydWUsIHByaW9yaXR5OiAnbWVkaXVtJywgbWF4RW50cmllczogMTAwIH0gICAvLyAzMOWIhumSn1xyXG4gIH0sXHJcbiAgbW9uaXRvcmluZzoge1xyXG4gICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgIHJlcG9ydEludGVydmFsOiA2MDAwMCAvLyAx5YiG6ZKfXHJcbiAgfSxcclxuICAvLyDinIUg5LyY5YyW77ya5pm66IO957yT5a2Y566h55CG6YWN572uXHJcbiAgc21hcnRFdmljdGlvbjoge1xyXG4gICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgIGFsZ29yaXRobTogJ2FkYXB0aXZlJyxcclxuICAgIHRocmVzaG9sZHM6IHtcclxuICAgICAgbWVtb3J5V2FybmluZzogMC43LCAvLyA3MCXlhoXlrZjkvb/nlKjnjoforablkYpcclxuICAgICAgbWVtb3J5Q3JpdGljYWw6IDAuODUgLy8gODUl5YaF5a2Y5L2/55So546H57Sn5oCl5riF55CGXHJcbiAgICB9XHJcbiAgfSxcclxuICAvLyDinIUg5LyY5YyW77ya57yT5a2Y6aKE54Ot6YWN572uXHJcbiAgcHJld2FybWluZzoge1xyXG4gICAgZW5hYmxlZDogdHJ1ZSxcclxuICAgIHN0cmF0ZWdpZXM6IFsnd29ya3N0YXRpb25zJywgJ29yZGVycycsICdwcm9kdWN0cyddLFxyXG4gICAgc2NoZWR1bGVJbnRlcnZhbDogMzAwMDAgLy8gMzDnp5LpooTng63pl7TpmpRcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiDpu5jorqTphY3nva4gLSDnroDljJbniYhcclxuICovXHJcbmNvbnN0IERFRkFVTFRfQ09ORklHOiBEYXRhQWNjZXNzQ29uZmlnID0ge1xyXG4gIGVuYWJsZUxvZ2dpbmc6IHRydWUsXHJcbiAgZW5hYmxlQ2FjaGluZzogdHJ1ZSxcclxuICBkZWZhdWx0VFRMOiA1ICogNjAgKiAxMDAwLCAvLyA15YiG6ZKfXHJcbiAgbWF4Q2FjaGVTaXplOiAxMDAwLFxyXG4gIHJldHJ5QXR0ZW1wdHM6IDMsXHJcbiAgcmV0cnlEZWxheTogMTAwMCAvLyAx56eSXHJcbn1cclxuXHJcbi8qKlxyXG4gKiDmlbDmja7orr/pl67ml6Xlv5forrDlvZVcclxuICovXHJcbmludGVyZmFjZSBBY2Nlc3NMb2cge1xyXG4gIHRpbWVzdGFtcDogc3RyaW5nXHJcbiAgc2VydmljZTogc3RyaW5nXHJcbiAgbWV0aG9kOiBzdHJpbmdcclxuICBwYXJhbXM/OiBhbnlcclxuICBkdXJhdGlvbjogbnVtYmVyXHJcbiAgc3VjY2VzczogYm9vbGVhblxyXG4gIGVycm9yPzogc3RyaW5nXHJcbn1cclxuXHJcbi8qKlxyXG4gKiDnvJPlrZjnu5/orqHkv6Hmga8gLSDkvJjljJbniYhcclxuICovXHJcbmludGVyZmFjZSBDYWNoZVN0YXRpc3RpY3Mge1xyXG4gIGVuYWJsZWQ6IGJvb2xlYW5cclxuICBzaXplOiBudW1iZXJcclxuICBoaXRzOiBudW1iZXJcclxuICBtaXNzZXM6IG51bWJlclxyXG4gIGhpdFJhdGU6IG51bWJlclxyXG4gIHRvdGFsUmVxdWVzdHM6IG51bWJlclxyXG4gIC8vIOKchSDkvJjljJbvvJrmlrDlop7nu5/orqHkv6Hmga9cclxuICBldmljdGlvbnM6IG51bWJlclxyXG4gIHByZXdhcm1IaXRzOiBudW1iZXJcclxuICBtZW1vcnlQcmVzc3VyZTogYm9vbGVhblxyXG4gIGF2ZXJhZ2VFbnRyeVNpemU6IG51bWJlclxyXG4gIHByaW9yaXR5RGlzdHJpYnV0aW9uOiB7XHJcbiAgICBjcml0aWNhbDogbnVtYmVyXHJcbiAgICBoaWdoOiBudW1iZXJcclxuICAgIG1lZGl1bTogbnVtYmVyXHJcbiAgICBsb3c6IG51bWJlclxyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIOe7n+S4gOaVsOaNruiuv+mXrueuoeeQhuWZqFxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIERhdGFBY2Nlc3NNYW5hZ2VyIHtcclxuICBwcml2YXRlIHN0YXRpYyBpbnN0YW5jZTogRGF0YUFjY2Vzc01hbmFnZXJcclxuICBwcml2YXRlIGNvbmZpZzogRGF0YUFjY2Vzc0NvbmZpZ1xyXG4gIHByaXZhdGUgYWNjZXNzTG9nczogQWNjZXNzTG9nW10gPSBbXVxyXG4gIC8vIOKchSDkvJjljJbvvJrlop7lvLrnvJPlrZjnrqHnkIblsZ7mgKdcclxuICBwcml2YXRlIGNhY2hlOiBNYXA8c3RyaW5nLCBDYWNoZUVudHJ5PiA9IG5ldyBNYXAoKVxyXG4gIHByaXZhdGUgY2FjaGVDb25maWc6IENhY2hlQ29uZmlnID0gREVGQVVMVF9DQUNIRV9DT05GSUdcclxuICBwcml2YXRlIHJlcXVlc3RDYWNoZSA9IG5ldyBNYXA8c3RyaW5nLCBQcm9taXNlPGFueT4+KClcclxuICBwcml2YXRlIGNhY2hlU3RhdHMgPSB7XHJcbiAgICBoaXRzOiAwLFxyXG4gICAgbWlzc2VzOiAwLFxyXG4gICAgdG90YWxSZXF1ZXN0czogMCxcclxuICAgIGV2aWN0aW9uczogMCxcclxuICAgIHByZXdhcm1IaXRzOiAwXHJcbiAgfVxyXG4gIC8vIOKchSDkvJjljJbvvJrmmbrog73nvJPlrZjnrqHnkIZcclxuICBwcml2YXRlIGNhY2hlQWNjZXNzSGlzdG9yeTogTWFwPHN0cmluZywgbnVtYmVyW10+ID0gbmV3IE1hcCgpIC8vIOiuv+mXruaXtumXtOWOhuWPslxyXG4gIHByaXZhdGUgcHJld2FybVRpbWVyOiBOb2RlSlMuVGltZW91dCB8IG51bGwgPSBudWxsXHJcbiAgcHJpdmF0ZSBsYXN0TWVtb3J5Q2hlY2sgPSAwXHJcbiAgcHJpdmF0ZSBtZW1vcnlQcmVzc3VyZSA9IGZhbHNlXHJcblxyXG4gIC8vIOaAp+iDveebkeaOp+ebuOWFs+WxnuaAp1xyXG4gIHByaXZhdGUgcGVyZm9ybWFuY2VNZXRyaWNzOiBQZXJmb3JtYW5jZU1ldHJpY3MgPSB7XHJcbiAgICB0b3RhbENhbGxzOiAwLFxyXG4gICAgc3VjY2Vzc0NhbGxzOiAwLFxyXG4gICAgZXJyb3JDYWxsczogMCxcclxuICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IDAsXHJcbiAgICBtaW5SZXNwb25zZVRpbWU6IDAsXHJcbiAgICBtYXhSZXNwb25zZVRpbWU6IDAsXHJcbiAgICBjYWNoZUhpdFJhdGU6IDAsXHJcbiAgICBzbG93UXVlcmllczogW10sXHJcbiAgICBtZXRob2RTdGF0czogbmV3IE1hcCgpLFxyXG4gICAgaG91cmx5U3RhdHM6IFtdXHJcbiAgfVxyXG4gIHByaXZhdGUgcmVjZW50Q2FsbHM6IEFycmF5PHtcclxuICAgIG1ldGhvZDogc3RyaW5nXHJcbiAgICBkdXJhdGlvbjogbnVtYmVyXHJcbiAgICBzdGF0dXM6ICdzdWNjZXNzJyB8ICdlcnJvcidcclxuICAgIHRpbWVzdGFtcDogbnVtYmVyXHJcbiAgICBjYWNoZWQ/OiBib29sZWFuXHJcbiAgfT4gPSBbXVxyXG4gIHByaXZhdGUgbWV0aG9kU3RhdHM6IE1hcDxzdHJpbmcsIHtcclxuICAgIGNhbGxzOiBudW1iZXJcclxuICAgIHRvdGFsVGltZTogbnVtYmVyXHJcbiAgICBhdmVyYWdlVGltZTogbnVtYmVyXHJcbiAgICBzdWNjZXNzUmF0ZTogbnVtYmVyXHJcbiAgICBsYXN0Q2FsbDogbnVtYmVyXHJcbiAgICBlcnJvcnM6IG51bWJlclxyXG4gIH0+ID0gbmV3IE1hcCgpXHJcblxyXG4gIC8vIPCflKcg5LyY5YyW77ya5om56YeP5oCn6IO95oql5ZGKXHJcbiAgcHJpdmF0ZSBwZXJmb3JtYW5jZUJhdGNoOiBBcnJheTx7XHJcbiAgICBzZXJ2aWNlOiBzdHJpbmdcclxuICAgIG1ldGhvZDogc3RyaW5nXHJcbiAgICBkdXJhdGlvbjogbnVtYmVyXHJcbiAgICBzdWNjZXNzOiBib29sZWFuXHJcbiAgICBmcm9tQ2FjaGU6IGJvb2xlYW5cclxuICAgIHRpbWVzdGFtcDogbnVtYmVyXHJcbiAgfT4gPSBbXVxyXG4gIHByaXZhdGUgYmF0Y2hSZXBvcnRUaW1lcjogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbFxyXG4gIHByaXZhdGUgcmVhZG9ubHkgQkFUQ0hfUkVQT1JUX0lOVEVSVkFMID0gNTAwMCAvLyA156eS5om56YeP5oql5ZGK5LiA5qyhXHJcbiAgXHJcbiAgLy8g5pyN5Yqh5a6e5L6LXHJcbiAgcHJpdmF0ZSBwcm9kdWN0U2VydmljZTogSVByb2R1Y3REYXRhQWNjZXNzXHJcbiAgcHJpdmF0ZSBjdXN0b21lclNlcnZpY2U6IElDdXN0b21lckRhdGFBY2Nlc3NcclxuICBwcml2YXRlIGVtcGxveWVlU2VydmljZTogSUVtcGxveWVlRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgaW52ZW50b3J5U2VydmljZTogSUludmVudG9yeURhdGFBY2Nlc3NcclxuICBwcml2YXRlIG9yZGVyU2VydmljZTogSU9yZGVyRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgd29ya1RpbWVTZXJ2aWNlOiBJV29ya1RpbWVEYXRhQWNjZXNzXHJcbiAgcHJpdmF0ZSBwcm9kdWN0aW9uT3JkZXJTZXJ2aWNlOiBJUHJvZHVjdGlvbk9yZGVyRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgcHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2U6IElQcm9kdWN0aW9uV29ya09yZGVyRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgd29ya3N0YXRpb25TZXJ2aWNlOiBJV29ya3N0YXRpb25EYXRhQWNjZXNzXHJcbiAgcHJpdmF0ZSBjb3N0Q2FsY3VsYXRpb25TZXJ2aWNlOiBJQ29zdENhbGN1bGF0aW9uRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgYXV0aFNlcnZpY2U6IElBdXRoRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgcm9sZVNlcnZpY2U6IElSb2xlRGF0YUFjY2Vzc1xyXG4gIHByaXZhdGUgdG9rZW5NYW5hZ2VtZW50U2VydmljZTogVG9rZW5NYW5hZ2VtZW50U2VydmljZVxyXG5cclxuICBwcml2YXRlIGNvbnN0cnVjdG9yKGNvbmZpZzogUGFydGlhbDxEYXRhQWNjZXNzQ29uZmlnPiA9IHt9KSB7XHJcbiAgICB0aGlzLmNvbmZpZyA9IHsgLi4uREVGQVVMVF9DT05GSUcsIC4uLmNvbmZpZyB9XHJcblxyXG4gICAgLy8g4pyFIOaetuaehOWQiOinhO+8muS9v+eUqOWGhee9rue8k+WtmOezu+e7n++8jOenu+mZpOi/neinhOeahENhY2hlU3RyYXRlZ3lNYW5hZ2VyXHJcblxyXG4gICAgLy8g5Li6RGF0YUFjY2Vzc01hbmFnZXLlrp7kvovmt7vliqDllK/kuIDmoIfor4ZcclxuICAgIDsodGhpcyBhcyBhbnkpLl9fbWFuYWdlcklkID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpXHJcbiAgICBjb25zb2xlLmxvZygn4pyFIFtEYXRhQWNjZXNzTWFuYWdlcl0g5Yib5bu65a6e5L6L77yMSUQ6JywgKHRoaXMgYXMgYW55KS5fX21hbmFnZXJJZCwgJ+WGhee9rue8k+WtmOezu+e7n+W3suWQr+eUqCcpXHJcblxyXG4gICAgLy8g5Yid5aeL5YyW5YaF572u57yT5a2Y57O757ufXHJcbiAgICBpZiAodGhpcy5jb25maWcuZW5hYmxlQ2FjaGluZykge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UpyBbRGF0YUFjY2Vzc01hbmFnZXJdIOWGhee9rue8k+WtmOezu+e7n+W3suWQr+eUqCcpXHJcbiAgICAgIFxyXG4gICAgICAvLyDinIUg5LyY5YyW77ya5ZCv5Yqo5pm66IO957yT5a2Y566h55CGXHJcbiAgICAgIHRoaXMuaW5pdGlhbGl6ZUFkdmFuY2VkQ2FjaGluZygpXHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UpyBbRGF0YUFjY2Vzc01hbmFnZXJdIOe8k+WtmOW3suemgeeUqCcpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5Yid5aeL5YyW5oCn6IO955uR5o6nXHJcbiAgICB0aGlzLmluaXRpYWxpemVQZXJmb3JtYW5jZU1vbml0b3JpbmcoKVxyXG5cclxuICAgIC8vIOWIneWni+WMluacjeWKoeWunuS+i1xyXG4gICAgdGhpcy5wcm9kdWN0U2VydmljZSA9IFByb2R1Y3REYXRhQWNjZXNzU2VydmljZS5nZXRJbnN0YW5jZSgpXHJcbiAgICB0aGlzLmN1c3RvbWVyU2VydmljZSA9IEN1c3RvbWVyRGF0YUFjY2Vzc1NlcnZpY2UuZ2V0SW5zdGFuY2UoKVxyXG4gICAgdGhpcy5lbXBsb3llZVNlcnZpY2UgPSBFbXBsb3llZURhdGFBY2Nlc3NTZXJ2aWNlLmdldEluc3RhbmNlKClcclxuICAgIHRoaXMuaW52ZW50b3J5U2VydmljZSA9IEludmVudG9yeURhdGFBY2Nlc3NTZXJ2aWNlLmdldEluc3RhbmNlKClcclxuICAgIHRoaXMub3JkZXJTZXJ2aWNlID0gT3JkZXJEYXRhQWNjZXNzU2VydmljZS5nZXRJbnN0YW5jZSgpXHJcbiAgICBjb25zb2xlLmxvZygn8J+UpyBbRGF0YUFjY2Vzc01hbmFnZXJdIOWIneWni+WMlk9yZGVyU2VydmljZe+8jOeuoeeQhuWZqElEOicsICh0aGlzIGFzIGFueSkuX19tYW5hZ2VySWQpXHJcbiAgICBjb25zb2xlLmxvZygn8J+UpyBbRGF0YUFjY2Vzc01hbmFnZXJdIE9yZGVyU2VydmljZeWunuS+i0lEOicsICh0aGlzLm9yZGVyU2VydmljZSBhcyBhbnkpLl9fc2VydmljZUlkKVxyXG4gICAgdGhpcy53b3JrVGltZVNlcnZpY2UgPSBXb3JrVGltZURhdGFBY2Nlc3NTZXJ2aWNlLmdldEluc3RhbmNlKClcclxuICAgIHRoaXMucHJvZHVjdGlvbk9yZGVyU2VydmljZSA9IFByb2R1Y3Rpb25PcmRlckRhdGFBY2Nlc3NTZXJ2aWNlLmdldEluc3RhbmNlKClcclxuICAgIHRoaXMucHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UgPSBQcm9kdWN0aW9uV29ya09yZGVyRGF0YUFjY2Vzc1NlcnZpY2UuZ2V0SW5zdGFuY2UoKVxyXG4gICAgdGhpcy53b3Jrc3RhdGlvblNlcnZpY2UgPSBXb3Jrc3RhdGlvbkRhdGFBY2Nlc3NTZXJ2aWNlLmdldEluc3RhbmNlKClcclxuICAgIHRoaXMuY29zdENhbGN1bGF0aW9uU2VydmljZSA9IENvc3RDYWxjdWxhdGlvbkRhdGFBY2Nlc3NTZXJ2aWNlLmdldEluc3RhbmNlKClcclxuICAgIHRoaXMuYXV0aFNlcnZpY2UgPSBBdXRoRGF0YUFjY2Vzc1NlcnZpY2UuZ2V0SW5zdGFuY2UoKVxyXG4gICAgdGhpcy5yb2xlU2VydmljZSA9IG5ldyBSb2xlRGF0YUFjY2Vzc1NlcnZpY2UoKVxyXG4gICAgdGhpcy50b2tlbk1hbmFnZW1lbnRTZXJ2aWNlID0gVG9rZW5NYW5hZ2VtZW50U2VydmljZS5nZXRJbnN0YW5jZSgpXHJcbiAgICBjb25zb2xlLmxvZygn8J+UpyBbRGF0YUFjY2Vzc01hbmFnZXJdIOiupOivgeacjeWKoeOAgeinkuiJsuacjeWKoeWSjFRva2Vu566h55CG5pyN5Yqh5bey5Yid5aeL5YyWJylcclxuXHJcbiAgICAvLyDliJ3lp4vljJbmlbDmja7lj5jmm7TpgJrnn6XmnLrliLZcclxuICAgIHRoaXMuaW5pdGlhbGl6ZURhdGFDaGFuZ2VOb3RpZmljYXRpb24oKVxyXG5cclxuICAgIC8vIOS8mOWFiOe6p+WQjOatpeacjeWKoeW3suWIoOmZpFxyXG5cclxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xyXG4gICAgICBjb25zb2xlLmxvZyh7XHJcbiAgICAgICAgdmVyc2lvbjogQVBJX1ZFUlNJT04sXHJcbiAgICAgICAgY29uZmlnOiB0aGlzLmNvbmZpZyxcclxuICAgICAgICBkYXRhQ2hhbmdlTm90aWZpZXI6ICflt7LlkK/nlKgnXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDojrflj5bljZXkvovlrp7kvotcclxuICAgKi9cclxuICBwdWJsaWMgc3RhdGljIGdldEluc3RhbmNlKGNvbmZpZz86IFBhcnRpYWw8RGF0YUFjY2Vzc0NvbmZpZz4pOiBEYXRhQWNjZXNzTWFuYWdlciB7XHJcbiAgICBpZiAoIURhdGFBY2Nlc3NNYW5hZ2VyLmluc3RhbmNlKSB7XHJcbiAgICAgIERhdGFBY2Nlc3NNYW5hZ2VyLmluc3RhbmNlID0gbmV3IERhdGFBY2Nlc3NNYW5hZ2VyKGNvbmZpZylcclxuICAgIH1cclxuICAgIHJldHVybiBEYXRhQWNjZXNzTWFuYWdlci5pbnN0YW5jZVxyXG4gIH1cclxuXHJcbiAgLy8g5LyY5YWI57qn5ZCM5q2l5pyN5Yqh5bey5Yig6ZmkXHJcblxyXG4gIC8qKlxyXG4gICAqIOiusOW9leiuv+mXruaXpeW/l1xyXG4gICAqL1xyXG4gIHByaXZhdGUgbG9nQWNjZXNzKFxyXG4gICAgc2VydmljZTogc3RyaW5nLFxyXG4gICAgbWV0aG9kOiBzdHJpbmcsXHJcbiAgICBwYXJhbXM6IGFueSxcclxuICAgIGR1cmF0aW9uOiBudW1iZXIsXHJcbiAgICBzdWNjZXNzOiBib29sZWFuLFxyXG4gICAgZXJyb3I/OiBzdHJpbmcsXHJcbiAgICBmcm9tQ2FjaGU/OiBib29sZWFuXHJcbiAgKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMuY29uZmlnLmVuYWJsZUxvZ2dpbmcpIHJldHVyblxyXG5cclxuICAgIGNvbnN0IGxvZzogQWNjZXNzTG9nID0ge1xyXG4gICAgICAvLyDinIUg5L2/55So57uf5LiA5pe26Ze05oiz55Sf5oiQ5ZmoXHJcbiAgICAgIHRpbWVzdGFtcDogdGltZXN0YW1wR2VuZXJhdG9yLm5vdygpLFxyXG4gICAgICBzZXJ2aWNlLFxyXG4gICAgICBtZXRob2QsXHJcbiAgICAgIHBhcmFtcyxcclxuICAgICAgZHVyYXRpb24sXHJcbiAgICAgIHN1Y2Nlc3MsXHJcbiAgICAgIGVycm9yXHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5hY2Nlc3NMb2dzLnB1c2gobG9nKVxyXG5cclxuICAgIC8vIOS/neaMgeacgOi/kTEwMDDmnaHml6Xlv5dcclxuICAgIGlmICh0aGlzLmFjY2Vzc0xvZ3MubGVuZ3RoID4gMTAwMCkge1xyXG4gICAgICB0aGlzLmFjY2Vzc0xvZ3MgPSB0aGlzLmFjY2Vzc0xvZ3Muc2xpY2UoLTEwMDApXHJcbiAgICB9XHJcblxyXG4gICAgLy8g8J+UpyDkvJjljJbvvJrkvb/nlKjmibnph4/miqXlkYrku6Pmm7/ljZXkuKrml6Xlv5dcclxuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xyXG4gICAgICB0aGlzLmFkZFRvUGVyZm9ybWFuY2VCYXRjaChzZXJ2aWNlLCBtZXRob2QsIGR1cmF0aW9uLCBzdWNjZXNzLCBmcm9tQ2FjaGUgfHwgZmFsc2UpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyDwn5SnIOS8mOWMlu+8muaJuemHj+aAp+iDveaKpeWRiuaWueazlVxyXG5cclxuICAvKipcclxuICAgKiDmt7vliqDliLDmgKfog73mibnmrKFcclxuICAgKi9cclxuICBwcml2YXRlIGFkZFRvUGVyZm9ybWFuY2VCYXRjaChzZXJ2aWNlOiBzdHJpbmcsIG1ldGhvZDogc3RyaW5nLCBkdXJhdGlvbjogbnVtYmVyLCBzdWNjZXNzOiBib29sZWFuLCBmcm9tQ2FjaGU6IGJvb2xlYW4pOiB2b2lkIHtcclxuICAgIHRoaXMucGVyZm9ybWFuY2VCYXRjaC5wdXNoKHtcclxuICAgICAgc2VydmljZSxcclxuICAgICAgbWV0aG9kLFxyXG4gICAgICBkdXJhdGlvbixcclxuICAgICAgc3VjY2VzcyxcclxuICAgICAgZnJvbUNhY2hlLFxyXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcclxuICAgIH0pXHJcblxyXG4gICAgLy8g5ZCv5Yqo5om56YeP5oql5ZGK5a6a5pe25ZmoXHJcbiAgICBpZiAoIXRoaXMuYmF0Y2hSZXBvcnRUaW1lcikge1xyXG4gICAgICB0aGlzLmJhdGNoUmVwb3J0VGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICB0aGlzLmZsdXNoUGVyZm9ybWFuY2VCYXRjaCgpXHJcbiAgICAgIH0sIHRoaXMuQkFUQ0hfUkVQT1JUX0lOVEVSVkFMKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6L6T5Ye65om56YeP5oCn6IO95oql5ZGKXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBmbHVzaFBlcmZvcm1hbmNlQmF0Y2goKTogdm9pZCB7XHJcbiAgICBpZiAodGhpcy5wZXJmb3JtYW5jZUJhdGNoLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICB0aGlzLmJhdGNoUmVwb3J0VGltZXIgPSBudWxsXHJcbiAgICAgIHJldHVyblxyXG4gICAgfVxyXG5cclxuICAgIC8vIOe7n+iuoeaVsOaNrlxyXG4gICAgY29uc3Qgc3RhdHMgPSB7XHJcbiAgICAgIHRvdGFsT3BlcmF0aW9uczogdGhpcy5wZXJmb3JtYW5jZUJhdGNoLmxlbmd0aCxcclxuICAgICAgc3VjY2Vzc0NvdW50OiB0aGlzLnBlcmZvcm1hbmNlQmF0Y2guZmlsdGVyKG9wID0+IG9wLnN1Y2Nlc3MpLmxlbmd0aCxcclxuICAgICAgY2FjaGVIaXRzOiB0aGlzLnBlcmZvcm1hbmNlQmF0Y2guZmlsdGVyKG9wID0+IG9wLmZyb21DYWNoZSkubGVuZ3RoLFxyXG4gICAgICBhdmVyYWdlVGltZTogTWF0aC5yb3VuZCh0aGlzLnBlcmZvcm1hbmNlQmF0Y2gucmVkdWNlKChzdW0sIG9wKSA9PiBzdW0gKyBvcC5kdXJhdGlvbiwgMCkgLyB0aGlzLnBlcmZvcm1hbmNlQmF0Y2gubGVuZ3RoKSxcclxuICAgICAgc2VydmljZXM6IG5ldyBTZXQodGhpcy5wZXJmb3JtYW5jZUJhdGNoLm1hcChvcCA9PiBvcC5zZXJ2aWNlKSkuc2l6ZSxcclxuICAgICAgdGltZVJhbmdlOiB7XHJcbiAgICAgICAgc3RhcnQ6IG5ldyBEYXRlKE1hdGgubWluKC4uLnRoaXMucGVyZm9ybWFuY2VCYXRjaC5tYXAob3AgPT4gb3AudGltZXN0YW1wKSkpLnRvTG9jYWxlVGltZVN0cmluZygpLFxyXG4gICAgICAgIGVuZDogbmV3IERhdGUoTWF0aC5tYXgoLi4udGhpcy5wZXJmb3JtYW5jZUJhdGNoLm1hcChvcCA9PiBvcC50aW1lc3RhbXApKSkudG9Mb2NhbGVUaW1lU3RyaW5nKClcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKCfwn5OKIFtEYXRhQWNjZXNzTWFuYWdlcl0g5om56YeP5oCn6IO95oql5ZGKOicsIHN0YXRzKVxyXG5cclxuICAgIC8vIOa4heepuuaJueasoVxyXG4gICAgdGhpcy5wZXJmb3JtYW5jZUJhdGNoID0gW11cclxuICAgIHRoaXMuYmF0Y2hSZXBvcnRUaW1lciA9IG51bGxcclxuICB9XHJcblxyXG4gIC8vIOaAp+iDveS8mOWMluaUr+aMgeaWueazlVxyXG5cclxuICAvKipcclxuICAgKiDliKTmlq3mmK/lkKblupTor6Xkvb/nlKjnvJPlrZhcclxuICAgKi9cclxuICBwcml2YXRlIHNob3VsZFVzZUNhY2hlKG1ldGhvZDogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICAvLyDwn5SnIOS4tOaXtuS/ruWkje+8muemgeeUqGdldEJ5SWTnmoTnvJPlrZjvvIzpgb/lhY1JROa3t+a3humXrumimFxyXG4gICAgaWYgKG1ldGhvZCA9PT0gJ2dldEJ5SWQnKSB7XHJcbiAgICAgIHJldHVybiBmYWxzZVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOivu+aTjeS9nOS9v+eUqOe8k+WtmO+8jOWGmeaTjeS9nOS4jeS9v+eUqFxyXG4gICAgY29uc3QgcmVhZE1ldGhvZHMgPSBbJ2dldCcsICdmaW5kJywgJ3NlYXJjaCcsICdsaXN0JywgJ3N0YXRpc3RpY3MnLCAndXRpbGl6YXRpb24nXVxyXG4gICAgcmV0dXJuIHJlYWRNZXRob2RzLnNvbWUocmVhZE1ldGhvZCA9PiBtZXRob2QudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhyZWFkTWV0aG9kKSlcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOWIpOaWreaYr+WQpuW6lOivpee8k+WtmOe7k+aenFxyXG4gICAqL1xyXG4gIHByaXZhdGUgc2hvdWxkQ2FjaGVSZXN1bHQobWV0aG9kOiBzdHJpbmcsIHJlc3VsdDogYW55KTogYm9vbGVhbiB7XHJcbiAgICAvLyDmiJDlip/nmoTor7vmk43kvZznu5PmnpzmiY3nvJPlrZhcclxuICAgIHJldHVybiB0aGlzLnNob3VsZFVzZUNhY2hlKG1ldGhvZCkgJiYgcmVzdWx0Py5zdGF0dXMgPT09ICdzdWNjZXNzJ1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog55Sf5oiQ57yT5a2Y6ZSuXHJcbiAgICog8J+UpyDkv67lpI3vvJrnu5/kuIDnvJPlrZjplK7moLzlvI/vvIznoa7kv53kuI7muIXnkIbmqKHlvI/ljLnphY1cclxuICAgKi9cclxuICBwcml2YXRlIGdlbmVyYXRlQ2FjaGVLZXkoc2VydmljZTogc3RyaW5nLCBtZXRob2Q6IHN0cmluZywgcGFyYW1zOiBhbnkpOiBzdHJpbmcge1xyXG4gICAgLy8g8J+UpyDkv67lpI3vvJrkvb/nlKjlhpLlj7fliIbpmpTnrKbvvIzkuI5Xb3Jrc3RhdGlvblVwZGF0ZVNlcnZpY2XnmoTmuIXnkIbmqKHlvI/kv53mjIHkuIDoh7RcclxuICAgIGNvbnN0IGJhc2VLZXkgPSBgJHtzZXJ2aWNlfToke21ldGhvZH1gXHJcblxyXG4gICAgLy8g8J+UpyDkv67lpI3vvJrmoIflh4bljJblj4LmlbDlpITnkIbvvIznoa7kv53plK7nmoTkuIDoh7TmgKdcclxuICAgIGlmICghcGFyYW1zIHx8ICh0eXBlb2YgcGFyYW1zID09PSAnb2JqZWN0JyAmJiBPYmplY3Qua2V5cyhwYXJhbXMpLmxlbmd0aCA9PT0gMCkpIHtcclxuICAgICAgcmV0dXJuIGJhc2VLZXlcclxuICAgIH1cclxuXHJcbiAgICAvLyDlr7nlj4LmlbDov5vooYzmoIflh4bljJblpITnkIZcclxuICAgIGxldCBwYXJhbVN0cjogc3RyaW5nXHJcbiAgICBpZiAodHlwZW9mIHBhcmFtcyA9PT0gJ29iamVjdCcpIHtcclxuICAgICAgLy8g5a+55a+56LGh5Y+C5pWw6L+b6KGM5o6S5bqP77yM56Gu5L+d6ZSu55qE5LiA6Ie05oCnXHJcbiAgICAgIGNvbnN0IHNvcnRlZFBhcmFtcyA9IE9iamVjdC5rZXlzKHBhcmFtcykuc29ydCgpLnJlZHVjZSgocmVzdWx0LCBrZXkpID0+IHtcclxuICAgICAgICByZXN1bHRba2V5XSA9IHBhcmFtc1trZXldXHJcbiAgICAgICAgcmV0dXJuIHJlc3VsdFxyXG4gICAgICB9LCB7fSBhcyBhbnkpXHJcbiAgICAgIHBhcmFtU3RyID0gSlNPTi5zdHJpbmdpZnkoc29ydGVkUGFyYW1zKVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgcGFyYW1TdHIgPSBTdHJpbmcocGFyYW1zKVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBgJHtiYXNlS2V5fToke0J1ZmZlci5mcm9tKHBhcmFtU3RyKS50b1N0cmluZygnYmFzZTY0Jykuc2xpY2UoMCwgMzIpfWBcclxuICB9XHJcblxyXG5cclxuXHJcbiAgLyoqXHJcbiAgICog5LuO5pyN5Yqh5ZCN6I635Y+W5pWw5o2u57G75Z6LXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBnZXREYXRhVHlwZUZyb21TZXJ2aWNlKHNlcnZpY2U6IHN0cmluZyk6ICdvcmRlcnMnIHwgJ3dvcmtPcmRlcnMnIHwgJ3dvcmtzdGF0aW9ucycgfCAnc3RhdGlzdGljcycge1xyXG4gICAgaWYgKHNlcnZpY2UuaW5jbHVkZXMoJ1Byb2R1Y3Rpb25PcmRlcicpKSByZXR1cm4gJ29yZGVycydcclxuICAgIGlmIChzZXJ2aWNlLmluY2x1ZGVzKCdQcm9kdWN0aW9uV29ya09yZGVyJykpIHJldHVybiAnd29ya09yZGVycydcclxuICAgIGlmIChzZXJ2aWNlLmluY2x1ZGVzKCdPcmRlclNlcnZpY2UnKSB8fCBzZXJ2aWNlLmluY2x1ZGVzKCdTYWxlc09yZGVyJykpIHJldHVybiAnb3JkZXJzJyAvLyDwn5SnIOaWsOWinu+8muaUr+aMgemUgOWUruiuouWNlVxyXG4gICAgaWYgKHNlcnZpY2UuaW5jbHVkZXMoJ1dvcmtzdGF0aW9uJykpIHJldHVybiAnd29ya3N0YXRpb25zJ1xyXG4gICAgcmV0dXJuICdzdGF0aXN0aWNzJ1xyXG4gIH1cclxuXHJcblxyXG5cclxuICAvKipcclxuICAgKiDojrflj5borr/pl67popHnjofvvIjnroDljJblrp7njrDvvIlcclxuICAgKi9cclxuICBwcml2YXRlIGdldEFjY2Vzc0ZyZXF1ZW5jeShvcGVyYXRpb25LZXk6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgICAvLyDku47orr/pl67ml6Xlv5fkuK3nu5/orqHpopHnjodcclxuICAgIGNvbnN0IHJlY2VudExvZ3MgPSB0aGlzLmFjY2Vzc0xvZ3MuZmlsdGVyKGxvZyA9PlxyXG4gICAgICBEYXRlLm5vdygpIC0gbmV3IERhdGUobG9nLnRpbWVzdGFtcCkuZ2V0VGltZSgpIDwgNSAqIDYwICogMTAwMCAvLyDmnIDov5E15YiG6ZKfXHJcbiAgICApXHJcblxyXG4gICAgcmV0dXJuIHJlY2VudExvZ3MuZmlsdGVyKGxvZyA9PiBgJHtsb2cuc2VydmljZX0uJHtsb2cubWV0aG9kfWAgPT09IG9wZXJhdGlvbktleSkubGVuZ3RoXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDliKTmlq3mmK/lkKblupTor6XpooTliqDovb1cclxuICAgKi9cclxuICBwcml2YXRlIHNob3VsZFByZWxvYWQobWV0aG9kOiBzdHJpbmcsIHJlc3VsdDogYW55KTogYm9vbGVhbiB7XHJcbiAgICAvLyDojrflj5bliJfooajmlbDmja7ml7bop6blj5HpooTliqDovb1cclxuICAgIHJldHVybiBtZXRob2QuaW5jbHVkZXMoJ2dldEFsbCcpICYmIHJlc3VsdD8uZGF0YT8uaXRlbXM/Lmxlbmd0aCA+IDBcclxuICB9XHJcblxyXG4gIC8vID09PT09PT09PT09PT09PT09PT09IOWGhee9rue8k+WtmOezu+e7n+aguOW/g+aWueazlSA9PT09PT09PT09PT09PT09PT09PVxyXG5cclxuICAvKipcclxuICAgKiDku47nvJPlrZjojrflj5bmlbDmja5cclxuICAgKi9cclxuICBwcml2YXRlIGdldEZyb21DYWNoZTxUPihrZXk6IHN0cmluZyk6IFQgfCBudWxsIHtcclxuICAgIGNvbnN0IGVudHJ5ID0gdGhpcy5jYWNoZS5nZXQoa2V5KVxyXG4gICAgaWYgKCFlbnRyeSkgcmV0dXJuIG51bGxcclxuXHJcbiAgICAvLyDmo4Dmn6XmmK/lkKbov4fmnJ9cclxuICAgIGlmIChEYXRlLm5vdygpID4gZW50cnkuZXhwaXJlc0F0KSB7XHJcbiAgICAgIHRoaXMuY2FjaGUuZGVsZXRlKGtleSlcclxuICAgICAgcmV0dXJuIG51bGxcclxuICAgIH1cclxuXHJcbiAgICBlbnRyeS5hY2Nlc3NDb3VudCsrXHJcbiAgICBlbnRyeS5sYXN0QWNjZXNzZWQgPSBEYXRlLm5vdygpXHJcbiAgICByZXR1cm4gZW50cnkuZGF0YVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6K6+572u57yT5a2Y5pWw5o2uIC0g5LyY5YyW54mIXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzZXRUb0NhY2hlPFQ+KGtleTogc3RyaW5nLCBkYXRhOiBULCBzZXJ2aWNlOiBzdHJpbmcsIG1ldGhvZD86IHN0cmluZyk6IHZvaWQge1xyXG4gICAgLy8g4pyFIOS8mOWMlu+8muaZuuiDvee8k+WtmOWuuemHj+euoeeQhlxyXG4gICAgaWYgKHRoaXMuY2FjaGUuc2l6ZSA+PSB0aGlzLmNhY2hlQ29uZmlnLm1heFNpemUpIHtcclxuICAgICAgdGhpcy5zbWFydEV2aWN0RW50cmllcygpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgc3RyYXRlZ3kgPSB0aGlzLmNhY2hlQ29uZmlnLnN0cmF0ZWdpZXNbc2VydmljZV0gfHwgeyBcclxuICAgICAgdHRsOiB0aGlzLmNhY2hlQ29uZmlnLmRlZmF1bHRUVEwsXHJcbiAgICAgIHByaW9yaXR5OiAnbWVkaXVtJ1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyDinIUg5LyY5YyW77ya5pm66IO95pWw5o2u5YiG5p6QXHJcbiAgICBjb25zdCBkYXRhVHlwZSA9IHRoaXMuZ2V0RGF0YVR5cGUoc2VydmljZSwgbWV0aG9kKVxyXG4gICAgY29uc3QgcHJpb3JpdHkgPSBzdHJhdGVneS5wcmlvcml0eSB8fCAnbWVkaXVtJ1xyXG4gICAgY29uc3QgZXN0aW1hdGVkU2l6ZSA9IHRoaXMuZXN0aW1hdGVEYXRhU2l6ZShkYXRhKVxyXG4gICAgY29uc3QgYWNjZXNzUGF0dGVybiA9IHRoaXMucHJlZGljdEFjY2Vzc1BhdHRlcm4oa2V5LCBzZXJ2aWNlKVxyXG4gICAgXHJcbiAgICBjb25zdCBlbnRyeTogQ2FjaGVFbnRyeSA9IHtcclxuICAgICAgZGF0YSxcclxuICAgICAgZXhwaXJlc0F0OiBEYXRlLm5vdygpICsgc3RyYXRlZ3kudHRsLFxyXG4gICAgICBhY2Nlc3NDb3VudDogMSxcclxuICAgICAgbGFzdEFjY2Vzc2VkOiBEYXRlLm5vdygpLFxyXG4gICAgICBjcmVhdGVkQXQ6IERhdGUubm93KCksXHJcbiAgICAgIC8vIOKchSDkvJjljJbvvJrmmbrog73nvJPlrZjlsZ7mgKdcclxuICAgICAgcHJpb3JpdHksXHJcbiAgICAgIGRhdGFUeXBlLFxyXG4gICAgICBlc3RpbWF0ZWRTaXplLFxyXG4gICAgICBhY2Nlc3NQYXR0ZXJuLFxyXG4gICAgICByZWZyZXNoYWJsZTogdGhpcy5pc1JlZnJlc2hhYmxlRGF0YShzZXJ2aWNlLCBtZXRob2QpXHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5jYWNoZS5zZXQoa2V5LCBlbnRyeSlcclxuICAgIFxyXG4gICAgLy8g4pyFIOS8mOWMlu+8muiusOW9leiuv+mXruWOhuWPslxyXG4gICAgdGhpcy5yZWNvcmRDYWNoZUFjY2VzcyhrZXkpXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya5pm66IO957yT5a2Y5riF55CG562W55WlXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzbWFydEV2aWN0RW50cmllcygpOiB2b2lkIHtcclxuICAgIGNvbnN0IGVudHJpZXMgPSBBcnJheS5mcm9tKHRoaXMuY2FjaGUuZW50cmllcygpKVxyXG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKVxyXG4gICAgXHJcbiAgICAvLyDmoLnmja7phY3nva7nmoTnrpfms5XpgInmi6nmuIXnkIbnrZbnlaVcclxuICAgIHN3aXRjaCAodGhpcy5jYWNoZUNvbmZpZy5zbWFydEV2aWN0aW9uLmFsZ29yaXRobSkge1xyXG4gICAgICBjYXNlICdscnUnOlxyXG4gICAgICAgIHRoaXMuZXZpY3RCeUxSVShlbnRyaWVzKVxyXG4gICAgICAgIGJyZWFrXHJcbiAgICAgIGNhc2UgJ2xmdSc6XHJcbiAgICAgICAgdGhpcy5ldmljdEJ5TEZVKGVudHJpZXMpXHJcbiAgICAgICAgYnJlYWtcclxuICAgICAgY2FzZSAnYWRhcHRpdmUnOlxyXG4gICAgICBkZWZhdWx0OlxyXG4gICAgICAgIHRoaXMuZXZpY3RBZGFwdGl2ZShlbnRyaWVzLCBub3cpXHJcbiAgICAgICAgYnJlYWtcclxuICAgIH1cclxuICAgIFxyXG4gICAgdGhpcy5jYWNoZVN0YXRzLmV2aWN0aW9ucysrXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya6Ieq6YCC5bqU57yT5a2Y5riF55CGXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBldmljdEFkYXB0aXZlKGVudHJpZXM6IEFycmF5PFtzdHJpbmcsIENhY2hlRW50cnldPiwgbm93OiBudW1iZXIpOiB2b2lkIHtcclxuICAgIC8vIOe7vOWQiOiAg+iZkeS8mOWFiOe6p+OAgeiuv+mXrumikeeOh+OAgeWkp+Wwj+etieWboOe0oFxyXG4gICAgY29uc3Qgc2NvcmVkID0gZW50cmllcy5tYXAoKFtrZXksIGVudHJ5XSkgPT4ge1xyXG4gICAgICBsZXQgc2NvcmUgPSAwXHJcbiAgICAgIFxyXG4gICAgICAvLyDkvJjlhYjnuqfor4TliIYgKOS8mOWFiOe6p+i2iumrmOWIhuaVsOi2iumrmO+8jOi2iuS4jeWuueaYk+iiq+a4heeQhilcclxuICAgICAgY29uc3QgcHJpb3JpdHlTY29yZXMgPSB7IGNyaXRpY2FsOiAxMDAsIGhpZ2g6IDcwLCBtZWRpdW06IDQwLCBsb3c6IDEwIH1cclxuICAgICAgc2NvcmUgKz0gcHJpb3JpdHlTY29yZXNbZW50cnkucHJpb3JpdHldIHx8IDQwXHJcbiAgICAgIFxyXG4gICAgICAvLyDorr/pl67popHnjofor4TliIZcclxuICAgICAgY29uc3QgYWdlID0gKG5vdyAtIGVudHJ5LmNyZWF0ZWRBdCkgLyAoMjQgKiA2MCAqIDYwICogMTAwMCkgLy8g5aSp5pWwXHJcbiAgICAgIGNvbnN0IGZyZXF1ZW5jeSA9IGVudHJ5LmFjY2Vzc0NvdW50IC8gTWF0aC5tYXgoYWdlLCAwLjEpXHJcbiAgICAgIHNjb3JlICs9IE1hdGgubWluKGZyZXF1ZW5jeSAqIDEwLCA1MClcclxuICAgICAgXHJcbiAgICAgIC8vIOacgOi/keiuv+mXruivhOWIhlxyXG4gICAgICBjb25zdCBsYXN0QWNjZXNzQWdlID0gKG5vdyAtIGVudHJ5Lmxhc3RBY2Nlc3NlZCkgLyAoNjAgKiA2MCAqIDEwMDApIC8vIOWwj+aXtlxyXG4gICAgICBzY29yZSArPSBNYXRoLm1heCg1MCAtIGxhc3RBY2Nlc3NBZ2UgKiAyLCAwKVxyXG4gICAgICBcclxuICAgICAgLy8g5aSn5bCP5oOp572a77yI5aSn5pWw5o2u6ZmN5L2O5YiG5pWw77yJXHJcbiAgICAgIHNjb3JlIC09IE1hdGgubWluKGVudHJ5LmVzdGltYXRlZFNpemUgLyAxMDAwLCAyMClcclxuICAgICAgXHJcbiAgICAgIHJldHVybiB7IGtleSwgZW50cnksIHNjb3JlIH1cclxuICAgIH0pXHJcbiAgICBcclxuICAgIC8vIOaMieWIhuaVsOaOkuW6j++8jOWIoOmZpOWIhuaVsOacgOS9jueahOadoeebrlxyXG4gICAgc2NvcmVkLnNvcnQoKGEsIGIpID0+IGEuc2NvcmUgLSBiLnNjb3JlKVxyXG4gICAgY29uc3QgZGVsZXRlQ291bnQgPSBNYXRoLm1heChNYXRoLmZsb29yKGVudHJpZXMubGVuZ3RoICogMC4xNSksIDEpXHJcbiAgICBcclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGVsZXRlQ291bnQgJiYgaSA8IHNjb3JlZC5sZW5ndGg7IGkrKykge1xyXG4gICAgICB0aGlzLmNhY2hlLmRlbGV0ZShzY29yZWRbaV0ua2V5KVxyXG4gICAgICB0aGlzLmNhY2hlQWNjZXNzSGlzdG9yeS5kZWxldGUoc2NvcmVkW2ldLmtleSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJpMUlXmuIXnkIbnrZbnlaVcclxuICAgKi9cclxuICBwcml2YXRlIGV2aWN0QnlMUlUoZW50cmllczogQXJyYXk8W3N0cmluZywgQ2FjaGVFbnRyeV0+KTogdm9pZCB7XHJcbiAgICBlbnRyaWVzLnNvcnQoKGEsIGIpID0+IGFbMV0ubGFzdEFjY2Vzc2VkIC0gYlsxXS5sYXN0QWNjZXNzZWQpXHJcbiAgICBjb25zdCBkZWxldGVDb3VudCA9IE1hdGguZmxvb3IoZW50cmllcy5sZW5ndGggKiAwLjEpXHJcbiAgICBcclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGVsZXRlQ291bnQ7IGkrKykge1xyXG4gICAgICB0aGlzLmNhY2hlLmRlbGV0ZShlbnRyaWVzW2ldWzBdKVxyXG4gICAgICB0aGlzLmNhY2hlQWNjZXNzSGlzdG9yeS5kZWxldGUoZW50cmllc1tpXVswXSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJpMRlXmuIXnkIbnrZbnlaVcclxuICAgKi9cclxuICBwcml2YXRlIGV2aWN0QnlMRlUoZW50cmllczogQXJyYXk8W3N0cmluZywgQ2FjaGVFbnRyeV0+KTogdm9pZCB7XHJcbiAgICBlbnRyaWVzLnNvcnQoKGEsIGIpID0+IGFbMV0uYWNjZXNzQ291bnQgLSBiWzFdLmFjY2Vzc0NvdW50KVxyXG4gICAgY29uc3QgZGVsZXRlQ291bnQgPSBNYXRoLmZsb29yKGVudHJpZXMubGVuZ3RoICogMC4xKVxyXG4gICAgXHJcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRlbGV0ZUNvdW50OyBpKyspIHtcclxuICAgICAgdGhpcy5jYWNoZS5kZWxldGUoZW50cmllc1tpXVswXSlcclxuICAgICAgdGhpcy5jYWNoZUFjY2Vzc0hpc3RvcnkuZGVsZXRlKGVudHJpZXNbaV1bMF0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmuIXnkIbov4fmnJ/nvJPlrZggLSDkv53nlZnljp/mnInmlrnms5XkvZzkuLrlpIfnlKhcclxuICAgKi9cclxuICBwcml2YXRlIGV2aWN0T2xkZXN0RW50cmllcygpOiB2b2lkIHtcclxuICAgIGNvbnN0IGVudHJpZXMgPSBBcnJheS5mcm9tKHRoaXMuY2FjaGUuZW50cmllcygpKVxyXG4gICAgZW50cmllcy5zb3J0KChhLCBiKSA9PiBhWzFdLmxhc3RBY2Nlc3NlZCAtIGJbMV0ubGFzdEFjY2Vzc2VkKVxyXG5cclxuICAgIC8vIOWIoOmZpOacgOaXp+eahDEwJeadoeebrlxyXG4gICAgY29uc3QgZGVsZXRlQ291bnQgPSBNYXRoLmZsb29yKGVudHJpZXMubGVuZ3RoICogMC4xKVxyXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkZWxldGVDb3VudDsgaSsrKSB7XHJcbiAgICAgIHRoaXMuY2FjaGUuZGVsZXRlKGVudHJpZXNbaV1bMF0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDnu5/kuIDnmoTnvJPlrZjmiafooYzmlrnms5VcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIGV4ZWN1dGVXaXRoQ2FjaGU8VD4oXHJcbiAgICBzZXJ2aWNlOiBzdHJpbmcsXHJcbiAgICBtZXRob2Q6IHN0cmluZyxcclxuICAgIHBhcmFtczogYW55LFxyXG4gICAgZXhlY3V0b3I6ICgpID0+IFByb21pc2U8VD5cclxuICApOiBQcm9taXNlPFQ+IHtcclxuICAgIC8vIOKchSDmnrbmnoTlkIjop4TvvJrkvb/nlKjlhoXnva7nvJPlrZjns7vnu5/lhrPnrZZcclxuICAgIGlmICghdGhpcy5jb25maWcuZW5hYmxlQ2FjaGluZykge1xyXG4gICAgICByZXR1cm4gZXhlY3V0b3IoKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOeugOWMlueahOe8k+WtmOWGs+etlu+8muivu+aTjeS9nOaJjee8k+WtmFxyXG4gICAgY29uc3QgcmVhZE1ldGhvZHMgPSBbJ2dldCcsICdmaW5kJywgJ3NlYXJjaCcsICdsaXN0JywgJ3N0YXRpc3RpY3MnXVxyXG4gICAgY29uc3Qgc2hvdWxkQ2FjaGUgPSByZWFkTWV0aG9kcy5zb21lKHJlYWRNZXRob2QgPT4gXHJcbiAgICAgIG1ldGhvZC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHJlYWRNZXRob2QpXHJcbiAgICApXHJcblxyXG4gICAgaWYgKCFzaG91bGRDYWNoZSkge1xyXG4gICAgICByZXR1cm4gZXhlY3V0b3IoKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNhY2hlS2V5ID0gdGhpcy5nZW5lcmF0ZUNhY2hlS2V5KHNlcnZpY2UsIG1ldGhvZCwgcGFyYW1zKVxyXG5cclxuICAgIC8vIOivt+axguWOu+mHjVxyXG4gICAgaWYgKHRoaXMucmVxdWVzdENhY2hlLmhhcyhjYWNoZUtleSkpIHtcclxuICAgICAgcmV0dXJuIHRoaXMucmVxdWVzdENhY2hlLmdldChjYWNoZUtleSkhXHJcbiAgICB9XHJcblxyXG4gICAgLy8g4pyFIOaetuaehOWQiOinhO+8muS9v+eUqERhdGFBY2Nlc3NNYW5hZ2Vy5YaF572u57yT5a2Y57O757ufXHJcbiAgICBjb25zdCBjYWNoZWRFbnRyeSA9IHRoaXMuY2FjaGUuZ2V0KGNhY2hlS2V5KVxyXG4gICAgaWYgKGNhY2hlZEVudHJ5ICYmIERhdGUubm93KCkgPCBjYWNoZWRFbnRyeS5leHBpcmVzQXQpIHtcclxuICAgICAgdGhpcy5jYWNoZVN0YXRzLmhpdHMrK1xyXG4gICAgICAvLyDinIUg5LyY5YyW77ya5pu05paw6K6/6Zeu5L+h5oGvXHJcbiAgICAgIGNhY2hlZEVudHJ5LmFjY2Vzc0NvdW50KytcclxuICAgICAgY2FjaGVkRW50cnkubGFzdEFjY2Vzc2VkID0gRGF0ZS5ub3coKVxyXG4gICAgICB0aGlzLnJlY29yZENhY2hlQWNjZXNzKGNhY2hlS2V5KVxyXG4gICAgICByZXR1cm4gY2FjaGVkRW50cnkuZGF0YVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOaJp+ihjOivt+axglxyXG4gICAgY29uc3QgcmVxdWVzdFByb21pc2UgPSBleGVjdXRvcigpXHJcbiAgICB0aGlzLnJlcXVlc3RDYWNoZS5zZXQoY2FjaGVLZXksIHJlcXVlc3RQcm9taXNlKVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlcXVlc3RQcm9taXNlXHJcbiAgICAgIHRoaXMuY2FjaGVTdGF0cy5taXNzZXMrK1xyXG5cclxuICAgICAgLy8g4pyFIOaetuaehOWQiOinhO+8muS9v+eUqERhdGFBY2Nlc3NNYW5hZ2Vy5YaF572u57yT5a2Y57O757uf6K6+572u57yT5a2YXHJcbiAgICAgIGlmICh0aGlzLnNob3VsZENhY2hlUmVzdWx0KG1ldGhvZCwgcmVzdWx0KSkge1xyXG4gICAgICAgIHRoaXMuc2V0VG9DYWNoZShjYWNoZUtleSwgcmVzdWx0LCBzZXJ2aWNlLCBtZXRob2QpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiByZXN1bHRcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHRoaXMucmVxdWVzdENhY2hlLmRlbGV0ZShjYWNoZUtleSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vID09PT09PT09PT09PT09PT09PT09IOKchSDkvJjljJbvvJrmmbrog73nvJPlrZjovoXliqnmlrnms5UgPT09PT09PT09PT09PT09PT09PT1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOS8mOWMlu+8muWIneWni+WMlumrmOe6p+e8k+WtmOWKn+iDvVxyXG4gICAqL1xyXG4gIHByaXZhdGUgaW5pdGlhbGl6ZUFkdmFuY2VkQ2FjaGluZygpOiB2b2lkIHtcclxuICAgIGNvbnNvbGUubG9nKCfwn5SnIFtEYXRhQWNjZXNzTWFuYWdlcl0g5ZCv5Yqo5pm66IO957yT5a2Y566h55CGJylcclxuICAgIFxyXG4gICAgLy8g5ZCv5Yqo57yT5a2Y6aKE54OtXHJcbiAgICBpZiAodGhpcy5jYWNoZUNvbmZpZy5wcmV3YXJtaW5nLmVuYWJsZWQpIHtcclxuICAgICAgdGhpcy5zdGFydENhY2hlUHJld2FybWluZygpXHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIOWQr+WKqOWumuacn+WGheWtmOajgOafpVxyXG4gICAgdGhpcy5zdGFydE1lbW9yeU1vbml0b3JpbmcoKVxyXG4gICAgXHJcbiAgICBjb25zb2xlLmxvZygn4pyFIFtEYXRhQWNjZXNzTWFuYWdlcl0g5pm66IO957yT5a2Y566h55CG5bey5ZCv5YqoJylcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJrlkK/liqjnvJPlrZjpooTng61cclxuICAgKi9cclxuICBwcml2YXRlIHN0YXJ0Q2FjaGVQcmV3YXJtaW5nKCk6IHZvaWQge1xyXG4gICAgaWYgKHRoaXMucHJld2FybVRpbWVyKSB7XHJcbiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5wcmV3YXJtVGltZXIpXHJcbiAgICB9XHJcbiAgICBcclxuICAgIHRoaXMucHJld2FybVRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICB0aGlzLnBlcmZvcm1DYWNoZVByZXdhcm1pbmcoKVxyXG4gICAgfSwgdGhpcy5jYWNoZUNvbmZpZy5wcmV3YXJtaW5nLnNjaGVkdWxlSW50ZXJ2YWwpXHJcbiAgICBcclxuICAgIC8vIOeri+WNs+aJp+ihjOS4gOasoemihOeDrVxyXG4gICAgc2V0VGltZW91dCgoKSA9PiB0aGlzLnBlcmZvcm1DYWNoZVByZXdhcm1pbmcoKSwgNTAwMCkgLy8gNeenkuWQjuW8gOWni1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOS8mOWMlu+8muaJp+ihjOe8k+WtmOmihOeDrVxyXG4gICAqL1xyXG4gIHByaXZhdGUgYXN5bmMgcGVyZm9ybUNhY2hlUHJld2FybWluZygpOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIGlmICh0aGlzLm1lbW9yeVByZXNzdXJlKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SnIFtEYXRhQWNjZXNzTWFuYWdlcl0g5YaF5a2Y5Y6L5Yqb6L+H6auY77yM6Lez6L+H6aKE54OtJylcclxuICAgICAgcmV0dXJuXHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnNvbGUubG9nKCfwn5SlIFtEYXRhQWNjZXNzTWFuYWdlcl0g5byA5aeL57yT5a2Y6aKE54OtJylcclxuICAgIFxyXG4gICAgdHJ5IHtcclxuICAgICAgZm9yIChjb25zdCBzdHJhdGVneSBvZiB0aGlzLmNhY2hlQ29uZmlnLnByZXdhcm1pbmcuc3RyYXRlZ2llcykge1xyXG4gICAgICAgIGF3YWl0IHRoaXMucHJld2FybVN0cmF0ZWd5KHN0cmF0ZWd5KVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgW0RhdGFBY2Nlc3NNYW5hZ2VyXSDnvJPlrZjpooTng63lpLHotKU6JywgZXJyb3IpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya6aKE54Ot54m55a6a562W55WlXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBwcmV3YXJtU3RyYXRlZ3koc3RyYXRlZ3k6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgc3dpdGNoIChzdHJhdGVneSkge1xyXG4gICAgICBjYXNlICd3b3Jrc3RhdGlvbnMnOlxyXG4gICAgICAgIGF3YWl0IHRoaXMucHJld2FybVdvcmtzdGF0aW9ucygpXHJcbiAgICAgICAgYnJlYWtcclxuICAgICAgY2FzZSAnb3JkZXJzJzpcclxuICAgICAgICBhd2FpdCB0aGlzLnByZXdhcm1PcmRlcnMoKVxyXG4gICAgICAgIGJyZWFrXHJcbiAgICAgIGNhc2UgJ3Byb2R1Y3RzJzpcclxuICAgICAgICBhd2FpdCB0aGlzLnByZXdhcm1Qcm9kdWN0cygpXHJcbiAgICAgICAgYnJlYWtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJrpooTng63lt6XkvY3mlbDmja5cclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIHByZXdhcm1Xb3Jrc3RhdGlvbnMoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCB0aGlzLndvcmtzdGF0aW9ucy5nZXRBY3RpdmVXb3Jrc3RhdGlvbnMoKVxyXG4gICAgICB0aGlzLmNhY2hlU3RhdHMucHJld2FybUhpdHMrK1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UpSBbRGF0YUFjY2Vzc01hbmFnZXJdIOW3peS9jeaVsOaNrumihOeDreWujOaIkCcpXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBbRGF0YUFjY2Vzc01hbmFnZXJdIOW3peS9jeaVsOaNrumihOeDreWksei0pTonLCBlcnJvcilcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJrpooTng63orqLljZXmlbDmja5cclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIHByZXdhcm1PcmRlcnMoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCB0aGlzLm9yZGVycy5nZXRBbGwoeyBsaW1pdDogNTAgfSkgLy8g5pyA6L+RNTDkuKrorqLljZVcclxuICAgICAgdGhpcy5jYWNoZVN0YXRzLnByZXdhcm1IaXRzKytcclxuICAgICAgY29uc29sZS5sb2coJ/CflKUgW0RhdGFBY2Nlc3NNYW5hZ2VyXSDorqLljZXmlbDmja7pooTng63lrozmiJAnKVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gW0RhdGFBY2Nlc3NNYW5hZ2VyXSDorqLljZXmlbDmja7pooTng63lpLHotKU6JywgZXJyb3IpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya6aKE54Ot5Lqn5ZOB5pWw5o2uXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBwcmV3YXJtUHJvZHVjdHMoKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCB0aGlzLnByb2R1Y3RzLmdldEFjdGl2ZSgpXHJcbiAgICAgIHRoaXMuY2FjaGVTdGF0cy5wcmV3YXJtSGl0cysrXHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SlIFtEYXRhQWNjZXNzTWFuYWdlcl0g5Lqn5ZOB5pWw5o2u6aKE54Ot5a6M5oiQJylcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIFtEYXRhQWNjZXNzTWFuYWdlcl0g5Lqn5ZOB5pWw5o2u6aKE54Ot5aSx6LSlOicsIGVycm9yKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOS8mOWMlu+8muWQr+WKqOWGheWtmOebkeaOp1xyXG4gICAqL1xyXG4gIHByaXZhdGUgc3RhcnRNZW1vcnlNb25pdG9yaW5nKCk6IHZvaWQge1xyXG4gICAgc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICB0aGlzLmNoZWNrTWVtb3J5UHJlc3N1cmUoKVxyXG4gICAgfSwgMzAwMDApIC8vIOavjzMw56eS5qOA5p+l5LiA5qyhXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya5qOA5p+l5YaF5a2Y5Y6L5YqbXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBjaGVja01lbW9yeVByZXNzdXJlKCk6IHZvaWQge1xyXG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKVxyXG4gICAgaWYgKG5vdyAtIHRoaXMubGFzdE1lbW9yeUNoZWNrIDwgMTAwMDApIHJldHVybiAvLyAxMOenkuWGheS4jemHjeWkjeajgOafpVxyXG4gICAgXHJcbiAgICB0aGlzLmxhc3RNZW1vcnlDaGVjayA9IG5vd1xyXG4gICAgXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDnroDljZXnmoTlhoXlrZjljovlipvmo4DmtYvvvIjln7rkuo7nvJPlrZjlpKflsI/vvIlcclxuICAgICAgY29uc3QgY2FjaGVNZW1vcnlSYXRpbyA9IHRoaXMuY2FjaGUuc2l6ZSAvIHRoaXMuY2FjaGVDb25maWcubWF4U2l6ZVxyXG4gICAgICBcclxuICAgICAgaWYgKGNhY2hlTWVtb3J5UmF0aW8gPiB0aGlzLmNhY2hlQ29uZmlnLnNtYXJ0RXZpY3Rpb24udGhyZXNob2xkcy5tZW1vcnlDcml0aWNhbCkge1xyXG4gICAgICAgIHRoaXMubWVtb3J5UHJlc3N1cmUgPSB0cnVlXHJcbiAgICAgICAgdGhpcy5oYW5kbGVDcml0aWNhbE1lbW9yeVByZXNzdXJlKClcclxuICAgICAgfSBlbHNlIGlmIChjYWNoZU1lbW9yeVJhdGlvID4gdGhpcy5jYWNoZUNvbmZpZy5zbWFydEV2aWN0aW9uLnRocmVzaG9sZHMubWVtb3J5V2FybmluZykge1xyXG4gICAgICAgIHRoaXMubWVtb3J5UHJlc3N1cmUgPSBmYWxzZVxyXG4gICAgICAgIHRoaXMuaGFuZGxlTWVtb3J5V2FybmluZygpXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhpcy5tZW1vcnlQcmVzc3VyZSA9IGZhbHNlXHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIFtEYXRhQWNjZXNzTWFuYWdlcl0g5YaF5a2Y5Y6L5Yqb5qOA5p+l5aSx6LSlOicsIGVycm9yKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOS8mOWMlu+8muWkhOeQhue0p+aApeWGheWtmOWOi+WKm1xyXG4gICAqL1xyXG4gIHByaXZhdGUgaGFuZGxlQ3JpdGljYWxNZW1vcnlQcmVzc3VyZSgpOiB2b2lkIHtcclxuICAgIGNvbnNvbGUud2Fybign8J+aqCBbRGF0YUFjY2Vzc01hbmFnZXJdIOajgOa1i+WIsOe0p+aApeWGheWtmOWOi+WKm++8jOaJp+ihjOW8uuWItua4heeQhicpXHJcbiAgICBcclxuICAgIC8vIOW8uuWItua4heeQhue8k+WtmFxyXG4gICAgdGhpcy5zbWFydEV2aWN0RW50cmllcygpXHJcbiAgICBcclxuICAgIC8vIOaaguWBnOmihOeDrVxyXG4gICAgaWYgKHRoaXMucHJld2FybVRpbWVyKSB7XHJcbiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5wcmV3YXJtVGltZXIpXHJcbiAgICAgIHRoaXMucHJld2FybVRpbWVyID0gbnVsbFxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOS8mOWMlu+8muWkhOeQhuWGheWtmOitpuWRilxyXG4gICAqL1xyXG4gIHByaXZhdGUgaGFuZGxlTWVtb3J5V2FybmluZygpOiB2b2lkIHtcclxuICAgIGNvbnNvbGUud2Fybign4pqg77iPIFtEYXRhQWNjZXNzTWFuYWdlcl0g5qOA5rWL5Yiw5YaF5a2Y5Y6L5Yqb6K2m5ZGK77yM5omn6KGM6YCC5bqm5riF55CGJylcclxuICAgIFxyXG4gICAgLy8g5riF55CG5L2O5LyY5YWI57qn57yT5a2YXHJcbiAgICB0aGlzLmNsZWFudXBMb3dQcmlvcml0eUNhY2hlKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJrmuIXnkIbkvY7kvJjlhYjnuqfnvJPlrZhcclxuICAgKi9cclxuICBwcml2YXRlIGNsZWFudXBMb3dQcmlvcml0eUNhY2hlKCk6IHZvaWQge1xyXG4gICAgY29uc3QgZW50cmllcyA9IEFycmF5LmZyb20odGhpcy5jYWNoZS5lbnRyaWVzKCkpXHJcbiAgICBsZXQgZGVsZXRlZENvdW50ID0gMFxyXG4gICAgXHJcbiAgICBmb3IgKGNvbnN0IFtrZXksIGVudHJ5XSBvZiBlbnRyaWVzKSB7XHJcbiAgICAgIGlmIChlbnRyeS5wcmlvcml0eSA9PT0gJ2xvdycgJiYgRGF0ZS5ub3coKSA+IGVudHJ5LmV4cGlyZXNBdCAtIDYwMDAwKSB7IC8vIOaPkOWJjTHliIbpkp/muIXnkIbkvY7kvJjlhYjnuqdcclxuICAgICAgICB0aGlzLmNhY2hlLmRlbGV0ZShrZXkpXHJcbiAgICAgICAgdGhpcy5jYWNoZUFjY2Vzc0hpc3RvcnkuZGVsZXRlKGtleSlcclxuICAgICAgICBkZWxldGVkQ291bnQrK1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnNvbGUubG9nKGDwn6e5IFtEYXRhQWNjZXNzTWFuYWdlcl0g5riF55CG5LqGJHtkZWxldGVkQ291bnR95Liq5L2O5LyY5YWI57qn57yT5a2Y5p2h55uuYClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOKchSDkvJjljJbvvJrojrflj5bmlbDmja7nsbvlnotcclxuICAgKi9cclxuICBwcml2YXRlIGdldERhdGFUeXBlKHNlcnZpY2U6IHN0cmluZywgbWV0aG9kPzogc3RyaW5nKTogc3RyaW5nIHtcclxuICAgIGlmICghbWV0aG9kKSByZXR1cm4gc2VydmljZVxyXG4gICAgXHJcbiAgICBpZiAobWV0aG9kLmluY2x1ZGVzKCdzdGF0aXN0aWNzJykgfHwgbWV0aG9kLmluY2x1ZGVzKCd1dGlsaXphdGlvbicpKSByZXR1cm4gJ3N0YXRpc3RpY3MnXHJcbiAgICBpZiAobWV0aG9kLmluY2x1ZGVzKCdsaXN0JykgfHwgbWV0aG9kLmluY2x1ZGVzKCdnZXRBbGwnKSkgcmV0dXJuICdsaXN0J1xyXG4gICAgaWYgKG1ldGhvZC5pbmNsdWRlcygnZ2V0QnlJZCcpIHx8IG1ldGhvZC5pbmNsdWRlcygnZ2V0QnknKSkgcmV0dXJuICdkZXRhaWwnXHJcbiAgICBcclxuICAgIHJldHVybiBzZXJ2aWNlXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya6aKE5rWL6K6/6Zeu5qih5byPXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBwcmVkaWN0QWNjZXNzUGF0dGVybihrZXk6IHN0cmluZywgc2VydmljZTogc3RyaW5nKTogJ2ZyZXF1ZW50JyB8ICdvY2Nhc2lvbmFsJyB8ICdyYXJlJyB7XHJcbiAgICBjb25zdCBoaXN0b3J5ID0gdGhpcy5jYWNoZUFjY2Vzc0hpc3RvcnkuZ2V0KGtleSkgfHwgW11cclxuICAgIFxyXG4gICAgaWYgKGhpc3RvcnkubGVuZ3RoIDwgMikge1xyXG4gICAgICAvLyDmoLnmja7mnI3liqHnsbvlnovpooTmtYtcclxuICAgICAgaWYgKHNlcnZpY2UgPT09ICd3b3Jrc3RhdGlvbnMnIHx8IHNlcnZpY2UgPT09ICdzdGF0aXN0aWNzJykgcmV0dXJuICdmcmVxdWVudCdcclxuICAgICAgaWYgKHNlcnZpY2UgPT09ICdvcmRlcnMnIHx8IHNlcnZpY2UgPT09ICdwcm9kdWN0cycpIHJldHVybiAnb2NjYXNpb25hbCdcclxuICAgICAgcmV0dXJuICdyYXJlJ1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyDln7rkuo7ljoblj7Lorr/pl67popHnjodcclxuICAgIGNvbnN0IHJlY2VudEFjY2Vzc2VzID0gaGlzdG9yeS5maWx0ZXIodGltZSA9PiBEYXRlLm5vdygpIC0gdGltZSA8IDYwICogNjAgKiAxMDAwKS5sZW5ndGggLy8gMeWwj+aXtuWGhVxyXG4gICAgaWYgKHJlY2VudEFjY2Vzc2VzID4gMTApIHJldHVybiAnZnJlcXVlbnQnXHJcbiAgICBpZiAocmVjZW50QWNjZXNzZXMgPiAzKSByZXR1cm4gJ29jY2FzaW9uYWwnXHJcbiAgICByZXR1cm4gJ3JhcmUnXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya5Lyw566X5pWw5o2u5aSn5bCPXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBlc3RpbWF0ZURhdGFTaXplKGRhdGE6IGFueSk6IG51bWJlciB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCBqc29uU3RyaW5nID0gSlNPTi5zdHJpbmdpZnkoZGF0YSlcclxuICAgICAgcmV0dXJuIGpzb25TdHJpbmcubGVuZ3RoICogMiAvLyDlpKfoh7TkvLDnrpfvvIzogIPomZFVbmljb2Rl5a2X56ymXHJcbiAgICB9IGNhdGNoIHtcclxuICAgICAgcmV0dXJuIDEwMDAgLy8g6buY6K6k5Lyw566XXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDinIUg5LyY5YyW77ya5Yik5pat5pWw5o2u5piv5ZCm5Y+v5Yi35pawXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBpc1JlZnJlc2hhYmxlRGF0YShzZXJ2aWNlOiBzdHJpbmcsIG1ldGhvZD86IHN0cmluZyk6IGJvb2xlYW4ge1xyXG4gICAgLy8g57uf6K6h5pWw5o2u44CB54q25oCB5pWw5o2u562J5piv5Y+v5Yi35paw55qEXHJcbiAgICBpZiAobWV0aG9kPy5pbmNsdWRlcygnc3RhdGlzdGljcycpIHx8IG1ldGhvZD8uaW5jbHVkZXMoJ3N0YXR1cycpIHx8IG1ldGhvZD8uaW5jbHVkZXMoJ3V0aWxpemF0aW9uJykpIHtcclxuICAgICAgcmV0dXJuIHRydWVcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8g5bel5L2N5pWw5o2u5piv6aKR57mB5Y+Y5YyW55qEXHJcbiAgICBpZiAoc2VydmljZSA9PT0gJ3dvcmtzdGF0aW9ucycpIHJldHVybiB0cnVlXHJcbiAgICBcclxuICAgIHJldHVybiBmYWxzZVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOS8mOWMlu+8muiusOW9lee8k+WtmOiuv+mXrlxyXG4gICAqL1xyXG4gIHByaXZhdGUgcmVjb3JkQ2FjaGVBY2Nlc3Moa2V5OiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIGNvbnN0IGhpc3RvcnkgPSB0aGlzLmNhY2hlQWNjZXNzSGlzdG9yeS5nZXQoa2V5KSB8fCBbXVxyXG4gICAgaGlzdG9yeS5wdXNoKERhdGUubm93KCkpXHJcbiAgICBcclxuICAgIC8vIOWPquS/neeVmeacgOi/kTEwMOasoeiuv+mXruiusOW9lVxyXG4gICAgaWYgKGhpc3RvcnkubGVuZ3RoID4gMTAwKSB7XHJcbiAgICAgIGhpc3Rvcnkuc3BsaWNlKDAsIGhpc3RvcnkubGVuZ3RoIC0gMTAwKVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICB0aGlzLmNhY2hlQWNjZXNzSGlzdG9yeS5zZXQoa2V5LCBoaXN0b3J5KVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog4pyFIOaetuaehOWQiOinhO+8muiOt+WPlue8k+WtmFRUTFxyXG4gICAqL1xyXG4gIHByaXZhdGUgZ2V0Q2FjaGVUVEwoc2VydmljZTogc3RyaW5nLCBtZXRob2Q6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgICAvLyDmoLnmja7mnI3liqHnsbvlnovlkozmlrnms5Xov5Tlm57kuI3lkIznmoRUVExcclxuICAgIGNvbnN0IHNlcnZpY2VDb25maWc6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XHJcbiAgICAgIG9yZGVyczogMiAqIDYwICogMTAwMCwgICAgICAvLyDorqLljZXmlbDmja46IDLliIbpkp9cclxuICAgICAgcHJvZHVjdHM6IDEwICogNjAgKiAxMDAwLCAgIC8vIOS6p+WTgeaVsOaNrjogMTDliIbpkp9cclxuICAgICAgd29ya3N0YXRpb25zOiAzMCAqIDEwMDAsICAgIC8vIOW3peS9jeaVsOaNrjogMzDnp5JcclxuICAgICAgc3RhdGlzdGljczogMzAgKiAxMDAwLCAgICAgIC8vIOe7n+iuoeaVsOaNrjogMzDnp5JcclxuICAgICAgY3VzdG9tZXJzOiAxNSAqIDYwICogMTAwMCwgIC8vIOWuouaIt+aVsOaNrjogMTXliIbpkp9cclxuICAgICAgZW1wbG95ZWVzOiAzMCAqIDYwICogMTAwMCAgIC8vIOWRmOW3peaVsOaNrjogMzDliIbpkp9cclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIHNlcnZpY2VDb25maWdbc2VydmljZV0gfHwgdGhpcy5jb25maWcuZGVmYXVsdFRUTCB8fCA1ICogNjAgKiAxMDAwIC8vIOm7mOiupDXliIbpkp9cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOWIneWni+WMluaAp+iDveebkeaOp1xyXG4gICAqL1xyXG4gIHByaXZhdGUgaW5pdGlhbGl6ZVBlcmZvcm1hbmNlTW9uaXRvcmluZygpOiB2b2lkIHtcclxuICAgIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzID0ge1xyXG4gICAgICB0b3RhbENhbGxzOiAwLFxyXG4gICAgICBzdWNjZXNzQ2FsbHM6IDAsXHJcbiAgICAgIGVycm9yQ2FsbHM6IDAsXHJcbiAgICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IDAsXHJcbiAgICAgIG1pblJlc3BvbnNlVGltZTogSW5maW5pdHksXHJcbiAgICAgIG1heFJlc3BvbnNlVGltZTogMCxcclxuICAgICAgY2FjaGVIaXRSYXRlOiAwLFxyXG4gICAgICBzbG93UXVlcmllczogW10sXHJcbiAgICAgIG1ldGhvZFN0YXRzOiBuZXcgTWFwKCksXHJcbiAgICAgIGhvdXJseVN0YXRzOiBbXVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOWQr+WKqOWumuacn+a4heeQhuS7u+WKoVxyXG4gICAgdGhpcy5zdGFydFBlcmZvcm1hbmNlQ2xlYW51cFRhc2soKVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5ZCv5Yqo5oCn6IO95pWw5o2u5riF55CG5Lu75YqhXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBzdGFydFBlcmZvcm1hbmNlQ2xlYW51cFRhc2soKTogdm9pZCB7XHJcbiAgICAvLyDmr4/lsI/ml7bmuIXnkIbkuIDmrKHov4fmnJ/nmoTmgKfog73mlbDmja5cclxuICAgIHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgdGhpcy5jbGVhbnVwUGVyZm9ybWFuY2VEYXRhKClcclxuICAgIH0sIDYwICogNjAgKiAxMDAwKSAvLyAx5bCP5pe2XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmuIXnkIbov4fmnJ/nmoTmgKfog73mlbDmja5cclxuICAgKi9cclxuICBwcml2YXRlIGNsZWFudXBQZXJmb3JtYW5jZURhdGEoKTogdm9pZCB7XHJcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpXHJcbiAgICBjb25zdCBvbmVIb3VyQWdvID0gbm93IC0gNjAgKiA2MCAqIDEwMDBcclxuICAgIGNvbnN0IG9uZURheUFnbyA9IG5vdyAtIDI0ICogNjAgKiA2MCAqIDEwMDBcclxuXHJcbiAgICAvLyDmuIXnkIbmnIDov5HosIPnlKjorrDlvZXvvIjkv53nlZkx5bCP5pe277yJXHJcbiAgICB0aGlzLnJlY2VudENhbGxzID0gdGhpcy5yZWNlbnRDYWxscy5maWx0ZXIoY2FsbCA9PiBjYWxsLnRpbWVzdGFtcCA+IG9uZUhvdXJBZ28pXHJcblxyXG4gICAgLy8g5riF55CG5oWi5p+l6K+i6K6w5b2V77yI5L+d55WZMeWkqe+8jOacgOWkmjEwMOadoe+8iVxyXG4gICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3Muc2xvd1F1ZXJpZXMgPSB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5zbG93UXVlcmllc1xyXG4gICAgICAuZmlsdGVyKHF1ZXJ5ID0+IHF1ZXJ5LnRpbWVzdGFtcCA+IG9uZURheUFnbylcclxuICAgICAgLnNsaWNlKC0xMDApXHJcblxyXG4gICAgLy8g5pu05paw5bCP5pe257uf6K6hXHJcbiAgICB0aGlzLnVwZGF0ZUhvdXJseVN0YXRzKClcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOabtOaWsOWwj+aXtue7n+iuoVxyXG4gICAqL1xyXG4gIHByaXZhdGUgdXBkYXRlSG91cmx5U3RhdHMoKTogdm9pZCB7XHJcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXHJcbiAgICBjb25zdCBjdXJyZW50SG91ciA9IGAke25vdy5nZXRGdWxsWWVhcigpfS0ke1N0cmluZyhub3cuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyl9LSR7U3RyaW5nKG5vdy5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyl9ICR7U3RyaW5nKG5vdy5nZXRIb3VycygpKS5wYWRTdGFydCgyLCAnMCcpfTowMGBcclxuXHJcbiAgICBjb25zdCBvbmVIb3VyQWdvID0gRGF0ZS5ub3coKSAtIDYwICogNjAgKiAxMDAwXHJcbiAgICBjb25zdCBob3VyQ2FsbHMgPSB0aGlzLnJlY2VudENhbGxzLmZpbHRlcihjYWxsID0+IGNhbGwudGltZXN0YW1wID4gb25lSG91ckFnbylcclxuXHJcbiAgICBpZiAoaG91ckNhbGxzLmxlbmd0aCA+IDApIHtcclxuICAgICAgY29uc3QgdG90YWxUaW1lID0gaG91ckNhbGxzLnJlZHVjZSgoc3VtLCBjYWxsKSA9PiBzdW0gKyBjYWxsLmR1cmF0aW9uLCAwKVxyXG4gICAgICBjb25zdCBlcnJvckNvdW50ID0gaG91ckNhbGxzLmZpbHRlcihjYWxsID0+IGNhbGwuc3RhdHVzID09PSAnZXJyb3InKS5sZW5ndGhcclxuXHJcbiAgICAgIGNvbnN0IGhvdXJTdGF0ID0ge1xyXG4gICAgICAgIGhvdXI6IGN1cnJlbnRIb3VyLFxyXG4gICAgICAgIGNhbGxzOiBob3VyQ2FsbHMubGVuZ3RoLFxyXG4gICAgICAgIGF2ZXJhZ2VUaW1lOiB0b3RhbFRpbWUgLyBob3VyQ2FsbHMubGVuZ3RoLFxyXG4gICAgICAgIGVycm9yUmF0ZTogZXJyb3JDb3VudCAvIGhvdXJDYWxscy5sZW5ndGhcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8g5pu05paw5oiW5re75Yqg5b2T5YmN5bCP5pe255qE57uf6K6hXHJcbiAgICAgIGNvbnN0IGV4aXN0aW5nSW5kZXggPSB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5ob3VybHlTdGF0cy5maW5kSW5kZXgoc3RhdCA9PiBzdGF0LmhvdXIgPT09IGN1cnJlbnRIb3VyKVxyXG4gICAgICBpZiAoZXhpc3RpbmdJbmRleCA+PSAwKSB7XHJcbiAgICAgICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MuaG91cmx5U3RhdHNbZXhpc3RpbmdJbmRleF0gPSBob3VyU3RhdFxyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmhvdXJseVN0YXRzLnB1c2goaG91clN0YXQpXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIOS/neeVmeacgOi/kTI05bCP5pe255qE57uf6K6hXHJcbiAgICAgIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmhvdXJseVN0YXRzID0gdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MuaG91cmx5U3RhdHMuc2xpY2UoLTI0KVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5pu05paw5oCn6IO95oyH5qCHXHJcbiAgICovXHJcbiAgcHJpdmF0ZSB1cGRhdGVQZXJmb3JtYW5jZU1ldHJpY3MoXHJcbiAgICBvcGVyYXRpb25LZXk6IHN0cmluZyxcclxuICAgIGR1cmF0aW9uOiBudW1iZXIsXHJcbiAgICBzdWNjZXNzOiBib29sZWFuLFxyXG4gICAgY2FjaGVkOiBib29sZWFuID0gZmFsc2VcclxuICApOiB2b2lkIHtcclxuICAgIC8vIOabtOaWsOaAu+S9k+aMh+agh1xyXG4gICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MudG90YWxDYWxscysrXHJcbiAgICBpZiAoc3VjY2Vzcykge1xyXG4gICAgICB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5zdWNjZXNzQ2FsbHMrK1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MuZXJyb3JDYWxscysrXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5pu05paw5ZON5bqU5pe26Ze057uf6K6hXHJcbiAgICB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5taW5SZXNwb25zZVRpbWUgPSBNYXRoLm1pbih0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5taW5SZXNwb25zZVRpbWUsIGR1cmF0aW9uKVxyXG4gICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MubWF4UmVzcG9uc2VUaW1lID0gTWF0aC5tYXgodGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MubWF4UmVzcG9uc2VUaW1lLCBkdXJhdGlvbilcclxuXHJcbiAgICAvLyDmm7TmlrDlubPlnYflk43lupTml7bpl7RcclxuICAgIGNvbnN0IHRvdGFsVGltZSA9IHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmF2ZXJhZ2VSZXNwb25zZVRpbWUgKiAodGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MudG90YWxDYWxscyAtIDEpICsgZHVyYXRpb25cclxuICAgIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmF2ZXJhZ2VSZXNwb25zZVRpbWUgPSB0b3RhbFRpbWUgLyB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy50b3RhbENhbGxzXHJcblxyXG4gICAgLy8g5pu05paw57yT5a2Y5ZG95Lit546HXHJcbiAgICBpZiAoY2FjaGVkKSB7XHJcbiAgICAgIGNvbnN0IGNhY2hlSGl0cyA9IHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLnRvdGFsQ2FsbHMgKiB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5jYWNoZUhpdFJhdGUgKyAxXHJcbiAgICAgIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmNhY2hlSGl0UmF0ZSA9IGNhY2hlSGl0cyAvIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLnRvdGFsQ2FsbHNcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnN0IGNhY2hlSGl0cyA9IHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLnRvdGFsQ2FsbHMgKiB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5jYWNoZUhpdFJhdGVcclxuICAgICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MuY2FjaGVIaXRSYXRlID0gY2FjaGVIaXRzIC8gdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MudG90YWxDYWxsc1xyXG4gICAgfVxyXG5cclxuICAgIC8vIOabtOaWsOaWueazlee7n+iuoVxyXG4gICAgY29uc3QgbWV0aG9kU3RhdCA9IHRoaXMubWV0aG9kU3RhdHMuZ2V0KG9wZXJhdGlvbktleSkgfHwge1xyXG4gICAgICBjYWxsczogMCxcclxuICAgICAgdG90YWxUaW1lOiAwLFxyXG4gICAgICBhdmVyYWdlVGltZTogMCxcclxuICAgICAgc3VjY2Vzc1JhdGU6IDAsXHJcbiAgICAgIGxhc3RDYWxsOiAwLFxyXG4gICAgICBlcnJvcnM6IDBcclxuICAgIH1cclxuXHJcbiAgICBtZXRob2RTdGF0LmNhbGxzKytcclxuICAgIG1ldGhvZFN0YXQudG90YWxUaW1lICs9IGR1cmF0aW9uXHJcbiAgICBtZXRob2RTdGF0LmF2ZXJhZ2VUaW1lID0gbWV0aG9kU3RhdC50b3RhbFRpbWUgLyBtZXRob2RTdGF0LmNhbGxzXHJcbiAgICBtZXRob2RTdGF0Lmxhc3RDYWxsID0gRGF0ZS5ub3coKVxyXG5cclxuICAgIGlmIChzdWNjZXNzKSB7XHJcbiAgICAgIG1ldGhvZFN0YXQuc3VjY2Vzc1JhdGUgPSAobWV0aG9kU3RhdC5zdWNjZXNzUmF0ZSAqIChtZXRob2RTdGF0LmNhbGxzIC0gMSkgKyAxKSAvIG1ldGhvZFN0YXQuY2FsbHNcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIG1ldGhvZFN0YXQuZXJyb3JzKytcclxuICAgICAgbWV0aG9kU3RhdC5zdWNjZXNzUmF0ZSA9IChtZXRob2RTdGF0LnN1Y2Nlc3NSYXRlICogKG1ldGhvZFN0YXQuY2FsbHMgLSAxKSkgLyBtZXRob2RTdGF0LmNhbGxzXHJcbiAgICB9XHJcblxyXG4gICAgdGhpcy5tZXRob2RTdGF0cy5zZXQob3BlcmF0aW9uS2V5LCBtZXRob2RTdGF0KVxyXG5cclxuICAgIC8vIOiusOW9leaFouafpeivolxyXG4gICAgaWYgKGR1cmF0aW9uID4gMTAwMCkgeyAvLyDotoXov4cx56eS55qE5p+l6K+iXHJcbiAgICAgIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLnNsb3dRdWVyaWVzLnB1c2goe1xyXG4gICAgICAgIG1ldGhvZDogb3BlcmF0aW9uS2V5LFxyXG4gICAgICAgIHBhcmFtczoge30sXHJcbiAgICAgICAgZHVyYXRpb24sXHJcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhY2hlZFxyXG4gICAgICB9KVxyXG5cclxuICAgICAgLy8g5L+d5oyB5oWi5p+l6K+i6K6w5b2V5pWw6YeP6ZmQ5Yi2XHJcbiAgICAgIGlmICh0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5zbG93UXVlcmllcy5sZW5ndGggPiAxMDApIHtcclxuICAgICAgICB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5zbG93UXVlcmllcyA9IHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLnNsb3dRdWVyaWVzLnNsaWNlKC01MClcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOiusOW9leacgOi/keiwg+eUqFxyXG4gICAgdGhpcy5yZWNlbnRDYWxscy5wdXNoKHtcclxuICAgICAgbWV0aG9kOiBvcGVyYXRpb25LZXksXHJcbiAgICAgIGR1cmF0aW9uLFxyXG4gICAgICBzdGF0dXM6IHN1Y2Nlc3MgPyAnc3VjY2VzcycgOiAnZXJyb3InLFxyXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXHJcbiAgICAgIGNhY2hlZFxyXG4gICAgfSlcclxuXHJcbiAgICAvLyDkv53mjIHmnIDov5HosIPnlKjorrDlvZXmlbDph4/pmZDliLZcclxuICAgIGlmICh0aGlzLnJlY2VudENhbGxzLmxlbmd0aCA+IDEwMDApIHtcclxuICAgICAgdGhpcy5yZWNlbnRDYWxscyA9IHRoaXMucmVjZW50Q2FsbHMuc2xpY2UoLTUwMClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIOazqOaEj++8mmV4ZWN1dGVXaXRoQ2FjaGluZ+aWueazleW3suenu+WKqOWIsOWGhee9rue8k+WtmOezu+e7n+mDqOWIhlxyXG5cclxuICAvKipcclxuICAgKiDmiafooYzluKbml6Xlv5forrDlvZXlkozmgKfog73nm5HmjqfnmoTmlrnms5XosIPnlKggLSDnu5/kuIDmlLnpgKDniYhcclxuICAgKi9cclxuICBwcml2YXRlIGFzeW5jIGV4ZWN1dGVXaXRoTG9nZ2luZzxUPihcclxuICAgIHNlcnZpY2U6IHN0cmluZyxcclxuICAgIG1ldGhvZDogc3RyaW5nLFxyXG4gICAgcGFyYW1zOiBhbnksXHJcbiAgICBleGVjdXRvcjogKCkgPT4gUHJvbWlzZTxUPlxyXG4gICk6IFByb21pc2U8VD4ge1xyXG4gICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKVxyXG4gICAgY29uc3Qgb3BlcmF0aW9uS2V5ID0gYCR7c2VydmljZX0uJHttZXRob2R9YFxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOS9v+eUqOWGhee9rue8k+WtmOezu+e7n1xyXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmV4ZWN1dGVXaXRoQ2FjaGUoc2VydmljZSwgbWV0aG9kLCBwYXJhbXMsIGV4ZWN1dG9yKVxyXG5cclxuICAgICAgY29uc3QgZHVyYXRpb24gPSBEYXRlLm5vdygpIC0gc3RhcnRUaW1lXHJcblxyXG4gICAgICAvLyDmm7TmlrDmgKfog73mjIfmoIfvvIjmiJDlip/vvIlcclxuICAgICAgdGhpcy51cGRhdGVQZXJmb3JtYW5jZU1ldHJpY3Mob3BlcmF0aW9uS2V5LCBkdXJhdGlvbiwgdHJ1ZSwgZmFsc2UpXHJcblxyXG4gICAgICB0aGlzLmxvZ0FjY2VzcyhzZXJ2aWNlLCBtZXRob2QsIHBhcmFtcywgZHVyYXRpb24sIHRydWUpXHJcbiAgICAgIGRhdGFBY2Nlc3NQZXJmb3JtYW5jZU1vbml0b3IucmVjb3JkUmVxdWVzdChvcGVyYXRpb25LZXksIGR1cmF0aW9uLCB0cnVlLCBwYXJhbXMpXHJcblxyXG4gICAgICByZXR1cm4gcmVzdWx0XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zdCBkdXJhdGlvbiA9IERhdGUubm93KCkgLSBzdGFydFRpbWVcclxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpXHJcblxyXG4gICAgICAvLyDmm7TmlrDmgKfog73mjIfmoIfvvIjplJnor6/vvIlcclxuICAgICAgdGhpcy51cGRhdGVQZXJmb3JtYW5jZU1ldHJpY3Mob3BlcmF0aW9uS2V5LCBkdXJhdGlvbiwgZmFsc2UsIGZhbHNlKVxyXG5cclxuICAgICAgdGhpcy5sb2dBY2Nlc3Moc2VydmljZSwgbWV0aG9kLCBwYXJhbXMsIGR1cmF0aW9uLCBmYWxzZSwgZXJyb3JNZXNzYWdlKVxyXG4gICAgICBkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yLnJlY29yZFJlcXVlc3Qob3BlcmF0aW9uS2V5LCBkdXJhdGlvbiwgZmFsc2UsIHBhcmFtcylcclxuXHJcbiAgICAgIHRocm93IGVycm9yXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBleGVjdXRlV2l0aFVuaWZpZWRDYWNoZeaWueazleW3suenu+mZpO+8jOS9v+eUqOWGhee9rue8k+WtmOezu+e7n1xyXG5cclxuICAvLyBpbnZhbGlkYXRlVW5pZmllZENhY2hl5pa55rOV5bey56e76Zmk77yM5L2/55So5YaF572u57yT5a2Y57O757ufXHJcblxyXG4gIC8vIGV4ZWN1dGVXaXRoV29ya3N0YXRpb25DYWNoaW5n5pa55rOV5bey56e76Zmk77yM5L2/55So5YaF572u57yT5a2Y57O757ufXHJcblxyXG4gIC8vIOe7n+S4gOe8k+WtmOebuOWFs+aWueazleW3suenu+mZpO+8jOS9v+eUqOWGhee9rue8k+WtmOezu+e7n1xyXG5cclxuICAvLyA9PT09PT09PT09PT09PT09PT09PSDkuqflk4HmlbDmja7orr/pl64gPT09PT09PT09PT09PT09PT09PT1cclxuXHJcbiAgLyoqXHJcbiAgICog5Lqn5ZOB5pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IHByb2R1Y3RzKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZ2V0QWxsOiAocGFyYW1zPzogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdFNlcnZpY2UnLCAnZ2V0UHJvZHVjdHMnLCBwYXJhbXMsXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0U2VydmljZS5nZXRQcm9kdWN0cyhwYXJhbXMpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBnZXRCeUlkOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3RTZXJ2aWNlJywgJ2dldFByb2R1Y3RCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdFNlcnZpY2UuZ2V0UHJvZHVjdEJ5SWQoaWQpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBnZXRCeUNvZGU6IChjb2RlOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0U2VydmljZScsICdnZXRQcm9kdWN0QnlDb2RlJywgeyBjb2RlIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0U2VydmljZS5nZXRQcm9kdWN0QnlDb2RlKGNvZGUpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBjcmVhdGU6IChkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0U2VydmljZScsICdjcmVhdGVQcm9kdWN0JywgZGF0YSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3RTZXJ2aWNlLmNyZWF0ZVByb2R1Y3QoZGF0YSlcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIHVwZGF0ZTogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3RTZXJ2aWNlJywgJ3VwZGF0ZVByb2R1Y3QnLCB7IGlkLCB1cGRhdGVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0U2VydmljZS51cGRhdGVQcm9kdWN0KGlkLCB1cGRhdGVzKVxyXG4gICAgICApLFxyXG4gICAgICBcclxuICAgICAgZGVsZXRlOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3RTZXJ2aWNlJywgJ2RlbGV0ZVByb2R1Y3QnLCB7IGlkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0U2VydmljZS5kZWxldGVQcm9kdWN0KGlkKVxyXG4gICAgICApLFxyXG4gICAgICBcclxuICAgICAgZ2V0QWN0aXZlOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdFNlcnZpY2UnLCAnZ2V0QWN0aXZlUHJvZHVjdHMnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3RTZXJ2aWNlLmdldEFjdGl2ZVByb2R1Y3RzKClcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGdldEJ5Q2F0ZWdvcnk6IChjYXRlZ29yeTogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdFNlcnZpY2UnLCAnZ2V0UHJvZHVjdHNCeUNhdGVnb3J5JywgeyBjYXRlZ29yeSB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdFNlcnZpY2UuZ2V0UHJvZHVjdHNCeUNhdGVnb3J5KGNhdGVnb3J5KVxyXG4gICAgICApLFxyXG4gICAgICBcclxuICAgICAgc2VhcmNoOiAoa2V5d29yZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdFNlcnZpY2UnLCAnc2VhcmNoUHJvZHVjdHMnLCB7IGtleXdvcmQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3RTZXJ2aWNlLnNlYXJjaFByb2R1Y3RzKGtleXdvcmQpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBnZXRCeU1vbGQ6IChtb2xkSWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3RTZXJ2aWNlJywgJ2dldFByb2R1Y3RzQnlNb2xkJywgeyBtb2xkSWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3RTZXJ2aWNlLmdldFByb2R1Y3RzQnlNb2xkKG1vbGRJZClcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGdldE1vbGRVc2FnZTogKCkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3RTZXJ2aWNlJywgJ2dldE1vbGRVc2FnZVN0YXRpc3RpY3MnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3RTZXJ2aWNlLmdldE1vbGRVc2FnZVN0YXRpc3RpY3MoKVxyXG4gICAgICApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyA9PT09PT09PT09PT09PT09PT09PSDlrqLmiLfmlbDmja7orr/pl64gPT09PT09PT09PT09PT09PT09PT1cclxuXHJcbiAgLyoqXHJcbiAgICog5a6i5oi35pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IGN1c3RvbWVycygpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGdldEFsbDogKHBhcmFtcz86IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0N1c3RvbWVyU2VydmljZScsICdnZXRDdXN0b21lcnMnLCBwYXJhbXMsXHJcbiAgICAgICAgKCkgPT4gdGhpcy5jdXN0b21lclNlcnZpY2UuZ2V0Q3VzdG9tZXJzKHBhcmFtcylcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGdldEJ5SWQ6IChpZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQ3VzdG9tZXJTZXJ2aWNlJywgJ2dldEN1c3RvbWVyQnlJZCcsIHsgaWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmN1c3RvbWVyU2VydmljZS5nZXRDdXN0b21lckJ5SWQoaWQpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBjcmVhdGU6IChkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDdXN0b21lclNlcnZpY2UnLCAnY3JlYXRlQ3VzdG9tZXInLCBkYXRhLFxyXG4gICAgICAgICgpID0+IHRoaXMuY3VzdG9tZXJTZXJ2aWNlLmNyZWF0ZUN1c3RvbWVyKGRhdGEpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICB1cGRhdGU6IChpZDogc3RyaW5nLCB1cGRhdGVzOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDdXN0b21lclNlcnZpY2UnLCAndXBkYXRlQ3VzdG9tZXInLCB7IGlkLCB1cGRhdGVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5jdXN0b21lclNlcnZpY2UudXBkYXRlQ3VzdG9tZXIoaWQsIHVwZGF0ZXMpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBkZWxldGU6IChpZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQ3VzdG9tZXJTZXJ2aWNlJywgJ2RlbGV0ZUN1c3RvbWVyJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMuY3VzdG9tZXJTZXJ2aWNlLmRlbGV0ZUN1c3RvbWVyKGlkKVxyXG4gICAgICApLFxyXG4gICAgICBcclxuICAgICAgZ2V0QWN0aXZlOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQ3VzdG9tZXJTZXJ2aWNlJywgJ2dldEFjdGl2ZUN1c3RvbWVycycsIHt9LFxyXG4gICAgICAgICgpID0+IHRoaXMuY3VzdG9tZXJTZXJ2aWNlLmdldEFjdGl2ZUN1c3RvbWVycygpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBzZWFyY2g6IChrZXl3b3JkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDdXN0b21lclNlcnZpY2UnLCAnc2VhcmNoQ3VzdG9tZXJzJywgeyBrZXl3b3JkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5jdXN0b21lclNlcnZpY2Uuc2VhcmNoQ3VzdG9tZXJzKGtleXdvcmQpXHJcbiAgICAgIClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vID09PT09PT09PT09PT09PT09PT09IOWRmOW3peaVsOaNruiuv+mXriA9PT09PT09PT09PT09PT09PT09PVxyXG5cclxuICAvKipcclxuICAgKiDlkZjlt6XmlbDmja7orr/pl67lmahcclxuICAgKi9cclxuICBnZXQgZW1wbG95ZWVzKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZ2V0QWxsOiAocGFyYW1zPzogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnRW1wbG95ZWVTZXJ2aWNlJywgJ2dldEVtcGxveWVlcycsIHBhcmFtcyxcclxuICAgICAgICAoKSA9PiB0aGlzLmVtcGxveWVlU2VydmljZS5nZXRFbXBsb3llZXMocGFyYW1zKVxyXG4gICAgICApLFxyXG4gICAgICBcclxuICAgICAgZ2V0QnlJZDogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdFbXBsb3llZVNlcnZpY2UnLCAnZ2V0RW1wbG95ZWVCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMuZW1wbG95ZWVTZXJ2aWNlLmdldEVtcGxveWVlQnlJZChpZClcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGNyZWF0ZTogKGRhdGE6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0VtcGxveWVlU2VydmljZScsICdjcmVhdGVFbXBsb3llZScsIGRhdGEsXHJcbiAgICAgICAgKCkgPT4gdGhpcy5lbXBsb3llZVNlcnZpY2UuY3JlYXRlRW1wbG95ZWUoZGF0YSlcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIHVwZGF0ZTogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0VtcGxveWVlU2VydmljZScsICd1cGRhdGVFbXBsb3llZScsIHsgaWQsIHVwZGF0ZXMgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmVtcGxveWVlU2VydmljZS51cGRhdGVFbXBsb3llZShpZCwgdXBkYXRlcylcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGRlbGV0ZTogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdFbXBsb3llZVNlcnZpY2UnLCAnZGVsZXRlRW1wbG95ZWUnLCB7IGlkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5lbXBsb3llZVNlcnZpY2UuZGVsZXRlRW1wbG95ZWUoaWQpXHJcbiAgICAgICksXHJcbiAgICAgIFxyXG4gICAgICBnZXRBY3RpdmU6ICgpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdFbXBsb3llZVNlcnZpY2UnLCAnZ2V0QWN0aXZlRW1wbG95ZWVzJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5lbXBsb3llZVNlcnZpY2UuZ2V0QWN0aXZlRW1wbG95ZWVzKClcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGdldEJ5RGVwYXJ0bWVudDogKGRlcGFydG1lbnQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0VtcGxveWVlU2VydmljZScsICdnZXRFbXBsb3llZXNCeURlcGFydG1lbnQnLCB7IGRlcGFydG1lbnQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmVtcGxveWVlU2VydmljZS5nZXRFbXBsb3llZXNCeURlcGFydG1lbnQoZGVwYXJ0bWVudClcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGdldEJ5Um9sZTogKHJvbGU6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0VtcGxveWVlU2VydmljZScsICdnZXRFbXBsb3llZXNCeVJvbGUnLCB7IHJvbGUgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmVtcGxveWVlU2VydmljZS5nZXRFbXBsb3llZXNCeVJvbGUocm9sZSlcclxuICAgICAgKSxcclxuICAgICAgXHJcbiAgICAgIGdldFNhbGVzOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnRW1wbG95ZWVTZXJ2aWNlJywgJ2dldFNhbGVzRW1wbG95ZWVzJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5lbXBsb3llZVNlcnZpY2UuZ2V0U2FsZXNFbXBsb3llZXMoKVxyXG4gICAgICApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyA9PT09PT09PT09PT09PT09PT09PSDlupPlrZjmlbDmja7orr/pl64gPT09PT09PT09PT09PT09PT09PT1cclxuXHJcbiAgLyoqXHJcbiAgICog5bqT5a2Y5pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IGludmVudG9yeSgpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGdldEFsbDogKHBhcmFtcz86IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0ludmVudG9yeVNlcnZpY2UnLCAnZ2V0UHJvZHVjdEludmVudG9yeScsIHBhcmFtcyxcclxuICAgICAgICAoKSA9PiB0aGlzLmludmVudG9yeVNlcnZpY2UuZ2V0UHJvZHVjdEludmVudG9yeShwYXJhbXMpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRCeVByb2R1Y3RDb2RlOiAocHJvZHVjdENvZGU6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0ludmVudG9yeVNlcnZpY2UnLCAnZ2V0SW52ZW50b3J5QnlQcm9kdWN0Q29kZScsIHsgcHJvZHVjdENvZGUgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmludmVudG9yeVNlcnZpY2UuZ2V0SW52ZW50b3J5QnlQcm9kdWN0Q29kZShwcm9kdWN0Q29kZSlcclxuICAgICAgKSxcclxuXHJcbiAgICAgIHVwZGF0ZTogKHByb2R1Y3RDb2RlOiBzdHJpbmcsIHVwZGF0ZXM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0ludmVudG9yeVNlcnZpY2UnLCAndXBkYXRlSW52ZW50b3J5JywgeyBwcm9kdWN0Q29kZSwgdXBkYXRlcyB9LFxyXG4gICAgICAgICgpID0+IHRoaXMuaW52ZW50b3J5U2VydmljZS51cGRhdGVJbnZlbnRvcnkocHJvZHVjdENvZGUsIHVwZGF0ZXMpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRMb3dTdG9jazogKHRocmVzaG9sZD86IG51bWJlcikgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0ludmVudG9yeVNlcnZpY2UnLCAnZ2V0TG93U3RvY2tQcm9kdWN0cycsIHsgdGhyZXNob2xkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5pbnZlbnRvcnlTZXJ2aWNlLmdldExvd1N0b2NrUHJvZHVjdHModGhyZXNob2xkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0VmFsdWU6ICgpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdJbnZlbnRvcnlTZXJ2aWNlJywgJ2dldEludmVudG9yeVZhbHVlJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5pbnZlbnRvcnlTZXJ2aWNlLmdldEludmVudG9yeVZhbHVlKClcclxuICAgICAgKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gPT09PT09PT09PT09PT09PT09PT0g6K6k6K+B5pWw5o2u6K6/6ZeuID09PT09PT09PT09PT09PT09PT09XHJcblxyXG4gIC8qKlxyXG4gICAqIOiupOivgeaVsOaNruiuv+mXruWZqFxyXG4gICAqL1xyXG4gIGdldCBhdXRoKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLy8g6K6k6K+B55u45YWzXHJcbiAgICAgIGxvZ2luOiAoY3JlZGVudGlhbHM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0F1dGhTZXJ2aWNlJywgJ2xvZ2luJywgY3JlZGVudGlhbHMsXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS5sb2dpbihjcmVkZW50aWFscylcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGxvZ291dDogKHJlZnJlc2hUb2tlbjogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQXV0aFNlcnZpY2UnLCAnbG9nb3V0JywgeyByZWZyZXNoVG9rZW4gfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmF1dGhTZXJ2aWNlLmxvZ291dChyZWZyZXNoVG9rZW4pXHJcbiAgICAgICksXHJcblxyXG4gICAgICByZWZyZXNoVG9rZW46IChyZXF1ZXN0OiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdyZWZyZXNoVG9rZW4nLCByZXF1ZXN0LFxyXG4gICAgICAgICgpID0+IHRoaXMuYXV0aFNlcnZpY2UucmVmcmVzaFRva2VuKHJlcXVlc3QpXHJcbiAgICAgICksXHJcblxyXG4gICAgICB2YWxpZGF0ZVRva2VuOiAodG9rZW46IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0F1dGhTZXJ2aWNlJywgJ3ZhbGlkYXRlVG9rZW4nLCB7IHRva2VuIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS52YWxpZGF0ZVRva2VuKHRva2VuKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8g55So5oi3566h55CGXHJcbiAgICAgIGdldFVzZXJCeUlkOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0F1dGhTZXJ2aWNlJywgJ2dldFVzZXJCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMuYXV0aFNlcnZpY2UuZ2V0VXNlckJ5SWQoaWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRVc2VyQnlVc2VybmFtZTogKHVzZXJuYW1lOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdnZXRVc2VyQnlVc2VybmFtZScsIHsgdXNlcm5hbWUgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmF1dGhTZXJ2aWNlLmdldFVzZXJCeVVzZXJuYW1lKHVzZXJuYW1lKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgY3JlYXRlVXNlcjogKHVzZXJEYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdjcmVhdGVVc2VyJywgdXNlckRhdGEsXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS5jcmVhdGVVc2VyKHVzZXJEYXRhKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgdXBkYXRlVXNlcjogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0F1dGhTZXJ2aWNlJywgJ3VwZGF0ZVVzZXInLCB7IGlkLCB1cGRhdGVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS51cGRhdGVVc2VyKGlkLCB1cGRhdGVzKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZGVsZXRlVXNlcjogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdkZWxldGVVc2VyJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMuYXV0aFNlcnZpY2UuZGVsZXRlVXNlcihpZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIC8vIOS8muivneeuoeeQhlxyXG4gICAgICBjcmVhdGVTZXNzaW9uOiAodXNlcklkOiBzdHJpbmcsIHNlc3Npb25EYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdjcmVhdGVTZXNzaW9uJywgeyB1c2VySWQsIHNlc3Npb25EYXRhIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS5jcmVhdGVTZXNzaW9uKHVzZXJJZCwgc2Vzc2lvbkRhdGEpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRTZXNzaW9uOiAoc2Vzc2lvbklkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdnZXRTZXNzaW9uJywgeyBzZXNzaW9uSWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmF1dGhTZXJ2aWNlLmdldFNlc3Npb24oc2Vzc2lvbklkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgdXBkYXRlU2Vzc2lvbjogKHNlc3Npb25JZDogc3RyaW5nLCB1cGRhdGVzOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICd1cGRhdGVTZXNzaW9uJywgeyBzZXNzaW9uSWQsIHVwZGF0ZXMgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmF1dGhTZXJ2aWNlLnVwZGF0ZVNlc3Npb24oc2Vzc2lvbklkLCB1cGRhdGVzKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZGVsZXRlU2Vzc2lvbjogKHNlc3Npb25JZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQXV0aFNlcnZpY2UnLCAnZGVsZXRlU2Vzc2lvbicsIHsgc2Vzc2lvbklkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS5kZWxldGVTZXNzaW9uKHNlc3Npb25JZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldFVzZXJTZXNzaW9uczogKHVzZXJJZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQXV0aFNlcnZpY2UnLCAnZ2V0VXNlclNlc3Npb25zJywgeyB1c2VySWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmF1dGhTZXJ2aWNlLmdldFVzZXJTZXNzaW9ucyh1c2VySWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICAvLyDlr4bnoIHnrqHnkIZcclxuICAgICAgY2hhbmdlUGFzc3dvcmQ6ICh1c2VySWQ6IHN0cmluZywgY3VycmVudFBhc3N3b3JkOiBzdHJpbmcsIG5ld1Bhc3N3b3JkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdjaGFuZ2VQYXNzd29yZCcsIHsgdXNlcklkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5hdXRoU2VydmljZS5jaGFuZ2VQYXNzd29yZCh1c2VySWQsIGN1cnJlbnRQYXNzd29yZCwgbmV3UGFzc3dvcmQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICByZXNldFBhc3N3b3JkOiAodXNlcklkOiBzdHJpbmcsIG5ld1Bhc3N3b3JkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdBdXRoU2VydmljZScsICdyZXNldFBhc3N3b3JkJywgeyB1c2VySWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmF1dGhTZXJ2aWNlLnJlc2V0UGFzc3dvcmQodXNlcklkLCBuZXdQYXNzd29yZClcclxuICAgICAgKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gPT09PT09PT09PT09PT09PT09PT0gVG9rZW7nrqHnkIbmlbDmja7orr/pl64gPT09PT09PT09PT09PT09PT09PT1cclxuXHJcbiAgLyoqXHJcbiAgICogVG9rZW7nrqHnkIbmlbDmja7orr/pl67lmahcclxuICAgKi9cclxuICBnZXQgdG9rZW5NYW5hZ2VtZW50KCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLy8gVG9rZW7nlJ/miJBcclxuICAgICAgZ2VuZXJhdGVBY2Nlc3NUb2tlbjogKHBheWxvYWQ6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Rva2VuTWFuYWdlbWVudFNlcnZpY2UnLCAnZ2VuZXJhdGVBY2Nlc3NUb2tlbicsIHBheWxvYWQsXHJcbiAgICAgICAgKCkgPT4gdGhpcy50b2tlbk1hbmFnZW1lbnRTZXJ2aWNlLmdlbmVyYXRlQWNjZXNzVG9rZW4ocGF5bG9hZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdlbmVyYXRlUmVmcmVzaFRva2VuOiAodXNlcklkOiBzdHJpbmcsIHNlc3Npb25JZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnVG9rZW5NYW5hZ2VtZW50U2VydmljZScsICdnZW5lcmF0ZVJlZnJlc2hUb2tlbicsIHsgdXNlcklkLCBzZXNzaW9uSWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnRva2VuTWFuYWdlbWVudFNlcnZpY2UuZ2VuZXJhdGVSZWZyZXNoVG9rZW4odXNlcklkLCBzZXNzaW9uSWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICAvLyBUb2tlbumqjOivgVxyXG4gICAgICB2ZXJpZnlBY2Nlc3NUb2tlbjogKHRva2VuOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlJywgJ3ZlcmlmeUFjY2Vzc1Rva2VuJywgeyB0b2tlbkxlbmd0aDogdG9rZW4/Lmxlbmd0aCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMudG9rZW5NYW5hZ2VtZW50U2VydmljZS52ZXJpZnlBY2Nlc3NUb2tlbih0b2tlbilcclxuICAgICAgKSxcclxuXHJcbiAgICAgIHZlcmlmeVJlZnJlc2hUb2tlbjogKHRva2VuOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlJywgJ3ZlcmlmeVJlZnJlc2hUb2tlbicsIHsgdG9rZW5MZW5ndGg6IHRva2VuPy5sZW5ndGggfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnRva2VuTWFuYWdlbWVudFNlcnZpY2UudmVyaWZ5UmVmcmVzaFRva2VuKHRva2VuKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8gVG9rZW7lt6Xlhbfmlrnms5VcclxuICAgICAgZXh0cmFjdFVzZXJJbmZvOiAodG9rZW46IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Rva2VuTWFuYWdlbWVudFNlcnZpY2UnLCAnZXh0cmFjdFVzZXJJbmZvJywgeyB0b2tlbkxlbmd0aDogdG9rZW4/Lmxlbmd0aCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMudG9rZW5NYW5hZ2VtZW50U2VydmljZS5leHRyYWN0VXNlckluZm8odG9rZW4pXHJcbiAgICAgICksXHJcblxyXG4gICAgICBzaG91bGRSZWZyZXNoVG9rZW46ICh0b2tlbjogc3RyaW5nLCB0aHJlc2hvbGRNaW51dGVzPzogbnVtYmVyKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnVG9rZW5NYW5hZ2VtZW50U2VydmljZScsICdzaG91bGRSZWZyZXNoVG9rZW4nLCB7IHRva2VuTGVuZ3RoOiB0b2tlbj8ubGVuZ3RoLCB0aHJlc2hvbGRNaW51dGVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy50b2tlbk1hbmFnZW1lbnRTZXJ2aWNlLnNob3VsZFJlZnJlc2hUb2tlbih0b2tlbiwgdGhyZXNob2xkTWludXRlcylcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldFRva2VuUmVtYWluaW5nVGltZTogKHRva2VuOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlJywgJ2dldFRva2VuUmVtYWluaW5nVGltZScsIHsgdG9rZW5MZW5ndGg6IHRva2VuPy5sZW5ndGggfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnRva2VuTWFuYWdlbWVudFNlcnZpY2UuZ2V0VG9rZW5SZW1haW5pbmdUaW1lKHRva2VuKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2VuZXJhdGVTZXNzaW9uSWQ6ICgpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlJywgJ2dlbmVyYXRlU2Vzc2lvbklkJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy50b2tlbk1hbmFnZW1lbnRTZXJ2aWNlLmdlbmVyYXRlU2Vzc2lvbklkKClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIC8vIOmFjee9ruajgOafpVxyXG4gICAgICBjaGVja0NvbmZpZ3VyYXRpb246ICgpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlJywgJ2NoZWNrQ29uZmlndXJhdGlvbicsIHt9LFxyXG4gICAgICAgICgpID0+IHRoaXMudG9rZW5NYW5hZ2VtZW50U2VydmljZS5jaGVja0NvbmZpZ3VyYXRpb24oKVxyXG4gICAgICApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyA9PT09PT09PT09PT09PT09PT09PSDop5LoibLmnYPpmZDmlbDmja7orr/pl64gPT09PT09PT09PT09PT09PT09PT1cclxuXHJcbiAgLyoqXHJcbiAgICog6KeS6Imy5p2D6ZmQ5pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IHJvbGVzKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLy8g6KeS6Imy566h55CGXHJcbiAgICAgIGdldEFsbDogKCkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1JvbGVTZXJ2aWNlJywgJ2dldFJvbGVzJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5yb2xlU2VydmljZS5nZXRSb2xlcygpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRCeUlkOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1JvbGVTZXJ2aWNlJywgJ2dldFJvbGVCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucm9sZVNlcnZpY2UuZ2V0Um9sZUJ5SWQoaWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRCeUNvZGU6IChjb2RlOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdSb2xlU2VydmljZScsICdnZXRSb2xlQnlDb2RlJywgeyBjb2RlIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5yb2xlU2VydmljZS5nZXRSb2xlQnlDb2RlKGNvZGUpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBjcmVhdGU6IChyb2xlRGF0YTogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUm9sZVNlcnZpY2UnLCAnY3JlYXRlUm9sZScsIHJvbGVEYXRhLFxyXG4gICAgICAgICgpID0+IHRoaXMucm9sZVNlcnZpY2UuY3JlYXRlUm9sZShyb2xlRGF0YSlcclxuICAgICAgKSxcclxuXHJcbiAgICAgIHVwZGF0ZTogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1JvbGVTZXJ2aWNlJywgJ3VwZGF0ZVJvbGUnLCB7IGlkLCB1cGRhdGVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5yb2xlU2VydmljZS51cGRhdGVSb2xlKGlkLCB1cGRhdGVzKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZGVsZXRlOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1JvbGVTZXJ2aWNlJywgJ2RlbGV0ZVJvbGUnLCB7IGlkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5yb2xlU2VydmljZS5kZWxldGVSb2xlKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8g5p2D6ZmQ566h55CGXHJcbiAgICAgIGdldFBlcm1pc3Npb25zOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUm9sZVNlcnZpY2UnLCAnZ2V0UGVybWlzc2lvbnMnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLnJvbGVTZXJ2aWNlLmdldFBlcm1pc3Npb25zKClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldFBlcm1pc3Npb25zQnlNb2R1bGU6IChtb2R1bGU6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1JvbGVTZXJ2aWNlJywgJ2dldFBlcm1pc3Npb25zQnlNb2R1bGUnLCB7IG1vZHVsZSB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucm9sZVNlcnZpY2UuZ2V0UGVybWlzc2lvbnNCeU1vZHVsZShtb2R1bGUpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBhc3NpZ25QZXJtaXNzaW9uczogKHJvbGVJZDogc3RyaW5nLCBwZXJtaXNzaW9uSWRzOiBzdHJpbmdbXSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1JvbGVTZXJ2aWNlJywgJ2Fzc2lnblJvbGVQZXJtaXNzaW9ucycsIHsgcm9sZUlkLCBwZXJtaXNzaW9uSWRzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5yb2xlU2VydmljZS5hc3NpZ25Sb2xlUGVybWlzc2lvbnMocm9sZUlkLCBwZXJtaXNzaW9uSWRzKVxyXG4gICAgICApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDorqLljZXmlbDmja7orr/pl67lmahcclxuICAgKi9cclxuICBnZXQgb3JkZXJzKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZ2V0QWxsOiAocGFyYW1zPzogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RhdGFBY2Nlc3NNYW5hZ2VyXSBvcmRlcnMuZ2V0QWxsIOiiq+iwg+eUqO+8jOeuoeeQhuWZqElEOicsICh0aGlzIGFzIGFueSkuX19tYW5hZ2VySWQpXHJcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW0RhdGFBY2Nlc3NNYW5hZ2VyXSDkvb/nlKjnmoRPcmRlclNlcnZpY2Xlrp7kvotJRDonLCAodGhpcy5vcmRlclNlcnZpY2UgYXMgYW55KS5fX3NlcnZpY2VJZClcclxuICAgICAgICByZXR1cm4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgICAnT3JkZXJTZXJ2aWNlJywgJ2dldE9yZGVycycsIHBhcmFtcyxcclxuICAgICAgICAgICgpID0+IHRoaXMub3JkZXJTZXJ2aWNlLmdldE9yZGVycyhwYXJhbXMpXHJcbiAgICAgICAgKVxyXG4gICAgICB9LFxyXG5cclxuICAgICAgZ2V0QnlJZDogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdPcmRlclNlcnZpY2UnLCAnZ2V0T3JkZXJCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMub3JkZXJTZXJ2aWNlLmdldE9yZGVyQnlJZChpZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldEJ5TnVtYmVyOiAob3JkZXJOdW1iZXI6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ09yZGVyU2VydmljZScsICdnZXRPcmRlckJ5TnVtYmVyJywgeyBvcmRlck51bWJlciB9LFxyXG4gICAgICAgICgpID0+IHRoaXMub3JkZXJTZXJ2aWNlLmdldE9yZGVyQnlOdW1iZXIob3JkZXJOdW1iZXIpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBjcmVhdGU6IChkYXRhOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbRGF0YUFjY2Vzc01hbmFnZXJdIG9yZGVycy5jcmVhdGUg6KKr6LCD55So77yM566h55CG5ZmoSUQ6JywgKHRoaXMgYXMgYW55KS5fX21hbmFnZXJJZClcclxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbRGF0YUFjY2Vzc01hbmFnZXJdIOS9v+eUqOeahE9yZGVyU2VydmljZeWunuS+i0lEOicsICh0aGlzLm9yZGVyU2VydmljZSBhcyBhbnkpLl9fc2VydmljZUlkKVxyXG4gICAgICAgIHJldHVybiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAgICdPcmRlclNlcnZpY2UnLCAnY3JlYXRlT3JkZXInLCBkYXRhLFxyXG4gICAgICAgICAgKCkgPT4gdGhpcy5vcmRlclNlcnZpY2UuY3JlYXRlT3JkZXIoZGF0YSlcclxuICAgICAgICApXHJcbiAgICAgIH0sXHJcblxyXG4gICAgICB1cGRhdGU6IChpZDogc3RyaW5nLCB1cGRhdGVzOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdPcmRlclNlcnZpY2UnLCAndXBkYXRlT3JkZXInLCB7IGlkLCB1cGRhdGVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5vcmRlclNlcnZpY2UudXBkYXRlT3JkZXIoaWQsIHVwZGF0ZXMpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBkZWxldGU6IChpZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnT3JkZXJTZXJ2aWNlJywgJ2RlbGV0ZU9yZGVyJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMub3JkZXJTZXJ2aWNlLmRlbGV0ZU9yZGVyKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlTdGF0dXM6IChzdGF0dXM6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ09yZGVyU2VydmljZScsICdnZXRPcmRlcnNCeVN0YXR1cycsIHsgc3RhdHVzIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5vcmRlclNlcnZpY2UuZ2V0T3JkZXJzQnlTdGF0dXMoc3RhdHVzKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlDdXN0b21lcjogKGN1c3RvbWVySWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ09yZGVyU2VydmljZScsICdnZXRPcmRlcnNCeUN1c3RvbWVyJywgeyBjdXN0b21lcklkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5vcmRlclNlcnZpY2UuZ2V0T3JkZXJzQnlDdXN0b21lcihjdXN0b21lcklkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlTYWxlc1JlcDogKHNhbGVzUmVwSWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ09yZGVyU2VydmljZScsICdnZXRPcmRlcnNCeVNhbGVzUmVwJywgeyBzYWxlc1JlcElkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5vcmRlclNlcnZpY2UuZ2V0T3JkZXJzQnlTYWxlc1JlcChzYWxlc1JlcElkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlEYXRlUmFuZ2U6IChzdGFydERhdGU6IHN0cmluZywgZW5kRGF0ZTogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnT3JkZXJTZXJ2aWNlJywgJ2dldE9yZGVyc0J5RGF0ZVJhbmdlJywgeyBzdGFydERhdGUsIGVuZERhdGUgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLm9yZGVyU2VydmljZS5nZXRPcmRlcnNCeURhdGVSYW5nZShzdGFydERhdGUsIGVuZERhdGUpXHJcbiAgICAgICksXHJcblxyXG4gICAgICAvLyDorqLljZXlj7fnlJ/miJDlkozpqozor4Hmlrnms5Xlt7Lnp7vpmaRcclxuICAgICAgLy8g6K+355u05o6l5L2/55SoIEJ1c2luZXNzSWRHZW5lcmF0b3Ig55qE55u45YWz5pa55rOVXHJcbiAgICAgIC8vIOajgOafpeiuouWNleaYr+WQpuWtmOWcqOivt+S9v+eUqCBnZXRCeU51bWJlcigpIOaWueazlVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5bel5L2c5pe26Ze06YWN572u5pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IHdvcmtUaW1lKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZ2V0Q29uZmlndXJhdGlvbnM6ICgpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3JrVGltZVNlcnZpY2UnLCAnZ2V0Q29uZmlndXJhdGlvbnMnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtUaW1lU2VydmljZS5nZXRDb25maWd1cmF0aW9ucygpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRCeUlkOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtUaW1lU2VydmljZScsICdnZXRDb25maWd1cmF0aW9uQnlJZCcsIHsgaWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtUaW1lU2VydmljZS5nZXRDb25maWd1cmF0aW9uQnlJZChpZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGNyZWF0ZTogKGRhdGE6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtUaW1lU2VydmljZScsICdjcmVhdGVDb25maWd1cmF0aW9uJywgZGF0YSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtUaW1lU2VydmljZS5jcmVhdGVDb25maWd1cmF0aW9uKGRhdGEpXHJcbiAgICAgICksXHJcblxyXG4gICAgICB1cGRhdGU6IChpZDogc3RyaW5nLCBkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3JrVGltZVNlcnZpY2UnLCAndXBkYXRlQ29uZmlndXJhdGlvbicsIHsgaWQsIC4uLmRhdGEgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtUaW1lU2VydmljZS51cGRhdGVDb25maWd1cmF0aW9uKGlkLCBkYXRhKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZGVsZXRlOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtUaW1lU2VydmljZScsICdkZWxldGVDb25maWd1cmF0aW9uJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMud29ya1RpbWVTZXJ2aWNlLmRlbGV0ZUNvbmZpZ3VyYXRpb24oaWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBhZGRXb3JrVGltZVNsb3Q6IChjb25maWdJZDogc3RyaW5nLCBzbG90OiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3JrVGltZVNlcnZpY2UnLCAnYWRkV29ya1RpbWVTbG90JywgeyBjb25maWdJZCwgc2xvdCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMud29ya1RpbWVTZXJ2aWNlLmFkZFdvcmtUaW1lU2xvdChjb25maWdJZCwgc2xvdClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIHVwZGF0ZVdvcmtUaW1lU2xvdDogKGNvbmZpZ0lkOiBzdHJpbmcsIHNsb3RJZDogc3RyaW5nLCBkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3JrVGltZVNlcnZpY2UnLCAndXBkYXRlV29ya1RpbWVTbG90JywgeyBjb25maWdJZCwgc2xvdElkLCAuLi5kYXRhIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy53b3JrVGltZVNlcnZpY2UudXBkYXRlV29ya1RpbWVTbG90KGNvbmZpZ0lkLCBzbG90SWQsIGRhdGEpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBkZWxldGVXb3JrVGltZVNsb3Q6IChjb25maWdJZDogc3RyaW5nLCBzbG90SWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtUaW1lU2VydmljZScsICdkZWxldGVXb3JrVGltZVNsb3QnLCB7IGNvbmZpZ0lkLCBzbG90SWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtUaW1lU2VydmljZS5kZWxldGVXb3JrVGltZVNsb3QoY29uZmlnSWQsIHNsb3RJZClcclxuICAgICAgKSxcclxuXHJcblxyXG5cclxuICAgICAgY2FsY3VsYXRlV29ya2luZ01pbnV0ZXM6ICh3b3JrVGltZVNsb3RzOiBhbnlbXSwgYnJlYWtUaW1lU2xvdHM/OiBhbnlbXSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KClcclxuICAgICAgICBjb25zdCByZXN1bHQgPSB0aGlzLndvcmtUaW1lU2VydmljZS5jYWxjdWxhdGVXb3JraW5nTWludXRlcyh3b3JrVGltZVNsb3RzLCBicmVha1RpbWVTbG90cylcclxuICAgICAgICBjb25zdCBkdXJhdGlvbiA9IERhdGUubm93KCkgLSBzdGFydFRpbWVcclxuICAgICAgICB0aGlzLmxvZ0FjY2VzcygnV29ya1RpbWVTZXJ2aWNlJywgJ2NhbGN1bGF0ZVdvcmtpbmdNaW51dGVzJywgeyB3b3JrVGltZVNsb3RzLCBicmVha1RpbWVTbG90cyB9LCBkdXJhdGlvbiwgdHJ1ZSlcclxuICAgICAgICByZXR1cm4gcmVzdWx0XHJcbiAgICAgIH0sXHJcblxyXG4gICAgICB2YWxpZGF0ZVRpbWVTbG90OiAoc3RhcnRUaW1lOiBzdHJpbmcsIGVuZFRpbWU6IHN0cmluZykgPT4ge1xyXG4gICAgICAgIGNvbnN0IHN0YXJ0ID0gRGF0ZS5ub3coKVxyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMud29ya1RpbWVTZXJ2aWNlLnZhbGlkYXRlVGltZVNsb3Qoc3RhcnRUaW1lLCBlbmRUaW1lKVxyXG4gICAgICAgIGNvbnN0IGR1cmF0aW9uID0gRGF0ZS5ub3coKSAtIHN0YXJ0XHJcbiAgICAgICAgdGhpcy5sb2dBY2Nlc3MoJ1dvcmtUaW1lU2VydmljZScsICd2YWxpZGF0ZVRpbWVTbG90JywgeyBzdGFydFRpbWUsIGVuZFRpbWUgfSwgZHVyYXRpb24sIHRydWUpXHJcbiAgICAgICAgcmV0dXJuIHJlc3VsdFxyXG4gICAgICB9LFxyXG5cclxuICAgICAgZ2V0RGVmYXVsdDogKCkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtUaW1lU2VydmljZScsICdnZXREZWZhdWx0Q29uZmlndXJhdGlvbicsIHt9LFxyXG4gICAgICAgICgpID0+IHRoaXMud29ya1RpbWVTZXJ2aWNlLmdldERlZmF1bHRDb25maWd1cmF0aW9uKClcclxuICAgICAgKVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog55Sf5Lqn6K6i5Y2V5pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IHByb2R1Y3Rpb25PcmRlcnMoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBnZXRBbGw6IChwYXJhbXM/OiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uT3JkZXJTZXJ2aWNlJywgJ2dldEFsbCcsIHBhcmFtcyxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3Rpb25PcmRlclNlcnZpY2UuZ2V0QWxsKHBhcmFtcylcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldEJ5SWQ6IChpZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbk9yZGVyU2VydmljZScsICdnZXRCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdGlvbk9yZGVyU2VydmljZS5nZXRCeUlkKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlPcmRlck51bWJlcjogKG9yZGVyTnVtYmVyOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uT3JkZXJTZXJ2aWNlJywgJ2dldEJ5T3JkZXJOdW1iZXInLCB7IG9yZGVyTnVtYmVyIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uT3JkZXJTZXJ2aWNlLmdldEJ5T3JkZXJOdW1iZXIob3JkZXJOdW1iZXIpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBjcmVhdGVGcm9tTVJQOiAoZGF0YTogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbk9yZGVyU2VydmljZScsICdjcmVhdGVGcm9tTVJQJywgZGF0YSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3Rpb25PcmRlclNlcnZpY2UuY3JlYXRlRnJvbU1SUChkYXRhKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8g5re75Yqg6YCa55So55qEY3JlYXRl5pa55rOV77yI55So5LqO5rWL6K+V77yJXHJcbiAgICAgIGNyZWF0ZTogKGRhdGE6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3Rpb25PcmRlclNlcnZpY2UnLCAnY3JlYXRlRnJvbU1SUCcsIGRhdGEsXHJcbiAgICAgICAgKCkgPT4ge1xyXG4gICAgICAgICAgLy8g5Li65rWL6K+V55uu55qE5re75Yqg6buY6K6kTVJQ5a2X5q61XHJcbiAgICAgICAgICBjb25zdCBtcnBEYXRhID0ge1xyXG4gICAgICAgICAgICAuLi5kYXRhLFxyXG4gICAgICAgICAgICBtcnBFeGVjdXRpb25JZDogZGF0YS5tcnBFeGVjdXRpb25JZCB8fCBgbXJwXyR7RGF0ZS5ub3coKX1gLFxyXG4gICAgICAgICAgICBtcnBFeGVjdXRlZEJ5OiBkYXRhLm1ycEV4ZWN1dGVkQnkgfHwgJ3Rlc3QtdXNlcicsXHJcbiAgICAgICAgICAgIG1ycEV4ZWN1dGVkQXQ6IGRhdGEubXJwRXhlY3V0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIHJldHVybiB0aGlzLnByb2R1Y3Rpb25PcmRlclNlcnZpY2UuY3JlYXRlRnJvbU1SUChtcnBEYXRhKVxyXG4gICAgICAgIH1cclxuICAgICAgKSxcclxuXHJcblxyXG5cclxuICAgICAgdXBkYXRlOiAoaWQ6IHN0cmluZywgZGF0YTogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbk9yZGVyU2VydmljZScsICd1cGRhdGUnLCB7IGlkLCAuLi5kYXRhIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uT3JkZXJTZXJ2aWNlLnVwZGF0ZShpZCwgZGF0YSlcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGRlbGV0ZTogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uT3JkZXJTZXJ2aWNlJywgJ2RlbGV0ZScsIHsgaWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3Rpb25PcmRlclNlcnZpY2UuZGVsZXRlKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlTdGF0dXM6IChzdGF0dXM6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3Rpb25PcmRlclNlcnZpY2UnLCAnZ2V0QnlTdGF0dXMnLCB7IHN0YXR1cyB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdGlvbk9yZGVyU2VydmljZS5nZXRCeVN0YXR1cyhzdGF0dXMpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRCeVNhbGVzT3JkZXJJZDogKHNhbGVzT3JkZXJJZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbk9yZGVyU2VydmljZScsICdnZXRCeVNhbGVzT3JkZXJJZCcsIHsgc2FsZXNPcmRlcklkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uT3JkZXJTZXJ2aWNlLmdldEJ5U2FsZXNPcmRlcklkKHNhbGVzT3JkZXJJZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIC8vIOa3u+WKoOe7n+iuoeaWueazlVxyXG4gICAgICBnZXRTdGF0aXN0aWNzOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbk9yZGVyU2VydmljZScsICdnZXRTdGF0aXN0aWNzJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uT3JkZXJTZXJ2aWNlLmdldFN0YXRpc3RpY3MoKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8g8J+UpyDmlrDlop7vvJrph43nva7miYDmnInorqLljZXnirbmgIHliLDliJ3lp4vnirbmgIFcclxuXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDnlJ/kuqflt6XljZXmlbDmja7orr/pl67lmahcclxuICAgKi9cclxuICBnZXQgcHJvZHVjdGlvbldvcmtPcmRlcnMoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBnZXRBbGw6IChwYXJhbXM/OiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uV29ya09yZGVyU2VydmljZScsICdnZXRBbGwnLCBwYXJhbXMsXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uV29ya09yZGVyU2VydmljZS5nZXRBbGwocGFyYW1zKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlJZDogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uV29ya09yZGVyU2VydmljZScsICdnZXRCeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UuZ2V0QnlJZChpZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldEJ5QmF0Y2hOdW1iZXI6IChiYXRjaE51bWJlcjogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UnLCAnZ2V0QnlCYXRjaE51bWJlcicsIHsgYmF0Y2hOdW1iZXIgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3Rpb25Xb3JrT3JkZXJTZXJ2aWNlLmdldEJ5QmF0Y2hOdW1iZXIoYmF0Y2hOdW1iZXIpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBjcmVhdGU6IChkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uV29ya09yZGVyU2VydmljZScsICdjcmVhdGUnLCBkYXRhLFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UuY3JlYXRlKGRhdGEpXHJcbiAgICAgICksXHJcblxyXG4gICAgICB1cGRhdGU6IChpZDogc3RyaW5nLCBkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdQcm9kdWN0aW9uV29ya09yZGVyU2VydmljZScsICd1cGRhdGUnLCB7IGlkLCAuLi5kYXRhIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uV29ya09yZGVyU2VydmljZS51cGRhdGUoaWQsIGRhdGEpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBkZWxldGU6IChpZDogc3RyaW5nKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnUHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UnLCAnZGVsZXRlJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UuZGVsZXRlKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlTdGF0dXM6IChzdGF0dXM6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3Rpb25Xb3JrT3JkZXJTZXJ2aWNlJywgJ2dldEJ5U3RhdHVzJywgeyBzdGF0dXMgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLnByb2R1Y3Rpb25Xb3JrT3JkZXJTZXJ2aWNlLmdldEJ5U3RhdHVzKHN0YXR1cylcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldEJ5U291cmNlT3JkZXJJZDogKHNvdXJjZU9yZGVySWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3Rpb25Xb3JrT3JkZXJTZXJ2aWNlJywgJ2dldEJ5U291cmNlT3JkZXJJZCcsIHsgc291cmNlT3JkZXJJZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMucHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UuZ2V0QnlTb3VyY2VPcmRlcklkKHNvdXJjZU9yZGVySWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRCeVdvcmtzdGF0aW9uOiAod29ya3N0YXRpb246IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1Byb2R1Y3Rpb25Xb3JrT3JkZXJTZXJ2aWNlJywgJ2dldEJ5V29ya3N0YXRpb24nLCB7IHdvcmtzdGF0aW9uIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5wcm9kdWN0aW9uV29ya09yZGVyU2VydmljZS5nZXRCeVdvcmtzdGF0aW9uKHdvcmtzdGF0aW9uKVxyXG4gICAgICApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDlt6XkvY3mlbDmja7orr/pl67lmahcclxuICAgKiDwn5SnIOS/ruWkje+8muS4uuW3peS9jeaVsOaNrua3u+WKoOeJueauiueahOe8k+WtmOetlueVpe+8jOehruS/neaOkueoi+eul+azleiOt+WPluacgOaWsOeKtuaAgVxyXG4gICAqL1xyXG4gIGdldCB3b3Jrc3RhdGlvbnMoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAvLyDmn6Xor6Lmlrnms5UgLSDkvb/nlKjlhoXnva7nvJPlrZjns7vnu59cclxuICAgICAgZ2V0QWxsOiAocGFyYW1zPzogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnV29ya3N0YXRpb25TZXJ2aWNlJywgJ2dldEFsbCcsIHBhcmFtcyxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtzdGF0aW9uU2VydmljZS5nZXRBbGwoKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0V29ya3N0YXRpb25zOiAocGFyYW1zPzogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnV29ya3N0YXRpb25TZXJ2aWNlJywgJ2dldFdvcmtzdGF0aW9ucycsIHBhcmFtcyxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtzdGF0aW9uU2VydmljZS5nZXRXb3Jrc3RhdGlvbnMocGFyYW1zKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0QnlJZDogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3Jrc3RhdGlvblNlcnZpY2UnLCAnZ2V0V29ya3N0YXRpb25CeUlkJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMud29ya3N0YXRpb25TZXJ2aWNlLmdldFdvcmtzdGF0aW9uQnlJZChpZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldEFjdGl2ZVdvcmtzdGF0aW9uczogKCkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtzdGF0aW9uU2VydmljZScsICdnZXRBY3RpdmVXb3Jrc3RhdGlvbnMnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtzdGF0aW9uU2VydmljZS5nZXRBY3RpdmVXb3Jrc3RhdGlvbnMoKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8gQ1JVROaTjeS9nFxyXG4gICAgICBjcmVhdGU6IChkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3Jrc3RhdGlvblNlcnZpY2UnLCAnY3JlYXRlJywgZGF0YSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtzdGF0aW9uU2VydmljZS5jcmVhdGUoZGF0YSlcclxuICAgICAgKSxcclxuXHJcbiAgICAgIHVwZGF0ZTogKGlkOiBzdHJpbmcsIGRhdGE6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtzdGF0aW9uU2VydmljZScsICd1cGRhdGUnLCB7IGlkLCAuLi5kYXRhIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy53b3Jrc3RhdGlvblNlcnZpY2UudXBkYXRlKGlkLCBkYXRhKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZGVsZXRlOiAoaWQ6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtzdGF0aW9uU2VydmljZScsICdkZWxldGUnLCB7IGlkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy53b3Jrc3RhdGlvblNlcnZpY2UuZGVsZXRlKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgLy8g54q25oCB566h55CGXHJcbiAgICAgIGdldFN0YXR1czogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdXb3Jrc3RhdGlvblNlcnZpY2UnLCAnZ2V0V29ya3N0YXRpb25TdGF0dXMnLCB7IGlkIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy53b3Jrc3RhdGlvblNlcnZpY2UuZ2V0V29ya3N0YXRpb25TdGF0dXMoaWQpXHJcbiAgICAgICksXHJcblxyXG4gICAgICB1cGRhdGVTdGF0dXM6IChpZDogc3RyaW5nLCBzdGF0dXM6IGFueSkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtzdGF0aW9uU2VydmljZScsICd1cGRhdGVXb3Jrc3RhdGlvblN0YXR1cycsIHsgaWQsIC4uLnN0YXR1cyB9LFxyXG4gICAgICAgICgpID0+IHRoaXMud29ya3N0YXRpb25TZXJ2aWNlLnVwZGF0ZVdvcmtzdGF0aW9uU3RhdHVzKGlkLCBzdGF0dXMpXHJcbiAgICAgICksXHJcblxyXG4gICAgICAvLyDpmJ/liJfnrqHnkIZcclxuICAgICAgYWRkVG9RdWV1ZTogKHdvcmtzdGF0aW9uSWQ6IHN0cmluZywgYmF0Y2hOdW1iZXI6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ1dvcmtzdGF0aW9uU2VydmljZScsICdhZGRUb1F1ZXVlJywgeyB3b3Jrc3RhdGlvbklkLCBiYXRjaE51bWJlciB9LFxyXG4gICAgICAgICgpID0+IHRoaXMud29ya3N0YXRpb25TZXJ2aWNlLmFkZFRvUXVldWUod29ya3N0YXRpb25JZCwgYmF0Y2hOdW1iZXIpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBtaWdyYXRlQmF0Y2hOdW1iZXJGb3JtYXRzOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnV29ya3N0YXRpb25TZXJ2aWNlJywgJ21pZ3JhdGVCYXRjaE51bWJlckZvcm1hdHMnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLndvcmtzdGF0aW9uU2VydmljZS5taWdyYXRlQmF0Y2hOdW1iZXJGb3JtYXRzKClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIC8vIPCflKcg57uf5LiA55qE5bel5L2N6YeN572u5pa55rOVXHJcbiAgICAgIHJlc2V0QWxsV29ya3N0YXRpb25zVG9JZGxlOiAoKSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnV29ya3N0YXRpb25TZXJ2aWNlJywgJ3Jlc2V0QWxsV29ya3N0YXRpb25zVG9JZGxlJywge30sXHJcbiAgICAgICAgKCkgPT4gdGhpcy53b3Jrc3RhdGlvblNlcnZpY2UucmVzZXRBbGxXb3Jrc3RhdGlvbnNUb0lkbGUoKVxyXG4gICAgICApXHJcblxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gPT09PT09PT09PT09PT09PT09PT0g566h55CG5Yqf6IO9ID09PT09PT09PT09PT09PT09PT09XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPluiuv+mXruaXpeW/l1xyXG4gICAqL1xyXG4gIGdldEFjY2Vzc0xvZ3MobGltaXQ/OiBudW1iZXIpOiBBY2Nlc3NMb2dbXSB7XHJcbiAgICByZXR1cm4gbGltaXQgPyB0aGlzLmFjY2Vzc0xvZ3Muc2xpY2UoLWxpbWl0KSA6IFsuLi50aGlzLmFjY2Vzc0xvZ3NdXHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmuIXpmaTorr/pl67ml6Xlv5dcclxuICAgKi9cclxuICBjbGVhckFjY2Vzc0xvZ3MoKTogdm9pZCB7XHJcbiAgICB0aGlzLmFjY2Vzc0xvZ3MgPSBbXVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6I635Y+W57uf6K6h5L+h5oGvXHJcbiAgICovXHJcbiAgZ2V0U3RhdGlzdGljcygpIHtcclxuICAgIGNvbnN0IHRvdGFsUmVxdWVzdHMgPSB0aGlzLmFjY2Vzc0xvZ3MubGVuZ3RoXHJcbiAgICBjb25zdCBzdWNjZXNzZnVsUmVxdWVzdHMgPSB0aGlzLmFjY2Vzc0xvZ3MuZmlsdGVyKGxvZyA9PiBsb2cuc3VjY2VzcykubGVuZ3RoXHJcbiAgICBjb25zdCBmYWlsZWRSZXF1ZXN0cyA9IHRvdGFsUmVxdWVzdHMgLSBzdWNjZXNzZnVsUmVxdWVzdHNcclxuICAgIGNvbnN0IGF2ZXJhZ2VEdXJhdGlvbiA9IHRvdGFsUmVxdWVzdHMgPiAwIFxyXG4gICAgICA/IHRoaXMuYWNjZXNzTG9ncy5yZWR1Y2UoKHN1bSwgbG9nKSA9PiBzdW0gKyBsb2cuZHVyYXRpb24sIDApIC8gdG90YWxSZXF1ZXN0cyBcclxuICAgICAgOiAwXHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgdG90YWxSZXF1ZXN0cyxcclxuICAgICAgc3VjY2Vzc2Z1bFJlcXVlc3RzLFxyXG4gICAgICBmYWlsZWRSZXF1ZXN0cyxcclxuICAgICAgc3VjY2Vzc1JhdGU6IHRvdGFsUmVxdWVzdHMgPiAwID8gKHN1Y2Nlc3NmdWxSZXF1ZXN0cyAvIHRvdGFsUmVxdWVzdHMpICogMTAwIDogMCxcclxuICAgICAgYXZlcmFnZUR1cmF0aW9uOiBNYXRoLnJvdW5kKGF2ZXJhZ2VEdXJhdGlvbiksXHJcbiAgICAgIGNvbmZpZzogdGhpcy5jb25maWdcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOWIneWni+WMluaVsOaNruWPmOabtOmAmuefpeacuuWItlxyXG4gICAqL1xyXG4gIHByaXZhdGUgaW5pdGlhbGl6ZURhdGFDaGFuZ2VOb3RpZmljYXRpb24oKTogdm9pZCB7XHJcbiAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDpop3lpJbnmoTliJ3lp4vljJbpgLvovpFcclxuICAgIC8vIOaVsOaNruWPmOabtOmAmuefpeWZqOW3sue7j+WcqOWvvOWFpeaXtuiHquWKqOWIneWni+WMllxyXG4gIH1cclxuXHJcblxyXG5cclxuICAvKipcclxuICAgKiDojrflj5bmlbDmja7lj5jmm7TpgJrnn6XlmahcclxuICAgKi9cclxuICBnZXREYXRhQ2hhbmdlTm90aWZpZXIoKSB7XHJcbiAgICByZXR1cm4gZGF0YUNoYW5nZU5vdGlmaWVyXHJcbiAgfVxyXG5cclxuICAvLyDkvJjlhYjnuqflkIzmraXmnI3liqHlt7LliKDpmaRcclxuXHJcbiAgLyoqXHJcbiAgICog5oiQ5pys6K6h566X5pWw5o2u6K6/6Zeu5ZmoXHJcbiAgICovXHJcbiAgZ2V0IGNvc3RDYWxjdWxhdGlvbnMoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBnZXRBbGxDYWxjdWxhdGlvbnM6IChwYXJhbXM/OiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDb3N0Q2FsY3VsYXRpb25TZXJ2aWNlJywgJ2dldEFsbENhbGN1bGF0aW9ucycsIHBhcmFtcyxcclxuICAgICAgICAoKSA9PiB0aGlzLmNvc3RDYWxjdWxhdGlvblNlcnZpY2UuZ2V0QWxsQ2FsY3VsYXRpb25zKHBhcmFtcylcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldENhbGN1bGF0aW9uQnlJZDogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDb3N0Q2FsY3VsYXRpb25TZXJ2aWNlJywgJ2dldENhbGN1bGF0aW9uQnlJZCcsIHsgaWQgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmNvc3RDYWxjdWxhdGlvblNlcnZpY2UuZ2V0Q2FsY3VsYXRpb25CeUlkKGlkKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgY3JlYXRlQ2FsY3VsYXRpb246IChkYXRhOiBhbnkpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDb3N0Q2FsY3VsYXRpb25TZXJ2aWNlJywgJ2NyZWF0ZUNhbGN1bGF0aW9uJywgZGF0YSxcclxuICAgICAgICAoKSA9PiB0aGlzLmNvc3RDYWxjdWxhdGlvblNlcnZpY2UuY3JlYXRlQ2FsY3VsYXRpb24oZGF0YSlcclxuICAgICAgKSxcclxuXHJcbiAgICAgIHVwZGF0ZUNhbGN1bGF0aW9uOiAoaWQ6IHN0cmluZywgZGF0YTogYW55KSA9PiB0aGlzLmV4ZWN1dGVXaXRoTG9nZ2luZyhcclxuICAgICAgICAnQ29zdENhbGN1bGF0aW9uU2VydmljZScsICd1cGRhdGVDYWxjdWxhdGlvbicsIHsgaWQsIC4uLmRhdGEgfSxcclxuICAgICAgICAoKSA9PiB0aGlzLmNvc3RDYWxjdWxhdGlvblNlcnZpY2UudXBkYXRlQ2FsY3VsYXRpb24oaWQsIGRhdGEpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBkZWxldGVDYWxjdWxhdGlvbjogKGlkOiBzdHJpbmcpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDb3N0Q2FsY3VsYXRpb25TZXJ2aWNlJywgJ2RlbGV0ZUNhbGN1bGF0aW9uJywgeyBpZCB9LFxyXG4gICAgICAgICgpID0+IHRoaXMuY29zdENhbGN1bGF0aW9uU2VydmljZS5kZWxldGVDYWxjdWxhdGlvbihpZClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldENhbGN1bGF0aW9uc0J5UHJvZHVjdDogKHByb2R1Y3RNb2RlbENvZGU6IHN0cmluZykgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0Nvc3RDYWxjdWxhdGlvblNlcnZpY2UnLCAnZ2V0Q2FsY3VsYXRpb25zQnlQcm9kdWN0JywgeyBwcm9kdWN0TW9kZWxDb2RlIH0sXHJcbiAgICAgICAgKCkgPT4gdGhpcy5jb3N0Q2FsY3VsYXRpb25TZXJ2aWNlLmdldENhbGN1bGF0aW9uc0J5UHJvZHVjdChwcm9kdWN0TW9kZWxDb2RlKVxyXG4gICAgICApLFxyXG5cclxuICAgICAgZ2V0UGVuZGluZ1JlY29uY2lsaWF0aW9uczogKCkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0Nvc3RDYWxjdWxhdGlvblNlcnZpY2UnLCAnZ2V0UGVuZGluZ1JlY29uY2lsaWF0aW9ucycsIHt9LFxyXG4gICAgICAgICgpID0+IHRoaXMuY29zdENhbGN1bGF0aW9uU2VydmljZS5nZXRQZW5kaW5nUmVjb25jaWxpYXRpb25zKClcclxuICAgICAgKSxcclxuXHJcbiAgICAgIGdldFN0YXRpc3RpY3M6ICgpID0+IHRoaXMuZXhlY3V0ZVdpdGhMb2dnaW5nKFxyXG4gICAgICAgICdDb3N0Q2FsY3VsYXRpb25TZXJ2aWNlJywgJ2dldFN0YXRpc3RpY3MnLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLmNvc3RDYWxjdWxhdGlvblNlcnZpY2UuZ2V0U3RhdGlzdGljcygpXHJcbiAgICAgICksXHJcblxyXG4gICAgICBnZXRDb3N0U3VtbWFyeTogKCkgPT4gdGhpcy5leGVjdXRlV2l0aExvZ2dpbmcoXHJcbiAgICAgICAgJ0Nvc3RDYWxjdWxhdGlvblNlcnZpY2UnLCAnZ2V0Q29zdFN1bW1hcnknLCB7fSxcclxuICAgICAgICAoKSA9PiB0aGlzLmNvc3RDYWxjdWxhdGlvblNlcnZpY2UuZ2V0Q29zdFN1bW1hcnkoKVxyXG4gICAgICApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyDml6fnvJPlrZjnrqHnkIbmlrnms5Xlt7Lnp7vpmaTvvIzkvb/nlKjlhoXnva7nvJPlrZjns7vnu59cclxuXHJcbiAgLy8g56e76Zmk6YeN5aSN55qE5pa55rOV5a6a5LmJ77yM5L2/55So5LiL6Z2i55qE57uf5LiA57yT5a2Y566h55CG5pa55rOVXHJcblxyXG4gIC8vIGludmFsaWRhdGVDYWNoZSh0YWdzOiBzdHJpbmdbXSk6IHZvaWQge1xyXG4gIC8vICAgdGhpcy5jYWNoZU1hbmFnZXIuZGVsZXRlQnlUYWdzKHRhZ3MpXHJcbiAgLy8gfVxyXG5cclxuICAvKipcclxuICAgKiDmm7TmlrDphY3nva5cclxuICAgKi9cclxuICB1cGRhdGVDb25maWcobmV3Q29uZmlnOiBQYXJ0aWFsPERhdGFBY2Nlc3NDb25maWc+KTogdm9pZCB7XHJcbiAgICB0aGlzLmNvbmZpZyA9IHsgLi4udGhpcy5jb25maWcsIC4uLm5ld0NvbmZpZyB9XHJcblxyXG4gICAgLy8g5aaC5p6c56aB55So57yT5a2Y77yM5riF56m6546w5pyJ57yT5a2YXHJcbiAgICBpZiAobmV3Q29uZmlnLmVuYWJsZUNhY2hpbmcgPT09IGZhbHNlKSB7XHJcbiAgICAgIHRoaXMuY2FjaGUuY2xlYXIoKVxyXG4gICAgICB0aGlzLnJlcXVlc3RDYWNoZS5jbGVhcigpXHJcbiAgICAgIGlmICh0aGlzLmNvbmZpZy5lbmFibGVMb2dnaW5nKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1tEYXRhQWNjZXNzTWFuYWdlcl0g57yT5a2Y5bey56aB55So5bm25riF56m6JylcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmICh0aGlzLmNvbmZpZy5lbmFibGVMb2dnaW5nKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdbRGF0YUFjY2Vzc01hbmFnZXJdIOmFjee9ruW3suabtOaWsDonLCB0aGlzLmNvbmZpZylcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vID09PT09PT09PT09PT09PT09PT09IOe8k+WtmOeuoeeQhuaOpeWPoyA9PT09PT09PT09PT09PT09PT09PVxyXG5cclxuICAvKipcclxuICAgKiDmuIXpmaTnibnlrprmnI3liqHnmoTnvJPlrZhcclxuICAgKiDnrKblkIjmlbDmja7osIPnlKjop4TojIPnmoTnvJPlrZjnrqHnkIbmjqXlj6NcclxuICAgKi9cclxuICBjbGVhclNlcnZpY2VDYWNoZShzZXJ2aWNlTmFtZTogc3RyaW5nLCBvcGVyYXRpb24/OiBzdHJpbmcpOiBudW1iZXIge1xyXG4gICAgaWYgKCF0aGlzLmNvbmZpZy5lbmFibGVDYWNoaW5nKSB7XHJcbiAgICAgIGlmICh0aGlzLmNvbmZpZy5lbmFibGVMb2dnaW5nKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1tEYXRhQWNjZXNzTWFuYWdlcl0g57yT5a2Y5pyq5ZCv55So77yM5peg6ZyA5riF55CGJylcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gMFxyXG4gICAgfVxyXG5cclxuICAgIC8vIOKchSDmnrbmnoTlkIjop4TvvJrkvb/nlKjlhoXnva7nvJPlrZjns7vnu5/muIXnkIbmnI3liqHnvJPlrZhcclxuICAgIGxldCBkZWxldGVkQ291bnQgPSAwXHJcbiAgICBjb25zdCBwYXR0ZXJuID0gYCR7c2VydmljZU5hbWV9OmBcclxuICAgIFxyXG4gICAgZm9yIChjb25zdCBba2V5XSBvZiBBcnJheS5mcm9tKHRoaXMuY2FjaGUpKSB7XHJcbiAgICAgIGlmIChrZXkuc3RhcnRzV2l0aChwYXR0ZXJuKSkge1xyXG4gICAgICAgIHRoaXMuY2FjaGUuZGVsZXRlKGtleSlcclxuICAgICAgICBkZWxldGVkQ291bnQrK1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHRoaXMuY29uZmlnLmVuYWJsZUxvZ2dpbmcpIHtcclxuICAgICAgY29uc29sZS5sb2coYFtEYXRhQWNjZXNzTWFuYWdlcl0g5riF6Zmk5pyN5Yqh57yT5a2YOiAke3NlcnZpY2VOYW1lfSR7b3BlcmF0aW9uID8gYCAoJHtvcGVyYXRpb259KWAgOiAnJ33vvIzliKDpmaTkuoYgJHtkZWxldGVkQ291bnR9IOS4que8k+WtmOmhuWApXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGRlbGV0ZWRDb3VudFxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5riF6Zmk54m55a6a5pWw5o2u57G75Z6L55qE57yT5a2YXHJcbiAgICog56ym5ZCI5pWw5o2u6LCD55So6KeE6IyD55qE57yT5a2Y566h55CG5o6l5Y+jXHJcbiAgICovXHJcbiAgY2xlYXJEYXRhVHlwZUNhY2hlKGRhdGFUeXBlOiBzdHJpbmcsIGFmZmVjdGVkSWRzPzogc3RyaW5nW10pOiBudW1iZXIge1xyXG4gICAgaWYgKCF0aGlzLmNvbmZpZy5lbmFibGVDYWNoaW5nKSB7XHJcbiAgICAgIGlmICh0aGlzLmNvbmZpZy5lbmFibGVMb2dnaW5nKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1tEYXRhQWNjZXNzTWFuYWdlcl0g57yT5a2Y5pyq5ZCv55So77yM5peg6ZyA5riF55CGJylcclxuICAgICAgfVxyXG4gICAgICByZXR1cm4gMFxyXG4gICAgfVxyXG5cclxuICAgIGxldCBkZWxldGVkQ291bnQgPSAwXHJcbiAgICBjb25zdCBzZXJ2aWNlTWFwOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xyXG4gICAgICAncHJvZHVjdHMnOiAnUHJvZHVjdFNlcnZpY2UnLFxyXG4gICAgICAnb3JkZXJzJzogJ09yZGVyU2VydmljZScsXHJcbiAgICAgICd3b3Jrc3RhdGlvbnMnOiAnV29ya3N0YXRpb25TZXJ2aWNlJyxcclxuICAgICAgJ3N0YXRpc3RpY3MnOiAnU3RhdGlzdGljc1NlcnZpY2UnXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgc2VydmljZU5hbWUgPSBzZXJ2aWNlTWFwW2RhdGFUeXBlXSB8fCBkYXRhVHlwZVxyXG5cclxuICAgIGZvciAoY29uc3QgW2tleV0gb2YgQXJyYXkuZnJvbSh0aGlzLmNhY2hlKSkge1xyXG4gICAgICBpZiAoa2V5LnN0YXJ0c1dpdGgoYCR7c2VydmljZU5hbWV9OmApKSB7XHJcbiAgICAgICAgLy8g5aaC5p6c5oyH5a6a5LqG54m55a6aSUTvvIzlj6rliKDpmaTnm7jlhbPnmoTnvJPlrZhcclxuICAgICAgICBpZiAoYWZmZWN0ZWRJZHMgJiYgYWZmZWN0ZWRJZHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgY29uc3QgaGFzTWF0Y2hpbmdJZCA9IGFmZmVjdGVkSWRzLnNvbWUoaWQgPT4ga2V5LmluY2x1ZGVzKGlkKSlcclxuICAgICAgICAgIGlmIChoYXNNYXRjaGluZ0lkKSB7XHJcbiAgICAgICAgICAgIHRoaXMuY2FjaGUuZGVsZXRlKGtleSlcclxuICAgICAgICAgICAgZGVsZXRlZENvdW50KytcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgLy8g5Yig6Zmk5omA5pyJ6K+l5pWw5o2u57G75Z6L55qE57yT5a2YXHJcbiAgICAgICAgICB0aGlzLmNhY2hlLmRlbGV0ZShrZXkpXHJcbiAgICAgICAgICBkZWxldGVkQ291bnQrK1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmICh0aGlzLmNvbmZpZy5lbmFibGVMb2dnaW5nKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBbRGF0YUFjY2Vzc01hbmFnZXJdIOa4hemZpOaVsOaNruexu+Wei+e8k+WtmDogJHtkYXRhVHlwZX3vvIzlvbHlk41JRDogJHthZmZlY3RlZElkcz8uam9pbignLCAnKSB8fCAn5YWo6YOoJ33vvIzliKDpmaTkuoYgJHtkZWxldGVkQ291bnR9IOS4que8k+WtmOmhuWApXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGRlbGV0ZWRDb3VudFxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5riF6Zmk5omA5pyJ57yT5a2YXHJcbiAgICog56ym5ZCI5pWw5o2u6LCD55So6KeE6IyD55qE57yT5a2Y566h55CG5o6l5Y+jXHJcbiAgICovXHJcbiAgY2xlYXJBbGxDYWNoZSgpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5jb25maWcuZW5hYmxlQ2FjaGluZykge1xyXG4gICAgICBpZiAodGhpcy5jb25maWcuZW5hYmxlTG9nZ2luZykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdbRGF0YUFjY2Vzc01hbmFnZXJdIOe8k+WtmOacquWQr+eUqO+8jOaXoOmcgOa4heeQhicpXHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuXHJcbiAgICB9XHJcblxyXG4gICAgLy8g4pyFIOaetuaehOWQiOinhO+8muS9v+eUqOWGhee9rue8k+WtmOezu+e7n+a4heeQhuaJgOaciee8k+WtmFxyXG4gICAgY29uc3QgYmVmb3JlU2l6ZSA9IHRoaXMuY2FjaGUuc2l6ZVxyXG4gICAgdGhpcy5jYWNoZS5jbGVhcigpXHJcbiAgICB0aGlzLnJlcXVlc3RDYWNoZS5jbGVhcigpXHJcblxyXG4gICAgaWYgKHRoaXMuY29uZmlnLmVuYWJsZUxvZ2dpbmcpIHtcclxuICAgICAgY29uc29sZS5sb2coYFtEYXRhQWNjZXNzTWFuYWdlcl0g5riF6Zmk5omA5pyJ57yT5a2Y77yM5Yig6Zmk5LqGICR7YmVmb3JlU2l6ZX0g5Liq57yT5a2Y6aG5YClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPlue8k+WtmOe7n+iuoeS/oeaBryAtIOS8mOWMlueJiFxyXG4gICAqIOespuWQiOaVsOaNruiwg+eUqOinhOiMg+eahOebkeaOp+aOpeWPo1xyXG4gICAqL1xyXG4gIGdldENhY2hlU3RhdGlzdGljcygpOiBDYWNoZVN0YXRpc3RpY3Mge1xyXG4gICAgLy8g4pyFIOaetuaehOWQiOinhO+8muS9v+eUqOWGhee9rue8k+WtmOezu+e7n+iOt+WPlue7n+iuoeS/oeaBr1xyXG4gICAgY29uc3QgZW50cmllcyA9IEFycmF5LmZyb20odGhpcy5jYWNoZS52YWx1ZXMoKSlcclxuICAgIGNvbnN0IHRvdGFsU2l6ZSA9IGVudHJpZXMucmVkdWNlKChzdW0sIGVudHJ5KSA9PiBzdW0gKyBlbnRyeS5lc3RpbWF0ZWRTaXplLCAwKVxyXG4gICAgY29uc3QgYXZlcmFnZUVudHJ5U2l6ZSA9IGVudHJpZXMubGVuZ3RoID4gMCA/IHRvdGFsU2l6ZSAvIGVudHJpZXMubGVuZ3RoIDogMFxyXG4gICAgXHJcbiAgICAvLyDorqHnrpfkvJjlhYjnuqfliIbluINcclxuICAgIGNvbnN0IHByaW9yaXR5RGlzdHJpYnV0aW9uID0geyBjcml0aWNhbDogMCwgaGlnaDogMCwgbWVkaXVtOiAwLCBsb3c6IDAgfVxyXG4gICAgZW50cmllcy5mb3JFYWNoKGVudHJ5ID0+IHtcclxuICAgICAgcHJpb3JpdHlEaXN0cmlidXRpb25bZW50cnkucHJpb3JpdHldKytcclxuICAgIH0pXHJcbiAgICBcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGVuYWJsZWQ6IHRoaXMuY29uZmlnLmVuYWJsZUNhY2hpbmcsXHJcbiAgICAgIHNpemU6IHRoaXMuY2FjaGUuc2l6ZSxcclxuICAgICAgaGl0czogdGhpcy5jYWNoZVN0YXRzLmhpdHMsXHJcbiAgICAgIG1pc3NlczogdGhpcy5jYWNoZVN0YXRzLm1pc3NlcyxcclxuICAgICAgaGl0UmF0ZTogdGhpcy5jYWNoZVN0YXRzLmhpdHMgLyAodGhpcy5jYWNoZVN0YXRzLmhpdHMgKyB0aGlzLmNhY2hlU3RhdHMubWlzc2VzKSB8fCAwLFxyXG4gICAgICB0b3RhbFJlcXVlc3RzOiB0aGlzLmNhY2hlU3RhdHMudG90YWxSZXF1ZXN0cyxcclxuICAgICAgLy8g4pyFIOS8mOWMlu+8muaWsOWinue7n+iuoeS/oeaBr1xyXG4gICAgICBldmljdGlvbnM6IHRoaXMuY2FjaGVTdGF0cy5ldmljdGlvbnMsXHJcbiAgICAgIHByZXdhcm1IaXRzOiB0aGlzLmNhY2hlU3RhdHMucHJld2FybUhpdHMsXHJcbiAgICAgIG1lbW9yeVByZXNzdXJlOiB0aGlzLm1lbW9yeVByZXNzdXJlLFxyXG4gICAgICBhdmVyYWdlRW50cnlTaXplLFxyXG4gICAgICBwcmlvcml0eURpc3RyaWJ1dGlvblxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5pu05paw57yT5a2Y6YWN572uXHJcbiAgICovXHJcbiAgdXBkYXRlQ2FjaGVDb25maWcobmV3Q29uZmlnOiBQYXJ0aWFsPENhY2hlQ29uZmlnPik6IHZvaWQge1xyXG4gICAgdGhpcy5jYWNoZUNvbmZpZyA9IHsgLi4udGhpcy5jYWNoZUNvbmZpZywgLi4ubmV3Q29uZmlnIH1cclxuICAgIGNvbnNvbGUubG9nKCdbRGF0YUFjY2Vzc01hbmFnZXJdIOe8k+WtmOmFjee9ruW3suabtOaWsCcpXHJcbiAgfVxyXG5cclxuICAvLyDinIUg5p625p6E5ZCI6KeE77ya56e76Zmk6L+d6KeE55qEQ2FjaGVTdHJhdGVneU1hbmFnZXLmlrnms5XvvIzkvb/nlKjlhoXnva7nvJPlrZjns7vnu5/mm7/ku6NcclxuXHJcbiAgLyoqXHJcbiAgICog5om56YeP5pON5L2c5pa55rOVXHJcbiAgICovXHJcbiAgZ2V0IGJhdGNoKCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgLy8g5om56YeP5Yib5bu655Sf5Lqn6K6i5Y2VXHJcbiAgICAgIGNyZWF0ZVByb2R1Y3Rpb25PcmRlcnM6IChvcmRlcnM6IGFueVtdKSA9PlxyXG4gICAgICAgIHBlcmZvcm1hbmNlT3B0aW1pemVyLmJhdGNoQ3JlYXRlUHJvZHVjdGlvbk9yZGVycyhvcmRlcnMpLFxyXG5cclxuICAgICAgLy8g5om56YeP5pu05paw55Sf5Lqn6K6i5Y2VXHJcbiAgICAgIHVwZGF0ZVByb2R1Y3Rpb25PcmRlcnM6ICh1cGRhdGVzOiBBcnJheTx7aWQ6IHN0cmluZywgZGF0YTogYW55fT4pID0+XHJcbiAgICAgICAgcGVyZm9ybWFuY2VPcHRpbWl6ZXIuYmF0Y2hVcGRhdGVQcm9kdWN0aW9uT3JkZXJzKHVwZGF0ZXMpLFxyXG5cclxuICAgICAgLy8g5om56YeP5Yib5bu655Sf5Lqn5bel5Y2VIC0g5L2/55So5bm25Y+R5o6n5Yi25LyY5YyWXHJcbiAgICAgIGNyZWF0ZVByb2R1Y3Rpb25Xb3JrT3JkZXJzOiBhc3luYyAod29ya09yZGVyczogYW55W10pID0+IHtcclxuICAgICAgICBjb25zdCBzdGFydFRpbWUgPSBwZXJmb3JtYW5jZS5ub3coKVxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAvLyDliJvlu7rku7vliqHmlbDnu4RcclxuICAgICAgICAgIGNvbnN0IHRhc2tzID0gd29ya09yZGVycy5tYXAod29ya09yZGVyID0+IGFzeW5jICgpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgdGhpcy5wcm9kdWN0aW9uV29ya09yZGVycy5jcmVhdGUod29ya09yZGVyKVxyXG4gICAgICAgICAgICBpZiAocmVzdWx0LnN0YXR1cyAhPT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3VsdC5tZXNzYWdlIHx8ICfliJvlu7rlt6XljZXlpLHotKUnKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiByZXN1bHQuZGF0YVxyXG4gICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICBjb25zdCB0YXNrTmFtZXMgPSB3b3JrT3JkZXJzLm1hcCgoXywgaW5kZXgpID0+IGDmibnph4/liJvlu7rlt6XljZUtJHtpbmRleCArIDF9YClcclxuICAgICAgICAgIGNvbnN0IGJhdGNoUmVzdWx0ID0gYXdhaXQgYmF0Y2hPcGVyYXRpb25Db250cm9sbGVyLmV4ZWN1dGVCYXRjaCh0YXNrcywgdGFza05hbWVzKVxyXG5cclxuICAgICAgICAgIGNvbnN0IGR1cmF0aW9uID0gcGVyZm9ybWFuY2Uubm93KCkgLSBzdGFydFRpbWVcclxuICAgICAgICAgIGNvbnN0IHsgc3VjY2Vzc2Z1bCwgZmFpbGVkIH0gPSBiYXRjaFJlc3VsdFxyXG4gICAgICAgICAgY29uc3Qgc3VjY2Vzc2Z1bERhdGEgPSBzdWNjZXNzZnVsLm1hcChyZXN1bHQgPT4gcmVzdWx0LmRhdGEhKS5maWx0ZXIoQm9vbGVhbilcclxuXHJcbiAgICAgICAgICBkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yLnJlY29yZFJlcXVlc3QoJ2JhdGNoLmNyZWF0ZVByb2R1Y3Rpb25Xb3JrT3JkZXJzJywgZHVyYXRpb24sIGZhaWxlZC5sZW5ndGggPT09IDApXHJcblxyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGRhdGE6IHN1Y2Nlc3NmdWxEYXRhLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBg5oiQ5Yqf5om56YeP5Yib5bu6JHtzdWNjZXNzZnVsRGF0YS5sZW5ndGh95Liq55Sf5Lqn5bel5Y2VJHtmYWlsZWQubGVuZ3RoID4gMCA/IGDvvIwke2ZhaWxlZC5sZW5ndGh95Liq5aSx6LSlYCA6ICcnfWAsXHJcbiAgICAgICAgICAgIGJhdGNoUmVzdWx0OiB7XHJcbiAgICAgICAgICAgICAgc3VjY2Vzc2Z1bDogc3VjY2Vzc2Z1bC5sZW5ndGgsXHJcbiAgICAgICAgICAgICAgZmFpbGVkOiBmYWlsZWQubGVuZ3RoLFxyXG4gICAgICAgICAgICAgIHN1Y2Nlc3NSYXRlOiBiYXRjaFJlc3VsdC5zdWNjZXNzUmF0ZSxcclxuICAgICAgICAgICAgICB0b3RhbER1cmF0aW9uOiBkdXJhdGlvblxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnN0IGR1cmF0aW9uID0gcGVyZm9ybWFuY2Uubm93KCkgLSBzdGFydFRpbWVcclxuICAgICAgICAgIGRhdGFBY2Nlc3NQZXJmb3JtYW5jZU1vbml0b3IucmVjb3JkUmVxdWVzdCgnYmF0Y2guY3JlYXRlUHJvZHVjdGlvbldvcmtPcmRlcnMnLCBkdXJhdGlvbiwgZmFsc2UpXHJcbiAgICAgICAgICB0aHJvdyBlcnJvclxyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuXHJcbiAgICAgIC8vIOaJuemHj+abtOaWsOW3peS9jeeKtuaAgVxyXG4gICAgICB1cGRhdGVXb3Jrc3RhdGlvblN0YXR1c2VzOiBhc3luYyAodXBkYXRlczogQXJyYXk8e2lkOiBzdHJpbmcsIHN0YXR1czogYW55fT4pID0+IHtcclxuICAgICAgICBjb25zdCBzdGFydFRpbWUgPSBwZXJmb3JtYW5jZS5ub3coKVxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgUHJvbWlzZS5hbGwoXHJcbiAgICAgICAgICAgIHVwZGF0ZXMubWFwKCh7IGlkLCBzdGF0dXMgfSkgPT4gdGhpcy53b3Jrc3RhdGlvbnMudXBkYXRlU3RhdHVzKGlkLCBzdGF0dXMpKVxyXG4gICAgICAgICAgKVxyXG4gICAgICAgICAgY29uc3QgZHVyYXRpb24gPSBwZXJmb3JtYW5jZS5ub3coKSAtIHN0YXJ0VGltZVxyXG4gICAgICAgICAgZGF0YUFjY2Vzc1BlcmZvcm1hbmNlTW9uaXRvci5yZWNvcmRSZXF1ZXN0KCdiYXRjaC51cGRhdGVXb3Jrc3RhdGlvblN0YXR1c2VzJywgZHVyYXRpb24sIHRydWUpXHJcblxyXG4gICAgICAgICAgLy8g5L2/55So5YaF572u57yT5a2Y57O757uf5riF55CG55u45YWz57yT5a2YXHJcbiAgICAgICAgICB0aGlzLmNsZWFyRGF0YVR5cGVDYWNoZSgnd29ya3N0YXRpb25zJywgdXBkYXRlcy5tYXAodSA9PiB1LmlkKSlcclxuXHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgZGF0YTogcmVzdWx0cy5maWx0ZXIociA9PiByLnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKS5tYXAociA9PiByLmRhdGEpLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBg5oiQ5Yqf5om56YeP5pu05pawJHtyZXN1bHRzLmxlbmd0aH3kuKrlt6XkvY3nirbmgIFgXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnN0IGR1cmF0aW9uID0gcGVyZm9ybWFuY2Uubm93KCkgLSBzdGFydFRpbWVcclxuICAgICAgICAgIGRhdGFBY2Nlc3NQZXJmb3JtYW5jZU1vbml0b3IucmVjb3JkUmVxdWVzdCgnYmF0Y2gudXBkYXRlV29ya3N0YXRpb25TdGF0dXNlcycsIGR1cmF0aW9uLCBmYWxzZSlcclxuICAgICAgICAgIHRocm93IGVycm9yXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDmgKfog73nm5Hmjqfmlrnms5VcclxuICAgKi9cclxuICBnZXQgcGVyZm9ybWFuY2UoKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAvLyDojrflj5bmgKfog73mjIfmoIdcclxuICAgICAgZ2V0TWV0cmljczogKCkgPT4gZGF0YUFjY2Vzc1BlcmZvcm1hbmNlTW9uaXRvci5nZXRNZXRyaWNzKCksXHJcblxyXG4gICAgICAvLyDojrflj5bmgKfog73orablkYpcclxuICAgICAgZ2V0QWxlcnRzOiAoKSA9PiBkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yLmdldEFsZXJ0cygpLFxyXG5cclxuICAgICAgLy8g6I635Y+W5LyY5YyW5bu66K6uXHJcbiAgICAgIGdldFN1Z2dlc3Rpb25zOiAoKSA9PiBkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yLmdldE9wdGltaXphdGlvblN1Z2dlc3Rpb25zKCksXHJcblxyXG4gICAgICAvLyDnlJ/miJDmgKfog73miqXlkYpcclxuICAgICAgZ2VuZXJhdGVSZXBvcnQ6ICgpID0+IGRhdGFBY2Nlc3NQZXJmb3JtYW5jZU1vbml0b3IuZ2VuZXJhdGVQZXJmb3JtYW5jZVJlcG9ydCgpLFxyXG5cclxuICAgICAgLy8g6YeN572u5oCn6IO95oyH5qCHXHJcbiAgICAgIHJlc2V0OiAoKSA9PiBkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yLnJlc2V0KCksXHJcblxyXG4gICAgICAvLyDkvJjljJbnvJPlrZjnrZbnlaVcclxuICAgICAgb3B0aW1pemVDYWNoZTogKCkgPT4ge1xyXG4gICAgICAgIC8vIOS9v+eUqOWGhee9rue8k+WtmOezu+e7n+i/m+ihjOS8mOWMllxyXG4gICAgICAgIHRoaXMuZXZpY3RPbGRlc3RFbnRyaWVzKClcclxuICAgICAgICBjb25zb2xlLmxvZygnW0RhdGFBY2Nlc3NNYW5hZ2VyXSDnvJPlrZjkvJjljJblrozmiJAnKVxyXG4gICAgICB9LFxyXG5cclxuICAgICAgLy8g6I635Y+W57yT5a2Y5oCn6IO95oyH5qCHXHJcbiAgICAgIGdldENhY2hlTWV0cmljczogKCkgPT4ge1xyXG4gICAgICAgIHJldHVybiB0aGlzLmdldENhY2hlU3RhdGlzdGljcygpXHJcbiAgICAgIH0sXHJcblxyXG4gICAgICAvLyDojrflj5bmgKfog73kvJjljJblmajmjIfmoIdcclxuICAgICAgZ2V0T3B0aW1pemVyTWV0cmljczogKCkgPT4gcGVyZm9ybWFuY2VPcHRpbWl6ZXIuZ2V0UGVyZm9ybWFuY2VNZXRyaWNzKClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIOaXp+eahOe8k+WtmOeuoeeQhuaWueazleW3suenu+mZpO+8jOS9v+eUqOWGhee9rue8k+WtmOezu+e7n+eahOe7n+S4gOaOpeWPo1xyXG5cclxuICAvKipcclxuICAgKiDmgKfog73nm5HmjqfnrqHnkIbmlrnms5VcclxuICAgKi9cclxuICBnZXRQZXJmb3JtYW5jZU1ldHJpY3MoKTogUGVyZm9ybWFuY2VNZXRyaWNzIHtcclxuICAgIC8vIOabtOaWsOaWueazlee7n+iuoeWIsOaAp+iDveaMh+agh+S4rVxyXG4gICAgdGhpcy5wZXJmb3JtYW5jZU1ldHJpY3MubWV0aG9kU3RhdHMgPSB0aGlzLm1ldGhvZFN0YXRzXHJcbiAgICByZXR1cm4geyAuLi50aGlzLnBlcmZvcm1hbmNlTWV0cmljcyB9XHJcbiAgfVxyXG5cclxuICBnZXRSZWFsVGltZU1ldHJpY3MoKTogUmVhbFRpbWVNZXRyaWNzIHtcclxuICAgIGNvbnN0IGJhdGNoQ29udHJvbGxlclN0YXR1cyA9IGJhdGNoT3BlcmF0aW9uQ29udHJvbGxlci5nZXRTdGF0dXMoKVxyXG5cclxuICAgIC8vIOWIhuaekOezu+e7n+WBpeW6t+eKtuWGtVxyXG4gICAgY29uc3Qgc3lzdGVtSGVhbHRoID0gdGhpcy5hbmFseXplU3lzdGVtSGVhbHRoKClcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBjdXJyZW50Q29uY3VycmVuY3k6IGJhdGNoQ29udHJvbGxlclN0YXR1cy5ydW5uaW5nLFxyXG4gICAgICBxdWV1ZUxlbmd0aDogYmF0Y2hDb250cm9sbGVyU3RhdHVzLnF1ZXVlZCxcclxuICAgICAgcmVjZW50Q2FsbHM6IFsuLi50aGlzLnJlY2VudENhbGxzLnNsaWNlKC01MCldLCAvLyDmnIDov5E1MOasoeiwg+eUqFxyXG4gICAgICBzeXN0ZW1IZWFsdGhcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHByaXZhdGUgYW5hbHl6ZVN5c3RlbUhlYWx0aCgpOiBSZWFsVGltZU1ldHJpY3NbJ3N5c3RlbUhlYWx0aCddIHtcclxuICAgIGNvbnN0IGlzc3Vlczogc3RyaW5nW10gPSBbXVxyXG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXSA9IFtdXHJcblxyXG4gICAgLy8g5qOA5p+l6ZSZ6K+v546HXHJcbiAgICBjb25zdCByZWNlbnRFcnJvclJhdGUgPSB0aGlzLnJlY2VudENhbGxzLmxlbmd0aCA+IDBcclxuICAgICAgPyB0aGlzLnJlY2VudENhbGxzLmZpbHRlcihjYWxsID0+IGNhbGwuc3RhdHVzID09PSAnZXJyb3InKS5sZW5ndGggLyB0aGlzLnJlY2VudENhbGxzLmxlbmd0aFxyXG4gICAgICA6IDBcclxuXHJcbiAgICBpZiAocmVjZW50RXJyb3JSYXRlID4gMC4xKSB7IC8vIOmUmeivr+eOh+i2hei/hzEwJVxyXG4gICAgICBpc3N1ZXMucHVzaChg6ZSZ6K+v546H6L+H6auYOiAkeyhyZWNlbnRFcnJvclJhdGUgKiAxMDApLnRvRml4ZWQoMil9JWApXHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCfmo4Dmn6XnvZHnu5zov57mjqXlkozmnI3liqHnirbmgIEnKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOajgOafpeWTjeW6lOaXtumXtFxyXG4gICAgaWYgKHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmF2ZXJhZ2VSZXNwb25zZVRpbWUgPiAyMDAwKSB7IC8vIOW5s+Wdh+WTjeW6lOaXtumXtOi2hei/hzLnp5JcclxuICAgICAgaXNzdWVzLnB1c2goYOW5s+Wdh+WTjeW6lOaXtumXtOi/h+mVvzogJHt0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5hdmVyYWdlUmVzcG9uc2VUaW1lLnRvRml4ZWQoMCl9bXNgKVxyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCgn6ICD6JmR5LyY5YyW5p+l6K+i5oiW5aKe5Yqg57yT5a2YJylcclxuICAgIH1cclxuXHJcbiAgICAvLyDmo4Dmn6XnvJPlrZjlkb3kuK3njodcclxuICAgIGlmICh0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5jYWNoZUhpdFJhdGUgPCAwLjMpIHsgLy8g57yT5a2Y5ZG95Lit546H5L2O5LqOMzAlXHJcbiAgICAgIGlzc3Vlcy5wdXNoKGDnvJPlrZjlkb3kuK3njofovoPkvY46ICR7KHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLmNhY2hlSGl0UmF0ZSAqIDEwMCkudG9GaXhlZCgyKX0lYClcclxuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goJ+ajgOafpee8k+WtmOetlueVpeWSjFRUTOiuvue9ricpXHJcbiAgICB9XHJcblxyXG4gICAgLy8g5qOA5p+l5bm25Y+R6Zif5YiXXHJcbiAgICBjb25zdCBiYXRjaFN0YXR1cyA9IGJhdGNoT3BlcmF0aW9uQ29udHJvbGxlci5nZXRTdGF0dXMoKVxyXG4gICAgaWYgKGJhdGNoU3RhdHVzLnF1ZXVlZCA+IDEwKSB7XHJcbiAgICAgIGlzc3Vlcy5wdXNoKGDmibnph4/mk43kvZzpmJ/liJfnp6/ljos6ICR7YmF0Y2hTdGF0dXMucXVldWVkfeS4quS7u+WKoWApXHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKCfogIPomZHlop7liqDlubblj5HmlbDmiJbkvJjljJbku7vliqHlpITnkIYnKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIOehruWumuezu+e7n+eKtuaAgVxyXG4gICAgbGV0IHN0YXR1czogJ2hlYWx0aHknIHwgJ3dhcm5pbmcnIHwgJ2NyaXRpY2FsJyA9ICdoZWFsdGh5J1xyXG4gICAgaWYgKGlzc3Vlcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIHN0YXR1cyA9IHJlY2VudEVycm9yUmF0ZSA+IDAuMiB8fCB0aGlzLnBlcmZvcm1hbmNlTWV0cmljcy5hdmVyYWdlUmVzcG9uc2VUaW1lID4gNTAwMCA/ICdjcml0aWNhbCcgOiAnd2FybmluZydcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4geyBzdGF0dXMsIGlzc3VlcywgcmVjb21tZW5kYXRpb25zIH1cclxuICB9XHJcblxyXG4gIHJlc2V0UGVyZm9ybWFuY2VNZXRyaWNzKCk6IHZvaWQge1xyXG4gICAgdGhpcy5pbml0aWFsaXplUGVyZm9ybWFuY2VNb25pdG9yaW5nKClcclxuICAgIGNvbnNvbGUubG9nKCdbRGF0YUFjY2Vzc01hbmFnZXJdIOaAp+iDveaMh+agh+W3sumHjee9ricpXHJcbiAgfVxyXG5cclxuICBnZXRTbG93UXVlcmllcyhsaW1pdDogbnVtYmVyID0gMjApOiBQZXJmb3JtYW5jZU1ldHJpY3NbJ3Nsb3dRdWVyaWVzJ10ge1xyXG4gICAgcmV0dXJuIHRoaXMucGVyZm9ybWFuY2VNZXRyaWNzLnNsb3dRdWVyaWVzXHJcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBiLmR1cmF0aW9uIC0gYS5kdXJhdGlvbilcclxuICAgICAgLnNsaWNlKDAsIGxpbWl0KVxyXG4gIH1cclxuXHJcbiAgZ2V0TWV0aG9kU3RhdGlzdGljcygpOiBBcnJheTx7XHJcbiAgICBtZXRob2Q6IHN0cmluZ1xyXG4gICAgY2FsbHM6IG51bWJlclxyXG4gICAgYXZlcmFnZVRpbWU6IG51bWJlclxyXG4gICAgc3VjY2Vzc1JhdGU6IG51bWJlclxyXG4gICAgbGFzdENhbGw6IHN0cmluZ1xyXG4gIH0+IHtcclxuICAgIHJldHVybiBBcnJheS5mcm9tKHRoaXMubWV0aG9kU3RhdHMuZW50cmllcygpKVxyXG4gICAgICAubWFwKChbbWV0aG9kLCBzdGF0c10pID0+ICh7XHJcbiAgICAgICAgbWV0aG9kLFxyXG4gICAgICAgIGNhbGxzOiBzdGF0cy5jYWxscyxcclxuICAgICAgICBhdmVyYWdlVGltZTogc3RhdHMuYXZlcmFnZVRpbWUsXHJcbiAgICAgICAgc3VjY2Vzc1JhdGU6IHN0YXRzLnN1Y2Nlc3NSYXRlLFxyXG4gICAgICAgIGxhc3RDYWxsOiBuZXcgRGF0ZShzdGF0cy5sYXN0Q2FsbCkudG9Mb2NhbGVTdHJpbmcoKVxyXG4gICAgICB9KSlcclxuICAgICAgLnNvcnQoKGEsIGIpID0+IGIuY2FsbHMgLSBhLmNhbGxzKSAvLyDmjInosIPnlKjmrKHmlbDmjpLluo9cclxuICB9XHJcblxyXG4gIGV4cG9ydFBlcmZvcm1hbmNlUmVwb3J0KCk6IHtcclxuICAgIHRpbWVzdGFtcDogc3RyaW5nXHJcbiAgICBzdW1tYXJ5OiBQZXJmb3JtYW5jZU1ldHJpY3NcclxuICAgIHJlYWxUaW1lOiBSZWFsVGltZU1ldHJpY3NcclxuICAgIHRvcE1ldGhvZHM6IEFycmF5PHsgbWV0aG9kOiBzdHJpbmc7IGNhbGxzOiBudW1iZXI7IGF2ZXJhZ2VUaW1lOiBudW1iZXIgfT5cclxuICAgIHNsb3dRdWVyaWVzOiBQZXJmb3JtYW5jZU1ldHJpY3NbJ3Nsb3dRdWVyaWVzJ11cclxuICB9IHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICBzdW1tYXJ5OiB0aGlzLmdldFBlcmZvcm1hbmNlTWV0cmljcygpLFxyXG4gICAgICByZWFsVGltZTogdGhpcy5nZXRSZWFsVGltZU1ldHJpY3MoKSxcclxuICAgICAgdG9wTWV0aG9kczogdGhpcy5nZXRNZXRob2RTdGF0aXN0aWNzKCkuc2xpY2UoMCwgMTApLFxyXG4gICAgICBzbG93UXVlcmllczogdGhpcy5nZXRTbG93UXVlcmllcygxMClcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vID09PT09PT09PT09PT09PT09PT09IOaJuemHj+aTjeS9nCA9PT09PT09PT09PT09PT09PT09PVxyXG5cclxuICAvKipcclxuICAgKiDmibnph4/mk43kvZzorr/pl67lmahcclxuICAgKiDmj5Dkvpvmibnph4/liJvlu7rjgIHmm7TmlrDjgIHliKDpmaTnrYnmk43kvZxcclxuICAgKi9cclxuICBnZXQgYmF0Y2goKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAvLyDmibnph4/liJvlu7rnlJ/kuqforqLljZVcclxuICAgICAgY3JlYXRlUHJvZHVjdGlvbk9yZGVyczogYXN5bmMgKG9yZGVyczogYW55W10pID0+IHtcclxuICAgICAgICBjb25zdCByZXN1bHRzID0gW11cclxuICAgICAgICBjb25zdCBlcnJvcnMgPSBbXVxyXG4gICAgICAgIFxyXG4gICAgICAgIGZvciAoY29uc3Qgb3JkZXIgb2Ygb3JkZXJzKSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLnByb2R1Y3Rpb25PcmRlcnMuY3JlYXRlKG9yZGVyKVxyXG4gICAgICAgICAgICBpZiAocmVzdWx0LnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHJlc3VsdC5kYXRhKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGVycm9ycy5wdXNoKHJlc3VsdC5tZXNzYWdlIHx8ICfliJvlu7rlpLHotKUnKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBlcnJvcnMucHVzaChlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmnKrnn6XplJnor68nKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICBpZiAoZXJyb3JzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGRhdGE6IHJlc3VsdHMsXHJcbiAgICAgICAgICAgIG1lc3NhZ2U6IGDmiJDlip/liJvlu7oke3Jlc3VsdHMubGVuZ3RofeS4queUn+S6p+iuouWNlWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnZXJyb3InIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBkYXRhOiByZXN1bHRzLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBg5Yib5bu66L+H56iL5Lit5Y+R55Sf6ZSZ6K+vOiAke2Vycm9ycy5qb2luKCcsICcpfWAsXHJcbiAgICAgICAgICAgIGVycm9yc1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuXHJcbiAgICAgIC8vIOaJuemHj+abtOaWsOeUn+S6p+iuouWNlVxyXG4gICAgICB1cGRhdGVQcm9kdWN0aW9uT3JkZXJzOiBhc3luYyAodXBkYXRlczogQXJyYXk8e2lkOiBzdHJpbmcsIGRhdGE6IGFueX0+KSA9PiB7XHJcbiAgICAgICAgY29uc3QgcmVzdWx0cyA9IFtdXHJcbiAgICAgICAgY29uc3QgZXJyb3JzID0gW11cclxuICAgICAgICBcclxuICAgICAgICBmb3IgKGNvbnN0IHsgaWQsIGRhdGEgfSBvZiB1cGRhdGVzKSB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLnByb2R1Y3Rpb25PcmRlcnMudXBkYXRlKGlkLCBkYXRhKVxyXG4gICAgICAgICAgICBpZiAocmVzdWx0LnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHJlc3VsdC5kYXRhKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGVycm9ycy5wdXNoKGBJRCAke2lkfTogJHtyZXN1bHQubWVzc2FnZSB8fCAn5pu05paw5aSx6LSlJ31gKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBlcnJvcnMucHVzaChgSUQgJHtpZH06ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6ZSZ6K+vJ31gKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICBpZiAoZXJyb3JzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGRhdGE6IHJlc3VsdHMsXHJcbiAgICAgICAgICAgIG1lc3NhZ2U6IGDmiJDlip/mm7TmlrAke3Jlc3VsdHMubGVuZ3RofeS4queUn+S6p+iuouWNlWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnZXJyb3InIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBkYXRhOiByZXN1bHRzLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBg5pu05paw6L+H56iL5Lit5Y+R55Sf6ZSZ6K+vOiAke2Vycm9ycy5qb2luKCcsICcpfWAsXHJcbiAgICAgICAgICAgIGVycm9yc1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuXHJcbiAgICAgIC8vIOaJuemHj+WIoOmZpOeUn+S6p+iuouWNlVxyXG4gICAgICBkZWxldGVQcm9kdWN0aW9uT3JkZXJzOiBhc3luYyAoaWRzOiBzdHJpbmdbXSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHN1Y2Nlc3NJZHMgPSBbXVxyXG4gICAgICAgIGNvbnN0IGVycm9ycyA9IFtdXHJcbiAgICAgICAgXHJcbiAgICAgICAgZm9yIChjb25zdCBpZCBvZiBpZHMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMucHJvZHVjdGlvbk9yZGVycy5kZWxldGUoaWQpXHJcbiAgICAgICAgICAgIGlmIChyZXN1bHQuc3RhdHVzID09PSAnc3VjY2VzcycpIHtcclxuICAgICAgICAgICAgICBzdWNjZXNzSWRzLnB1c2goaWQpXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgZXJyb3JzLnB1c2goYElEICR7aWR9OiAke3Jlc3VsdC5tZXNzYWdlIHx8ICfliKDpmaTlpLHotKUnfWApXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGVycm9ycy5wdXNoKGBJRCAke2lkfTogJHtlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmnKrnn6XplJnor68nfWApXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIGlmIChlcnJvcnMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyBhcyBjb25zdCxcclxuICAgICAgICAgICAgZGF0YTogdHJ1ZSxcclxuICAgICAgICAgICAgbWVzc2FnZTogYOaIkOWKn+WIoOmZpCR7c3VjY2Vzc0lkcy5sZW5ndGh95Liq55Sf5Lqn6K6i5Y2VYFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBzdGF0dXM6ICdlcnJvcicgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGRhdGE6IGZhbHNlLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBg5Yig6Zmk6L+H56iL5Lit5Y+R55Sf6ZSZ6K+vOiAke2Vycm9ycy5qb2luKCcsICcpfWAsXHJcbiAgICAgICAgICAgIGVycm9yc1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuXHJcbiAgICAgIC8vIOaJuemHj+afpeivoueUn+S6p+iuouWNlVxyXG4gICAgICBnZXRQcm9kdWN0aW9uT3JkZXJzOiBhc3luYyAoaWRzOiBzdHJpbmdbXSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdHMgPSBbXVxyXG4gICAgICAgIGNvbnN0IGVycm9ycyA9IFtdXHJcbiAgICAgICAgXHJcbiAgICAgICAgZm9yIChjb25zdCBpZCBvZiBpZHMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMucHJvZHVjdGlvbk9yZGVycy5nZXRCeUlkKGlkKVxyXG4gICAgICAgICAgICBpZiAocmVzdWx0LnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKSB7XHJcbiAgICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHJlc3VsdC5kYXRhKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIGVycm9ycy5wdXNoKGBJRCAke2lkfTogJHtyZXN1bHQubWVzc2FnZSB8fCAn5p+l6K+i5aSx6LSlJ31gKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBlcnJvcnMucHVzaChgSUQgJHtpZH06ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6ZSZ6K+vJ31gKVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICBpZiAoZXJyb3JzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycgYXMgY29uc3QsXHJcbiAgICAgICAgICAgIGRhdGE6IHJlc3VsdHMsXHJcbiAgICAgICAgICAgIG1lc3NhZ2U6IGDmiJDlip/mn6Xor6Ike3Jlc3VsdHMubGVuZ3RofeS4queUn+S6p+iuouWNlWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgc3RhdHVzOiAnZXJyb3InIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICBkYXRhOiByZXN1bHRzLFxyXG4gICAgICAgICAgICBtZXNzYWdlOiBg5p+l6K+i6L+H56iL5Lit5Y+R55Sf6ZSZ6K+vOiAke2Vycm9ycy5qb2luKCcsICcpfWAsXHJcbiAgICAgICAgICAgIGVycm9yc1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gPT09PT09PT09PT09PT09PT09PT0g6YWN572u566h55CGID09PT09PT09PT09PT09PT09PT09XHJcblxyXG4gIC8qKlxyXG4gICAqIOezu+e7n+mFjee9rueuoeeQhlxyXG4gICAqIOaPkOS+m+WKqOaAgemFjee9ruabtOaWsOWSjOafpeivouWKn+iDvVxyXG4gICAqL1xyXG4gIHByaXZhdGUgc3lzdGVtQ29uZmlnID0ge1xyXG4gICAgZW5hYmxlQ2FjaGluZzogdHJ1ZSxcclxuICAgIGNhY2hlVGltZW91dDogNSAqIDYwICogMTAwMCwgLy8gNeWIhumSn1xyXG4gICAgZW5hYmxlTG9nZ2luZzogdHJ1ZSxcclxuICAgIGxvZ0xldmVsOiAnSU5GTycgYXMgJ0RFQlVHJyB8ICdJTkZPJyB8ICdXQVJOJyB8ICdFUlJPUicsXHJcbiAgICBlbmFibGVQZXJmb3JtYW5jZU1vbml0b3Jpbmc6IHRydWUsXHJcbiAgICBtYXhDb25jdXJyZW50UmVxdWVzdHM6IDEwLFxyXG4gICAgcmV0cnlBdHRlbXB0czogMyxcclxuICAgIHJldHJ5RGVsYXk6IDEwMDBcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPluezu+e7n+mFjee9rlxyXG4gICAqL1xyXG4gIGdldENvbmZpZygpIHtcclxuICAgIHJldHVybiB7IC4uLnRoaXMuc3lzdGVtQ29uZmlnIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOabtOaWsOezu+e7n+mFjee9rlxyXG4gICAqL1xyXG4gIHVwZGF0ZUNvbmZpZyhjb25maWc6IFBhcnRpYWw8dHlwZW9mIHRoaXMuc3lzdGVtQ29uZmlnPikge1xyXG4gICAgdGhpcy5zeXN0ZW1Db25maWcgPSB7IC4uLnRoaXMuc3lzdGVtQ29uZmlnLCAuLi5jb25maWcgfVxyXG4gICAgY29uc29sZS5sb2coJ1tEYXRhQWNjZXNzTWFuYWdlcl0g57O757uf6YWN572u5bey5pu05pawOicsIGNvbmZpZylcclxuICAgIFxyXG4gICAgLy8g5qC55o2u6YWN572u5pu05paw5a+55bqU55qE57O757uf6KGM5Li6XHJcbiAgICBpZiAoY29uZmlnLmVuYWJsZUNhY2hpbmcgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhgW0RhdGFBY2Nlc3NNYW5hZ2VyXSDnvJPlrZjnirbmgIE6ICR7Y29uZmlnLmVuYWJsZUNhY2hpbmcgPyAn5bey5ZCv55SoJyA6ICflt7LnpoHnlKgnfWApXHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChjb25maWcuY2FjaGVUaW1lb3V0ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgY29uc29sZS5sb2coYFtEYXRhQWNjZXNzTWFuYWdlcl0g57yT5a2Y6LaF5pe25pe26Ze0OiAke2NvbmZpZy5jYWNoZVRpbWVvdXR9bXNgKVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAoY29uZmlnLmVuYWJsZUxvZ2dpbmcgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhgW0RhdGFBY2Nlc3NNYW5hZ2VyXSDml6Xlv5fnirbmgIE6ICR7Y29uZmlnLmVuYWJsZUxvZ2dpbmcgPyAn5bey5ZCv55SoJyA6ICflt7LnpoHnlKgnfWApXHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmIChjb25maWcubG9nTGV2ZWwgIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhgW0RhdGFBY2Nlc3NNYW5hZ2VyXSDml6Xlv5fnuqfliKs6ICR7Y29uZmlnLmxvZ0xldmVsfWApXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDph43nva7phY3nva7liLDpu5jorqTlgLxcclxuICAgKi9cclxuICByZXNldENvbmZpZygpIHtcclxuICAgIHRoaXMuc3lzdGVtQ29uZmlnID0ge1xyXG4gICAgICBlbmFibGVDYWNoaW5nOiB0cnVlLFxyXG4gICAgICBjYWNoZVRpbWVvdXQ6IDUgKiA2MCAqIDEwMDAsXHJcbiAgICAgIGVuYWJsZUxvZ2dpbmc6IHRydWUsXHJcbiAgICAgIGxvZ0xldmVsOiAnSU5GTycsXHJcbiAgICAgIGVuYWJsZVBlcmZvcm1hbmNlTW9uaXRvcmluZzogdHJ1ZSxcclxuICAgICAgbWF4Q29uY3VycmVudFJlcXVlc3RzOiAxMCxcclxuICAgICAgcmV0cnlBdHRlbXB0czogMyxcclxuICAgICAgcmV0cnlEZWxheTogMTAwMFxyXG4gICAgfVxyXG4gICAgY29uc29sZS5sb2coJ1tEYXRhQWNjZXNzTWFuYWdlcl0g57O757uf6YWN572u5bey6YeN572uJylcclxuICB9XHJcbn1cclxuXHJcbi8vIOWIm+W7uuWNleS+i+WunuS+i1xyXG5leHBvcnQgY29uc3QgZGF0YUFjY2Vzc01hbmFnZXIgPSBEYXRhQWNjZXNzTWFuYWdlci5nZXRJbnN0YW5jZSgpXHJcblxyXG4vLyDwn5SnIOWwhkRhdGFBY2Nlc3NNYW5hZ2Vy5pq06Zyy5Yiw5YWo5bGA5L2c55So5Z+f77yI55So5LqO6LCD6K+V5ZKM5rWL6K+V77yJXHJcbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICh3aW5kb3cgYXMgYW55KS5kYXRhQWNjZXNzTWFuYWdlciA9IGRhdGFBY2Nlc3NNYW5hZ2VyXHJcbiAgY29uc29sZS5sb2coJ/CflKcgW0RhdGFBY2Nlc3NNYW5hZ2VyXSDlt7LmmrTpnLLliLDlhajlsYDkvZznlKjln58gd2luZG93LmRhdGFBY2Nlc3NNYW5hZ2VyJylcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHtcclxuICBwcm9kdWN0cyxcclxuICBjdXN0b21lcnMsXHJcbiAgZW1wbG95ZWVzLFxyXG4gIGludmVudG9yeSxcclxuICBwcm9kdWN0aW9uT3JkZXJzLFxyXG4gIHByb2R1Y3Rpb25Xb3JrT3JkZXJzLFxyXG4gIHdvcmtzdGF0aW9ucyxcclxuICBjb3N0Q2FsY3VsYXRpb25zLFxyXG4gIGF1dGgsXHJcbiAgdG9rZW5NYW5hZ2VtZW50XHJcbn0gPSBkYXRhQWNjZXNzTWFuYWdlclxyXG4iXSwibmFtZXMiOlsiUHJvZHVjdERhdGFBY2Nlc3NTZXJ2aWNlIiwiQ3VzdG9tZXJEYXRhQWNjZXNzU2VydmljZSIsIkVtcGxveWVlRGF0YUFjY2Vzc1NlcnZpY2UiLCJJbnZlbnRvcnlEYXRhQWNjZXNzU2VydmljZSIsIk9yZGVyRGF0YUFjY2Vzc1NlcnZpY2UiLCJXb3JrVGltZURhdGFBY2Nlc3NTZXJ2aWNlIiwiUHJvZHVjdGlvbk9yZGVyRGF0YUFjY2Vzc1NlcnZpY2UiLCJQcm9kdWN0aW9uV29ya09yZGVyRGF0YUFjY2Vzc1NlcnZpY2UiLCJXb3Jrc3RhdGlvbkRhdGFBY2Nlc3NTZXJ2aWNlIiwiQ29zdENhbGN1bGF0aW9uRGF0YUFjY2Vzc1NlcnZpY2UiLCJBdXRoRGF0YUFjY2Vzc1NlcnZpY2UiLCJSb2xlRGF0YUFjY2Vzc1NlcnZpY2UiLCJUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlIiwiZGF0YUNoYW5nZU5vdGlmaWVyIiwicGVyZm9ybWFuY2VPcHRpbWl6ZXIiLCJkYXRhQWNjZXNzUGVyZm9ybWFuY2VNb25pdG9yIiwiYmF0Y2hPcGVyYXRpb25Db250cm9sbGVyIiwiQVBJX1ZFUlNJT04iLCJ0aW1lc3RhbXBHZW5lcmF0b3IiLCJERUZBVUxUX0NBQ0hFX0NPTkZJRyIsImVuYWJsZUNhY2hpbmciLCJkZWZhdWx0VFRMIiwibWF4U2l6ZSIsInN0cmF0ZWdpZXMiLCJvcmRlcnMiLCJ0dGwiLCJlbmFibGVkIiwicHJpb3JpdHkiLCJtYXhFbnRyaWVzIiwicHJvZHVjdHMiLCJ3b3Jrc3RhdGlvbnMiLCJzdGF0aXN0aWNzIiwiY3VzdG9tZXJzIiwiZW1wbG95ZWVzIiwibW9uaXRvcmluZyIsInJlcG9ydEludGVydmFsIiwic21hcnRFdmljdGlvbiIsImFsZ29yaXRobSIsInRocmVzaG9sZHMiLCJtZW1vcnlXYXJuaW5nIiwibWVtb3J5Q3JpdGljYWwiLCJwcmV3YXJtaW5nIiwic2NoZWR1bGVJbnRlcnZhbCIsIkRFRkFVTFRfQ09ORklHIiwiZW5hYmxlTG9nZ2luZyIsIm1heENhY2hlU2l6ZSIsInJldHJ5QXR0ZW1wdHMiLCJyZXRyeURlbGF5IiwiRGF0YUFjY2Vzc01hbmFnZXIiLCJnZXRJbnN0YW5jZSIsImNvbmZpZyIsImluc3RhbmNlIiwibG9nQWNjZXNzIiwic2VydmljZSIsIm1ldGhvZCIsInBhcmFtcyIsImR1cmF0aW9uIiwic3VjY2VzcyIsImVycm9yIiwiZnJvbUNhY2hlIiwibG9nIiwidGltZXN0YW1wIiwibm93IiwiYWNjZXNzTG9ncyIsInB1c2giLCJsZW5ndGgiLCJzbGljZSIsInByb2Nlc3MiLCJhZGRUb1BlcmZvcm1hbmNlQmF0Y2giLCJwZXJmb3JtYW5jZUJhdGNoIiwiRGF0ZSIsImJhdGNoUmVwb3J0VGltZXIiLCJzZXRUaW1lb3V0IiwiZmx1c2hQZXJmb3JtYW5jZUJhdGNoIiwiQkFUQ0hfUkVQT1JUX0lOVEVSVkFMIiwic3RhdHMiLCJ0b3RhbE9wZXJhdGlvbnMiLCJzdWNjZXNzQ291bnQiLCJmaWx0ZXIiLCJvcCIsImNhY2hlSGl0cyIsImF2ZXJhZ2VUaW1lIiwiTWF0aCIsInJvdW5kIiwicmVkdWNlIiwic3VtIiwic2VydmljZXMiLCJTZXQiLCJtYXAiLCJzaXplIiwidGltZVJhbmdlIiwic3RhcnQiLCJtaW4iLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJlbmQiLCJtYXgiLCJjb25zb2xlIiwic2hvdWxkVXNlQ2FjaGUiLCJyZWFkTWV0aG9kcyIsInNvbWUiLCJyZWFkTWV0aG9kIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInNob3VsZENhY2hlUmVzdWx0IiwicmVzdWx0Iiwic3RhdHVzIiwiZ2VuZXJhdGVDYWNoZUtleSIsImJhc2VLZXkiLCJPYmplY3QiLCJrZXlzIiwicGFyYW1TdHIiLCJzb3J0ZWRQYXJhbXMiLCJzb3J0Iiwia2V5IiwiSlNPTiIsInN0cmluZ2lmeSIsIlN0cmluZyIsIkJ1ZmZlciIsImZyb20iLCJ0b1N0cmluZyIsImdldERhdGFUeXBlRnJvbVNlcnZpY2UiLCJnZXRBY2Nlc3NGcmVxdWVuY3kiLCJvcGVyYXRpb25LZXkiLCJyZWNlbnRMb2dzIiwiZ2V0VGltZSIsInNob3VsZFByZWxvYWQiLCJkYXRhIiwiaXRlbXMiLCJnZXRGcm9tQ2FjaGUiLCJlbnRyeSIsImNhY2hlIiwiZ2V0IiwiZXhwaXJlc0F0IiwiZGVsZXRlIiwiYWNjZXNzQ291bnQiLCJsYXN0QWNjZXNzZWQiLCJzZXRUb0NhY2hlIiwiY2FjaGVDb25maWciLCJzbWFydEV2aWN0RW50cmllcyIsInN0cmF0ZWd5IiwiZGF0YVR5cGUiLCJnZXREYXRhVHlwZSIsImVzdGltYXRlZFNpemUiLCJlc3RpbWF0ZURhdGFTaXplIiwiYWNjZXNzUGF0dGVybiIsInByZWRpY3RBY2Nlc3NQYXR0ZXJuIiwiY3JlYXRlZEF0IiwicmVmcmVzaGFibGUiLCJpc1JlZnJlc2hhYmxlRGF0YSIsInNldCIsInJlY29yZENhY2hlQWNjZXNzIiwiZW50cmllcyIsIkFycmF5IiwiZXZpY3RCeUxSVSIsImV2aWN0QnlMRlUiLCJldmljdEFkYXB0aXZlIiwiY2FjaGVTdGF0cyIsImV2aWN0aW9ucyIsInNjb3JlZCIsInNjb3JlIiwicHJpb3JpdHlTY29yZXMiLCJjcml0aWNhbCIsImhpZ2giLCJtZWRpdW0iLCJsb3ciLCJhZ2UiLCJmcmVxdWVuY3kiLCJsYXN0QWNjZXNzQWdlIiwiYSIsImIiLCJkZWxldGVDb3VudCIsImZsb29yIiwiaSIsImNhY2hlQWNjZXNzSGlzdG9yeSIsImV2aWN0T2xkZXN0RW50cmllcyIsImV4ZWN1dGVXaXRoQ2FjaGUiLCJleGVjdXRvciIsInNob3VsZENhY2hlIiwiY2FjaGVLZXkiLCJyZXF1ZXN0Q2FjaGUiLCJoYXMiLCJjYWNoZWRFbnRyeSIsImhpdHMiLCJyZXF1ZXN0UHJvbWlzZSIsIm1pc3NlcyIsImluaXRpYWxpemVBZHZhbmNlZENhY2hpbmciLCJzdGFydENhY2hlUHJld2FybWluZyIsInN0YXJ0TWVtb3J5TW9uaXRvcmluZyIsInByZXdhcm1UaW1lciIsImNsZWFySW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInBlcmZvcm1DYWNoZVByZXdhcm1pbmciLCJtZW1vcnlQcmVzc3VyZSIsInByZXdhcm1TdHJhdGVneSIsInByZXdhcm1Xb3Jrc3RhdGlvbnMiLCJwcmV3YXJtT3JkZXJzIiwicHJld2FybVByb2R1Y3RzIiwiZ2V0QWN0aXZlV29ya3N0YXRpb25zIiwicHJld2FybUhpdHMiLCJ3YXJuIiwiZ2V0QWxsIiwibGltaXQiLCJnZXRBY3RpdmUiLCJjaGVja01lbW9yeVByZXNzdXJlIiwibGFzdE1lbW9yeUNoZWNrIiwiY2FjaGVNZW1vcnlSYXRpbyIsImhhbmRsZUNyaXRpY2FsTWVtb3J5UHJlc3N1cmUiLCJoYW5kbGVNZW1vcnlXYXJuaW5nIiwiY2xlYW51cExvd1ByaW9yaXR5Q2FjaGUiLCJkZWxldGVkQ291bnQiLCJoaXN0b3J5IiwicmVjZW50QWNjZXNzZXMiLCJ0aW1lIiwianNvblN0cmluZyIsInNwbGljZSIsImdldENhY2hlVFRMIiwic2VydmljZUNvbmZpZyIsImluaXRpYWxpemVQZXJmb3JtYW5jZU1vbml0b3JpbmciLCJwZXJmb3JtYW5jZU1ldHJpY3MiLCJ0b3RhbENhbGxzIiwic3VjY2Vzc0NhbGxzIiwiZXJyb3JDYWxscyIsImF2ZXJhZ2VSZXNwb25zZVRpbWUiLCJtaW5SZXNwb25zZVRpbWUiLCJJbmZpbml0eSIsIm1heFJlc3BvbnNlVGltZSIsImNhY2hlSGl0UmF0ZSIsInNsb3dRdWVyaWVzIiwibWV0aG9kU3RhdHMiLCJNYXAiLCJob3VybHlTdGF0cyIsInN0YXJ0UGVyZm9ybWFuY2VDbGVhbnVwVGFzayIsImNsZWFudXBQZXJmb3JtYW5jZURhdGEiLCJvbmVIb3VyQWdvIiwib25lRGF5QWdvIiwicmVjZW50Q2FsbHMiLCJjYWxsIiwicXVlcnkiLCJ1cGRhdGVIb3VybHlTdGF0cyIsImN1cnJlbnRIb3VyIiwiZ2V0RnVsbFllYXIiLCJnZXRNb250aCIsInBhZFN0YXJ0IiwiZ2V0RGF0ZSIsImdldEhvdXJzIiwiaG91ckNhbGxzIiwidG90YWxUaW1lIiwiZXJyb3JDb3VudCIsImhvdXJTdGF0IiwiaG91ciIsImNhbGxzIiwiZXJyb3JSYXRlIiwiZXhpc3RpbmdJbmRleCIsImZpbmRJbmRleCIsInN0YXQiLCJ1cGRhdGVQZXJmb3JtYW5jZU1ldHJpY3MiLCJjYWNoZWQiLCJtZXRob2RTdGF0Iiwic3VjY2Vzc1JhdGUiLCJsYXN0Q2FsbCIsImVycm9ycyIsImV4ZWN1dGVXaXRoTG9nZ2luZyIsInN0YXJ0VGltZSIsInJlY29yZFJlcXVlc3QiLCJlcnJvck1lc3NhZ2UiLCJFcnJvciIsIm1lc3NhZ2UiLCJwcm9kdWN0U2VydmljZSIsImdldFByb2R1Y3RzIiwiZ2V0QnlJZCIsImlkIiwiZ2V0UHJvZHVjdEJ5SWQiLCJnZXRCeUNvZGUiLCJjb2RlIiwiZ2V0UHJvZHVjdEJ5Q29kZSIsImNyZWF0ZSIsImNyZWF0ZVByb2R1Y3QiLCJ1cGRhdGUiLCJ1cGRhdGVzIiwidXBkYXRlUHJvZHVjdCIsImRlbGV0ZVByb2R1Y3QiLCJnZXRBY3RpdmVQcm9kdWN0cyIsImdldEJ5Q2F0ZWdvcnkiLCJjYXRlZ29yeSIsImdldFByb2R1Y3RzQnlDYXRlZ29yeSIsInNlYXJjaCIsImtleXdvcmQiLCJzZWFyY2hQcm9kdWN0cyIsImdldEJ5TW9sZCIsIm1vbGRJZCIsImdldFByb2R1Y3RzQnlNb2xkIiwiZ2V0TW9sZFVzYWdlIiwiZ2V0TW9sZFVzYWdlU3RhdGlzdGljcyIsImN1c3RvbWVyU2VydmljZSIsImdldEN1c3RvbWVycyIsImdldEN1c3RvbWVyQnlJZCIsImNyZWF0ZUN1c3RvbWVyIiwidXBkYXRlQ3VzdG9tZXIiLCJkZWxldGVDdXN0b21lciIsImdldEFjdGl2ZUN1c3RvbWVycyIsInNlYXJjaEN1c3RvbWVycyIsImVtcGxveWVlU2VydmljZSIsImdldEVtcGxveWVlcyIsImdldEVtcGxveWVlQnlJZCIsImNyZWF0ZUVtcGxveWVlIiwidXBkYXRlRW1wbG95ZWUiLCJkZWxldGVFbXBsb3llZSIsImdldEFjdGl2ZUVtcGxveWVlcyIsImdldEJ5RGVwYXJ0bWVudCIsImRlcGFydG1lbnQiLCJnZXRFbXBsb3llZXNCeURlcGFydG1lbnQiLCJnZXRCeVJvbGUiLCJyb2xlIiwiZ2V0RW1wbG95ZWVzQnlSb2xlIiwiZ2V0U2FsZXMiLCJnZXRTYWxlc0VtcGxveWVlcyIsImludmVudG9yeSIsImludmVudG9yeVNlcnZpY2UiLCJnZXRQcm9kdWN0SW52ZW50b3J5IiwiZ2V0QnlQcm9kdWN0Q29kZSIsInByb2R1Y3RDb2RlIiwiZ2V0SW52ZW50b3J5QnlQcm9kdWN0Q29kZSIsInVwZGF0ZUludmVudG9yeSIsImdldExvd1N0b2NrIiwidGhyZXNob2xkIiwiZ2V0TG93U3RvY2tQcm9kdWN0cyIsImdldFZhbHVlIiwiZ2V0SW52ZW50b3J5VmFsdWUiLCJhdXRoIiwibG9naW4iLCJjcmVkZW50aWFscyIsImF1dGhTZXJ2aWNlIiwibG9nb3V0IiwicmVmcmVzaFRva2VuIiwicmVxdWVzdCIsInZhbGlkYXRlVG9rZW4iLCJ0b2tlbiIsImdldFVzZXJCeUlkIiwiZ2V0VXNlckJ5VXNlcm5hbWUiLCJ1c2VybmFtZSIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsInVwZGF0ZVVzZXIiLCJkZWxldGVVc2VyIiwiY3JlYXRlU2Vzc2lvbiIsInVzZXJJZCIsInNlc3Npb25EYXRhIiwiZ2V0U2Vzc2lvbiIsInNlc3Npb25JZCIsInVwZGF0ZVNlc3Npb24iLCJkZWxldGVTZXNzaW9uIiwiZ2V0VXNlclNlc3Npb25zIiwiY2hhbmdlUGFzc3dvcmQiLCJjdXJyZW50UGFzc3dvcmQiLCJuZXdQYXNzd29yZCIsInJlc2V0UGFzc3dvcmQiLCJ0b2tlbk1hbmFnZW1lbnQiLCJnZW5lcmF0ZUFjY2Vzc1Rva2VuIiwicGF5bG9hZCIsInRva2VuTWFuYWdlbWVudFNlcnZpY2UiLCJnZW5lcmF0ZVJlZnJlc2hUb2tlbiIsInZlcmlmeUFjY2Vzc1Rva2VuIiwidG9rZW5MZW5ndGgiLCJ2ZXJpZnlSZWZyZXNoVG9rZW4iLCJleHRyYWN0VXNlckluZm8iLCJzaG91bGRSZWZyZXNoVG9rZW4iLCJ0aHJlc2hvbGRNaW51dGVzIiwiZ2V0VG9rZW5SZW1haW5pbmdUaW1lIiwiZ2VuZXJhdGVTZXNzaW9uSWQiLCJjaGVja0NvbmZpZ3VyYXRpb24iLCJyb2xlcyIsInJvbGVTZXJ2aWNlIiwiZ2V0Um9sZXMiLCJnZXRSb2xlQnlJZCIsImdldFJvbGVCeUNvZGUiLCJyb2xlRGF0YSIsImNyZWF0ZVJvbGUiLCJ1cGRhdGVSb2xlIiwiZGVsZXRlUm9sZSIsImdldFBlcm1pc3Npb25zIiwiZ2V0UGVybWlzc2lvbnNCeU1vZHVsZSIsIm1vZHVsZSIsImFzc2lnblBlcm1pc3Npb25zIiwicm9sZUlkIiwicGVybWlzc2lvbklkcyIsImFzc2lnblJvbGVQZXJtaXNzaW9ucyIsIl9fbWFuYWdlcklkIiwib3JkZXJTZXJ2aWNlIiwiX19zZXJ2aWNlSWQiLCJnZXRPcmRlcnMiLCJnZXRPcmRlckJ5SWQiLCJnZXRCeU51bWJlciIsIm9yZGVyTnVtYmVyIiwiZ2V0T3JkZXJCeU51bWJlciIsImNyZWF0ZU9yZGVyIiwidXBkYXRlT3JkZXIiLCJkZWxldGVPcmRlciIsImdldEJ5U3RhdHVzIiwiZ2V0T3JkZXJzQnlTdGF0dXMiLCJnZXRCeUN1c3RvbWVyIiwiY3VzdG9tZXJJZCIsImdldE9yZGVyc0J5Q3VzdG9tZXIiLCJnZXRCeVNhbGVzUmVwIiwic2FsZXNSZXBJZCIsImdldE9yZGVyc0J5U2FsZXNSZXAiLCJnZXRCeURhdGVSYW5nZSIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJnZXRPcmRlcnNCeURhdGVSYW5nZSIsIndvcmtUaW1lIiwiZ2V0Q29uZmlndXJhdGlvbnMiLCJ3b3JrVGltZVNlcnZpY2UiLCJnZXRDb25maWd1cmF0aW9uQnlJZCIsImNyZWF0ZUNvbmZpZ3VyYXRpb24iLCJ1cGRhdGVDb25maWd1cmF0aW9uIiwiZGVsZXRlQ29uZmlndXJhdGlvbiIsImFkZFdvcmtUaW1lU2xvdCIsImNvbmZpZ0lkIiwic2xvdCIsInVwZGF0ZVdvcmtUaW1lU2xvdCIsInNsb3RJZCIsImRlbGV0ZVdvcmtUaW1lU2xvdCIsImNhbGN1bGF0ZVdvcmtpbmdNaW51dGVzIiwid29ya1RpbWVTbG90cyIsImJyZWFrVGltZVNsb3RzIiwidmFsaWRhdGVUaW1lU2xvdCIsImVuZFRpbWUiLCJnZXREZWZhdWx0IiwiZ2V0RGVmYXVsdENvbmZpZ3VyYXRpb24iLCJwcm9kdWN0aW9uT3JkZXJzIiwicHJvZHVjdGlvbk9yZGVyU2VydmljZSIsImdldEJ5T3JkZXJOdW1iZXIiLCJjcmVhdGVGcm9tTVJQIiwibXJwRGF0YSIsIm1ycEV4ZWN1dGlvbklkIiwibXJwRXhlY3V0ZWRCeSIsIm1ycEV4ZWN1dGVkQXQiLCJ0b0lTT1N0cmluZyIsImdldEJ5U2FsZXNPcmRlcklkIiwic2FsZXNPcmRlcklkIiwiZ2V0U3RhdGlzdGljcyIsInByb2R1Y3Rpb25Xb3JrT3JkZXJzIiwicHJvZHVjdGlvbldvcmtPcmRlclNlcnZpY2UiLCJnZXRCeUJhdGNoTnVtYmVyIiwiYmF0Y2hOdW1iZXIiLCJnZXRCeVNvdXJjZU9yZGVySWQiLCJzb3VyY2VPcmRlcklkIiwiZ2V0QnlXb3Jrc3RhdGlvbiIsIndvcmtzdGF0aW9uIiwid29ya3N0YXRpb25TZXJ2aWNlIiwiZ2V0V29ya3N0YXRpb25zIiwiZ2V0V29ya3N0YXRpb25CeUlkIiwiZ2V0U3RhdHVzIiwiZ2V0V29ya3N0YXRpb25TdGF0dXMiLCJ1cGRhdGVTdGF0dXMiLCJ1cGRhdGVXb3Jrc3RhdGlvblN0YXR1cyIsImFkZFRvUXVldWUiLCJ3b3Jrc3RhdGlvbklkIiwibWlncmF0ZUJhdGNoTnVtYmVyRm9ybWF0cyIsInJlc2V0QWxsV29ya3N0YXRpb25zVG9JZGxlIiwiZ2V0QWNjZXNzTG9ncyIsImNsZWFyQWNjZXNzTG9ncyIsInRvdGFsUmVxdWVzdHMiLCJzdWNjZXNzZnVsUmVxdWVzdHMiLCJmYWlsZWRSZXF1ZXN0cyIsImF2ZXJhZ2VEdXJhdGlvbiIsImluaXRpYWxpemVEYXRhQ2hhbmdlTm90aWZpY2F0aW9uIiwiZ2V0RGF0YUNoYW5nZU5vdGlmaWVyIiwiY29zdENhbGN1bGF0aW9ucyIsImdldEFsbENhbGN1bGF0aW9ucyIsImNvc3RDYWxjdWxhdGlvblNlcnZpY2UiLCJnZXRDYWxjdWxhdGlvbkJ5SWQiLCJjcmVhdGVDYWxjdWxhdGlvbiIsInVwZGF0ZUNhbGN1bGF0aW9uIiwiZGVsZXRlQ2FsY3VsYXRpb24iLCJnZXRDYWxjdWxhdGlvbnNCeVByb2R1Y3QiLCJwcm9kdWN0TW9kZWxDb2RlIiwiZ2V0UGVuZGluZ1JlY29uY2lsaWF0aW9ucyIsImdldENvc3RTdW1tYXJ5IiwidXBkYXRlQ29uZmlnIiwibmV3Q29uZmlnIiwiY2xlYXIiLCJjbGVhclNlcnZpY2VDYWNoZSIsInNlcnZpY2VOYW1lIiwib3BlcmF0aW9uIiwicGF0dGVybiIsInN0YXJ0c1dpdGgiLCJjbGVhckRhdGFUeXBlQ2FjaGUiLCJhZmZlY3RlZElkcyIsInNlcnZpY2VNYXAiLCJoYXNNYXRjaGluZ0lkIiwiam9pbiIsImNsZWFyQWxsQ2FjaGUiLCJiZWZvcmVTaXplIiwiZ2V0Q2FjaGVTdGF0aXN0aWNzIiwidmFsdWVzIiwidG90YWxTaXplIiwiYXZlcmFnZUVudHJ5U2l6ZSIsInByaW9yaXR5RGlzdHJpYnV0aW9uIiwiZm9yRWFjaCIsImhpdFJhdGUiLCJ1cGRhdGVDYWNoZUNvbmZpZyIsImJhdGNoIiwiY3JlYXRlUHJvZHVjdGlvbk9yZGVycyIsImJhdGNoQ3JlYXRlUHJvZHVjdGlvbk9yZGVycyIsInVwZGF0ZVByb2R1Y3Rpb25PcmRlcnMiLCJiYXRjaFVwZGF0ZVByb2R1Y3Rpb25PcmRlcnMiLCJjcmVhdGVQcm9kdWN0aW9uV29ya09yZGVycyIsIndvcmtPcmRlcnMiLCJwZXJmb3JtYW5jZSIsInRhc2tzIiwid29ya09yZGVyIiwidGFza05hbWVzIiwiXyIsImluZGV4IiwiYmF0Y2hSZXN1bHQiLCJleGVjdXRlQmF0Y2giLCJzdWNjZXNzZnVsIiwiZmFpbGVkIiwic3VjY2Vzc2Z1bERhdGEiLCJCb29sZWFuIiwidG90YWxEdXJhdGlvbiIsInVwZGF0ZVdvcmtzdGF0aW9uU3RhdHVzZXMiLCJyZXN1bHRzIiwiUHJvbWlzZSIsImFsbCIsInUiLCJyIiwiZ2V0TWV0cmljcyIsImdldEFsZXJ0cyIsImdldFN1Z2dlc3Rpb25zIiwiZ2V0T3B0aW1pemF0aW9uU3VnZ2VzdGlvbnMiLCJnZW5lcmF0ZVJlcG9ydCIsImdlbmVyYXRlUGVyZm9ybWFuY2VSZXBvcnQiLCJyZXNldCIsIm9wdGltaXplQ2FjaGUiLCJnZXRDYWNoZU1ldHJpY3MiLCJnZXRPcHRpbWl6ZXJNZXRyaWNzIiwiZ2V0UGVyZm9ybWFuY2VNZXRyaWNzIiwiZ2V0UmVhbFRpbWVNZXRyaWNzIiwiYmF0Y2hDb250cm9sbGVyU3RhdHVzIiwic3lzdGVtSGVhbHRoIiwiYW5hbHl6ZVN5c3RlbUhlYWx0aCIsImN1cnJlbnRDb25jdXJyZW5jeSIsInJ1bm5pbmciLCJxdWV1ZUxlbmd0aCIsInF1ZXVlZCIsImlzc3VlcyIsInJlY29tbWVuZGF0aW9ucyIsInJlY2VudEVycm9yUmF0ZSIsInRvRml4ZWQiLCJiYXRjaFN0YXR1cyIsInJlc2V0UGVyZm9ybWFuY2VNZXRyaWNzIiwiZ2V0U2xvd1F1ZXJpZXMiLCJnZXRNZXRob2RTdGF0aXN0aWNzIiwidG9Mb2NhbGVTdHJpbmciLCJleHBvcnRQZXJmb3JtYW5jZVJlcG9ydCIsInN1bW1hcnkiLCJyZWFsVGltZSIsInRvcE1ldGhvZHMiLCJvcmRlciIsImRlbGV0ZVByb2R1Y3Rpb25PcmRlcnMiLCJpZHMiLCJzdWNjZXNzSWRzIiwiZ2V0UHJvZHVjdGlvbk9yZGVycyIsImdldENvbmZpZyIsInN5c3RlbUNvbmZpZyIsInVuZGVmaW5lZCIsImNhY2hlVGltZW91dCIsImxvZ0xldmVsIiwicmVzZXRDb25maWciLCJlbmFibGVQZXJmb3JtYW5jZU1vbml0b3JpbmciLCJtYXhDb25jdXJyZW50UmVxdWVzdHMiLCJyYW5kb20iLCJzdWJzdHIiLCJ2ZXJzaW9uIiwiZGF0YUFjY2Vzc01hbmFnZXIiLCJ3aW5kb3ciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\n"));

/***/ })

});