/**
 * 简化用户管理组件
 * 
 * 根据PRD要求，提供简洁实用的用户管理功能
 * 专为小于20人的企业设计，支持基本的CRUD操作
 */

'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Alert
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  CrownOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { User, UserStatus } from '@/types/auth'
import { SimpleRoleRadio } from '@/components/auth/SimpleRoleSelector'
import { SimplePermissionLevel } from '@/utils/auth/SimplePermissionManager'
import { SECURITY_CONFIG } from '@/config/security'

const { Option } = Select

/**
 * 简化的用户数据接口
 */
interface SimpleUser {
  id: string
  username: string
  fullName: string
  email?: string
  role: SimplePermissionLevel
  status: UserStatus
  lastLoginAt?: string
  createdAt: string
}

/**
 * 用户表单数据接口
 */
interface UserFormData {
  username: string
  fullName: string
  email?: string
  password?: string
  role: SimplePermissionLevel
  status: UserStatus
}

/**
 * 简化用户管理组件
 */
export const SimpleUserManagement: React.FC = () => {
  const [users, setUsers] = useState<SimpleUser[]>([])
  const [loading, setLoading] = useState(false)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<SimpleUser | null>(null)
  const [form] = Form.useForm()

  // 加载用户数据
  const loadUsers = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/users')
      const result = await response.json()
      
      if (result.success && result.data) {
        // 简化用户数据，只保留必要字段
        const simplifiedUsers: SimpleUser[] = result.data.users.map((user: User) => ({
          id: user.id,
          username: user.username,
          fullName: user.fullName,
          email: user.email,
          role: user.roles.some(r => r.code === 'admin') ? 'admin' : 'employee',
          status: user.status,
          lastLoginAt: user.lastLoginAt,
          createdAt: user.createdAt
        }))
        setUsers(simplifiedUsers)
      } else {
        message.error(result.error || '获取用户列表失败')
      }
    } catch (error) {
      console.error('加载用户数据失败:', error)
      message.error('加载用户数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadUsers()
  }, [])

  // 表格列定义
  const columns: ColumnsType<SimpleUser> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <Space>
          {record.role === 'admin' ? <CrownOutlined className="text-red-500" /> : <UserOutlined />}
          <span className="font-medium">{text}</span>
        </Space>
      )
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName'
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: SimplePermissionLevel) => (
        <Tag color={role === 'admin' ? 'red' : 'blue'}>
          {role === 'admin' ? '管理员' : '员工'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: UserStatus) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '禁用'}
        </Tag>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      render: (text) => text ? new Date(text).toLocaleDateString() : '从未登录'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
          >
            编辑
          </Button>
          {record.username !== 'admin' && (
            <Popconfirm
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDeleteUser(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  // 处理新建用户
  const handleCreateUser = () => {
    setEditingUser(null)
    form.resetFields()
    form.setFieldsValue({ role: 'employee', status: 'active' })
    setIsModalVisible(true)
  }

  // 处理编辑用户
  const handleEditUser = (user: SimpleUser) => {
    setEditingUser(user)
    form.setFieldsValue({
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      role: user.role,
      status: user.status
    })
    setIsModalVisible(true)
  }

  // 处理删除用户
  const handleDeleteUser = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      })
      const result = await response.json()
      
      if (result.success) {
        message.success('用户删除成功')
        loadUsers()
      } else {
        message.error(result.error || '删除用户失败')
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      message.error('删除用户失败')
    }
  }

  // 处理表单提交
  const handleFormSubmit = async (values: UserFormData) => {
    try {
      setLoading(true)

      const url = editingUser ? `/api/users/${editingUser.id}` : '/api/users'
      const method = editingUser ? 'PUT' : 'POST'

      // 构建请求数据
      const requestData = {
        ...values,
        roleIds: [values.role] // 转换为角色ID数组
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      const result = await response.json()

      if (result.success) {
        message.success(editingUser ? '用户更新成功' : '用户创建成功')
        setIsModalVisible(false)
        loadUsers()
      } else {
        message.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('操作失败:', error)
      message.error('操作失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* 页面标题和操作 */}
      <Card>
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold">用户管理</h2>
            <p className="text-gray-500 text-sm mt-1">
              管理系统用户，支持管理员和员工两种角色
            </p>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleCreateUser}
          >
            新建用户
          </Button>
        </div>
      </Card>

      {/* 用户统计 */}
      <Card>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">{users.length}</div>
            <div className="text-gray-500">总用户数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">
              {users.filter(u => u.role === 'admin').length}
            </div>
            <div className="text-gray-500">管理员</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {users.filter(u => u.status === 'active').length}
            </div>
            <div className="text-gray-500">活跃用户</div>
          </div>
        </div>
      </Card>

      {/* 用户列表 */}
      <Card title="用户列表">
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: false,
            showTotal: (total) => `共 ${total} 个用户`
          }}
        />
      </Card>

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={500}
      >
        <Alert
          message="简化用户管理"
          description="只需要设置基本信息和角色，系统会自动分配相应权限"
          type="info"
          showIcon
          className="mb-4"
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' }
            ]}
          >
            <Input
              placeholder="请输入用户名"
              disabled={!!editingUser}
            />
          </Form.Item>

          <Form.Item
            name="fullName"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
          >
            <Input placeholder="请输入邮箱（可选）" />
          </Form.Item>

          {!editingUser && (
            <Form.Item
              name="password"
              label="初始密码"
              rules={[
                { required: true, message: '请输入初始密码' },
                { min: SECURITY_CONFIG.password.minLength, message: `密码至少${SECURITY_CONFIG.password.minLength}个字符` }
              ]}
            >
              <Input.Password placeholder="请输入初始密码" />
            </Form.Item>
          )}

          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <SimpleRoleRadio readonly={false} showDetails={false} />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="active">正常</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingUser ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SimpleUserManagement
