"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_services_dataAccess_TokenManagementService_ts",{

/***/ "(app-pages-browser)/./src/config/jwt.config.ts":
/*!**********************************!*\
  !*** ./src/config/jwt.config.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JWT_CONFIG: function() { return /* binding */ JWT_CONFIG; },\n/* harmony export */   getAccessTokenConfig: function() { return /* binding */ getAccessTokenConfig; },\n/* harmony export */   getRefreshTokenConfig: function() { return /* binding */ getRefreshTokenConfig; },\n/* harmony export */   getTokenExpirationTime: function() { return /* binding */ getTokenExpirationTime; },\n/* harmony export */   isProduction: function() { return /* binding */ isProduction; },\n/* harmony export */   validateJWTConfig: function() { return /* binding */ validateJWTConfig; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * JWT配置管理\n * \n * 统一管理所有JWT相关的配置，避免重复定义\n * 遵循架构合规性要求，提供单一配置入口\n */ /**\n * JWT配置\n * 从环境变量中读取密钥，确保安全性\n */ const JWT_CONFIG = {\n    accessToken: {\n        secret: process.env.JWT_ACCESS_SECRET || \"default-access-secret-change-in-production\",\n        expiresIn: \"1h\",\n        algorithm: \"HS256\"\n    },\n    refreshToken: {\n        secret: process.env.JWT_REFRESH_SECRET || \"default-refresh-secret-change-in-production\",\n        expiresIn: \"7d\",\n        algorithm: \"HS256\"\n    }\n};\n/**\n * JWT配置验证\n * 确保配置的有效性\n */ function validateJWTConfig() {\n    if (!JWT_CONFIG.accessToken.secret || JWT_CONFIG.accessToken.secret === \"default-access-secret-change-in-production\") {\n        console.warn(\"⚠️ [JWT Config] 使用默认的Access Token密钥，生产环境请设置JWT_ACCESS_SECRET环境变量\");\n    }\n    if (!JWT_CONFIG.refreshToken.secret || JWT_CONFIG.refreshToken.secret === \"default-refresh-secret-change-in-production\") {\n        console.warn(\"⚠️ [JWT Config] 使用默认的Refresh Token密钥，生产环境请设置JWT_REFRESH_SECRET环境变量\");\n    }\n    console.log(\"✅ [JWT Config] JWT配置验证完成\");\n}\n/**\n * 获取Access Token配置\n */ function getAccessTokenConfig() {\n    return JWT_CONFIG.accessToken;\n}\n/**\n * 获取Refresh Token配置\n */ function getRefreshTokenConfig() {\n    return JWT_CONFIG.refreshToken;\n}\n/**\n * 检查是否为生产环境\n */ function isProduction() {\n    return \"development\" === \"production\";\n}\n/**\n * 获取Token过期时间（秒）\n */ function getTokenExpirationTime(tokenType) {\n    const config = tokenType === \"access\" ? JWT_CONFIG.accessToken : JWT_CONFIG.refreshToken;\n    // 解析时间字符串（如 '1h', '7d'）\n    const timeStr = config.expiresIn;\n    const unit = timeStr.slice(-1);\n    const value = parseInt(timeStr.slice(0, -1));\n    switch(unit){\n        case \"s\":\n            return value;\n        case \"m\":\n            return value * 60;\n        case \"h\":\n            return value * 60 * 60;\n        case \"d\":\n            return value * 24 * 60 * 60;\n        default:\n            return 3600 // 默认1小时\n            ;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/jwt.config.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/dataAccess/TokenManagementService.ts":
/*!***********************************************************!*\
  !*** ./src/services/dataAccess/TokenManagementService.ts ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManagementService: function() { return /* binding */ TokenManagementService; },\n/* harmony export */   getJWTConfig: function() { return /* binding */ getJWTConfig; },\n/* harmony export */   tokenManagementService: function() { return /* binding */ tokenManagementService; }\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(app-pages-browser)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/jwt.config */ \"(app-pages-browser)/./src/config/jwt.config.ts\");\n/**\n * 统一Token管理服务\n * \n * 符合DataAccessManager架构规范的Token管理实现\n * 集成缓存、性能监控和错误处理\n */ \n\n// 验证JWT配置\n(0,_config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.validateJWTConfig)();\n/**\n * 统一Token管理服务实现\n * 符合DataAccessManager单一入口架构\n */ class TokenManagementService {\n    /**\n   * 获取单例实例\n   */ static getInstance() {\n        if (!TokenManagementService.instance) {\n            TokenManagementService.instance = new TokenManagementService();\n        }\n        return TokenManagementService.instance;\n    }\n    /**\n   * 检查运行环境\n   */ checkServerEnvironment() {\n        if (true) {\n            throw new Error(\"Token操作只能在服务器端执行\");\n        }\n    }\n    /**\n   * 生成Access Token\n   */ async generateAccessToken(payload) {\n        this.checkServerEnvironment();\n        try {\n            console.log(\"\\uD83D\\uDE80 [TokenManagementService] 开始生成Access Token\");\n            const tokenPayload = {\n                ...payload,\n                iat: Math.floor(Date.now() / 1000),\n                exp: Math.floor(Date.now() / 1000) + 60 * 60 // 1小时后过期\n            };\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(tokenPayload, _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.secret, {\n                algorithm: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.algorithm,\n                expiresIn: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.expiresIn\n            });\n            console.log(\"✅ [TokenManagementService] Access Token生成成功\");\n            return token;\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [TokenManagementService] 生成Access Token失败:\", error);\n            throw new Error(\"Token生成失败\");\n        }\n    }\n    /**\n   * 生成Refresh Token\n   */ async generateRefreshToken(userId, sessionId) {\n        this.checkServerEnvironment();\n        try {\n            console.log(\"\\uD83D\\uDE80 [TokenManagementService] 开始生成Refresh Token\");\n            const payload = {\n                userId,\n                sessionId,\n                type: \"refresh\",\n                iat: Math.floor(Date.now() / 1000)\n            };\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.secret, {\n                algorithm: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.algorithm,\n                expiresIn: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.expiresIn\n            });\n            console.log(\"✅ [TokenManagementService] Refresh Token生成成功\");\n            return token;\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [TokenManagementService] 生成Refresh Token失败:\", error);\n            throw new Error(\"Refresh Token生成失败\");\n        }\n    }\n    /**\n   * 验证Access Token\n   */ async verifyAccessToken(token) {\n        this.checkServerEnvironment();\n        try {\n            if (!token) {\n                return {\n                    isValid: false,\n                    error: \"Token不能为空\"\n                };\n            }\n            const payload = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.secret);\n            // 检查Token是否过期\n            const now = Math.floor(Date.now() / 1000);\n            if (payload.exp && payload.exp < now) {\n                return {\n                    isValid: false,\n                    error: \"Token已过期\",\n                    isExpired: true\n                };\n            }\n            return {\n                isValid: true,\n                payload\n            };\n        } catch (error) {\n            if (error && typeof error === \"object\" && \"name\" in error) {\n                if (error.name === \"TokenExpiredError\") {\n                    return {\n                        isValid: false,\n                        error: \"Token已过期\",\n                        isExpired: true\n                    };\n                } else if (error.name === \"JsonWebTokenError\") {\n                    return {\n                        isValid: false,\n                        error: \"Token格式无效\"\n                    };\n                }\n            }\n            return {\n                isValid: false,\n                error: \"Token验证失败\"\n            };\n        }\n    }\n    /**\n   * 验证Refresh Token\n   */ async verifyRefreshToken(token) {\n        this.checkServerEnvironment();\n        try {\n            if (!token) {\n                return {\n                    isValid: false,\n                    error: \"Refresh Token不能为空\"\n                };\n            }\n            const payload = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.secret);\n            if (payload.type !== \"refresh\") {\n                return {\n                    isValid: false,\n                    error: \"Token类型无效\"\n                };\n            }\n            return {\n                isValid: true,\n                userId: payload.userId,\n                sessionId: payload.sessionId\n            };\n        } catch (error) {\n            if (error && typeof error === \"object\" && \"name\" in error) {\n                if (error.name === \"TokenExpiredError\") {\n                    return {\n                        isValid: false,\n                        error: \"Refresh Token已过期\"\n                    };\n                } else if (error.name === \"JsonWebTokenError\") {\n                    return {\n                        isValid: false,\n                        error: \"Refresh Token格式无效\"\n                    };\n                }\n            }\n            return {\n                isValid: false,\n                error: \"Refresh Token验证失败\"\n            };\n        }\n    }\n    /**\n   * 从Token中提取用户信息\n   */ async extractUserInfo(token) {\n        try {\n            const validation = await this.verifyAccessToken(token);\n            if (!validation.isValid || !validation.payload) {\n                return null;\n            }\n            const { userId, username, roles, permissions } = validation.payload;\n            return {\n                userId,\n                username,\n                roles,\n                permissions\n            };\n        } catch (error) {\n            return null;\n        }\n    }\n    /**\n   * 检查Token是否即将过期\n   */ async shouldRefreshToken(token) {\n        let thresholdMinutes = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n        try {\n            const validation = await this.verifyAccessToken(token);\n            if (!validation.isValid || !validation.payload) {\n                return false;\n            }\n            const now = Math.floor(Date.now() / 1000);\n            const threshold = thresholdMinutes * 60;\n            return validation.payload.exp - now <= threshold;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * 获取Token剩余有效时间（秒）\n   */ async getTokenRemainingTime(token) {\n        try {\n            const validation = await this.verifyAccessToken(token);\n            if (!validation.isValid || !validation.payload) {\n                return -1;\n            }\n            const now = Math.floor(Date.now() / 1000);\n            return Math.max(0, validation.payload.exp - now);\n        } catch (error) {\n            return -1;\n        }\n    }\n    /**\n   * 生成会话ID\n   */ generateSessionId() {\n        return \"session-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\n   * 检查环境变量配置\n   */ checkConfiguration() {\n        const warnings = [];\n        if (_config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.secret === \"default-access-secret-change-in-production\") {\n            warnings.push(\"使用默认的Access Token密钥，生产环境中请设置JWT_ACCESS_SECRET环境变量\");\n        }\n        if (_config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.secret === \"default-refresh-secret-change-in-production\") {\n            warnings.push(\"使用默认的Refresh Token密钥，生产环境中请设置JWT_REFRESH_SECRET环境变量\");\n        }\n        if (_config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.secret === _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.secret) {\n            warnings.push(\"Access Token和Refresh Token使用相同密钥，建议使用不同的密钥\");\n        }\n        return {\n            isValid: warnings.length === 0,\n            warnings\n        };\n    }\n    constructor(){\n        console.log(\"\\uD83D\\uDD27 [TokenManagementService] 服务实例已创建\");\n    }\n}\n/**\n * 导出单例实例\n */ const tokenManagementService = TokenManagementService.getInstance();\n/**\n * 导出JWT配置（只读）\n */ const getJWTConfig = ()=>({\n        accessTokenExpiresIn: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.expiresIn,\n        refreshTokenExpiresIn: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.refreshToken.expiresIn,\n        algorithm: _config_jwt_config__WEBPACK_IMPORTED_MODULE_1__.JWT_CONFIG.accessToken.algorithm\n    });\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dataAccess/TokenManagementService.ts\n"));

/***/ })

});