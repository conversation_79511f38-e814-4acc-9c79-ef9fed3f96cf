#!/usr/bin/env node

/**
 * 代码质量监控脚本
 * 
 * 持续监控认证系统的代码质量和架构合规性
 * 生成质量报告和趋势分析
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class QualityMonitor {
  constructor() {
    this.reportDir = path.join(process.cwd(), 'reports/quality')
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.metrics = {
      architecture: {},
      coverage: {},
      performance: {},
      security: {}
    }
  }

  /**
   * 运行完整的质量监控
   */
  async runFullMonitoring() {
    console.log('🔍 开始代码质量监控...\n')

    // 确保报告目录存在
    this.ensureReportDirectory()

    // 运行各项检查
    await this.checkArchitectureCompliance()
    await this.checkTestCoverage()
    await this.checkPerformanceMetrics()
    await this.checkSecurityIssues()
    await this.checkCodeQuality()

    // 生成综合报告
    this.generateComprehensiveReport()

    console.log('\n📊 质量监控完成！')
    console.log(`📁 报告保存在: ${this.reportDir}`)
  }

  /**
   * 确保报告目录存在
   */
  ensureReportDirectory() {
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true })
    }
  }

  /**
   * 检查架构合规性
   */
  async checkArchitectureCompliance() {
    console.log('📋 检查架构合规性...')
    
    try {
      const output = execSync('npm run check:auth-architecture', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })
      
      this.metrics.architecture = {
        status: 'passed',
        violations: 0,
        warnings: 0,
        details: output
      }
      
      console.log('✅ 架构合规性检查通过')
    } catch (error) {
      this.metrics.architecture = {
        status: 'failed',
        violations: this.extractViolationCount(error.stdout || error.message),
        warnings: this.extractWarningCount(error.stdout || error.message),
        details: error.stdout || error.message
      }
      
      console.log('❌ 架构合规性检查失败')
    }
  }

  /**
   * 检查测试覆盖率
   */
  async checkTestCoverage() {
    console.log('📋 检查测试覆盖率...')
    
    try {
      const output = execSync('npm run test:coverage -- --silent', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })
      
      const coverage = this.parseCoverageOutput(output)
      
      this.metrics.coverage = {
        status: coverage.overall >= 80 ? 'passed' : 'warning',
        overall: coverage.overall,
        lines: coverage.lines,
        functions: coverage.functions,
        branches: coverage.branches,
        statements: coverage.statements,
        details: output
      }
      
      console.log(`✅ 测试覆盖率: ${coverage.overall}%`)
    } catch (error) {
      this.metrics.coverage = {
        status: 'failed',
        error: error.message,
        details: error.stdout || error.message
      }
      
      console.log('❌ 测试覆盖率检查失败')
    }
  }

  /**
   * 检查性能指标
   */
  async checkPerformanceMetrics() {
    console.log('📋 检查性能指标...')
    
    try {
      // 运行性能测试
      const output = execSync('npm run test:performance', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })
      
      this.metrics.performance = {
        status: 'passed',
        details: output
      }
      
      console.log('✅ 性能指标检查通过')
    } catch (error) {
      this.metrics.performance = {
        status: 'warning',
        details: error.stdout || error.message
      }
      
      console.log('⚠️ 性能指标检查有警告')
    }
  }

  /**
   * 检查安全问题
   */
  async checkSecurityIssues() {
    console.log('📋 检查安全问题...')
    
    try {
      // 运行安全审计
      const auditOutput = execSync('npm audit --json', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })
      
      const auditResult = JSON.parse(auditOutput)
      
      this.metrics.security = {
        status: auditResult.metadata.vulnerabilities.total === 0 ? 'passed' : 'warning',
        vulnerabilities: auditResult.metadata.vulnerabilities,
        details: auditOutput
      }
      
      console.log(`✅ 安全检查: ${auditResult.metadata.vulnerabilities.total} 个漏洞`)
    } catch (error) {
      this.metrics.security = {
        status: 'failed',
        error: error.message,
        details: error.stdout || error.message
      }
      
      console.log('❌ 安全检查失败')
    }
  }

  /**
   * 检查代码质量
   */
  async checkCodeQuality() {
    console.log('📋 检查代码质量...')
    
    try {
      // TypeScript 类型检查
      execSync('npm run type-check', { stdio: 'pipe' })
      
      // ESLint 检查
      const lintOutput = execSync('npm run lint', { 
        encoding: 'utf-8',
        stdio: 'pipe'
      })
      
      this.metrics.codeQuality = {
        status: 'passed',
        typescript: 'passed',
        eslint: 'passed',
        details: lintOutput
      }
      
      console.log('✅ 代码质量检查通过')
    } catch (error) {
      this.metrics.codeQuality = {
        status: 'failed',
        error: error.message,
        details: error.stdout || error.message
      }
      
      console.log('❌ 代码质量检查失败')
    }
  }

  /**
   * 生成综合报告
   */
  generateComprehensiveReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(),
      metrics: this.metrics,
      recommendations: this.generateRecommendations()
    }

    // 保存JSON报告
    const jsonReportPath = path.join(this.reportDir, `quality-report-${this.timestamp}.json`)
    fs.writeFileSync(jsonReportPath, JSON.stringify(report, null, 2))

    // 生成Markdown报告
    const markdownReport = this.generateMarkdownReport(report)
    const mdReportPath = path.join(this.reportDir, `quality-report-${this.timestamp}.md`)
    fs.writeFileSync(mdReportPath, markdownReport)

    // 生成最新报告链接
    const latestReportPath = path.join(this.reportDir, 'latest-report.json')
    fs.writeFileSync(latestReportPath, JSON.stringify(report, null, 2))
  }

  /**
   * 生成摘要
   */
  generateSummary() {
    const statuses = Object.values(this.metrics).map(metric => metric.status)
    const passedCount = statuses.filter(status => status === 'passed').length
    const totalCount = statuses.length

    return {
      overallStatus: passedCount === totalCount ? 'passed' : 'warning',
      passedChecks: passedCount,
      totalChecks: totalCount,
      score: Math.round((passedCount / totalCount) * 100)
    }
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = []

    if (this.metrics.architecture.status === 'failed') {
      recommendations.push('修复架构合规性违规问题')
    }

    if (this.metrics.coverage.status !== 'passed') {
      recommendations.push('提高测试覆盖率至80%以上')
    }

    if (this.metrics.security.status !== 'passed') {
      recommendations.push('修复安全漏洞')
    }

    return recommendations
  }

  /**
   * 生成Markdown报告
   */
  generateMarkdownReport(report) {
    return `# 代码质量监控报告

**生成时间**: ${report.timestamp}  
**总体评分**: ${report.summary.score}/100  
**状态**: ${report.summary.overallStatus === 'passed' ? '✅ 通过' : '⚠️ 需要改进'}

## 📊 检查结果摘要

- 通过检查: ${report.summary.passedChecks}/${report.summary.totalChecks}
- 架构合规性: ${this.getStatusIcon(report.metrics.architecture.status)}
- 测试覆盖率: ${this.getStatusIcon(report.metrics.coverage.status)} ${report.metrics.coverage.overall || 'N/A'}%
- 性能指标: ${this.getStatusIcon(report.metrics.performance.status)}
- 安全检查: ${this.getStatusIcon(report.metrics.security.status)}
- 代码质量: ${this.getStatusIcon(report.metrics.codeQuality?.status || 'unknown')}

## 🔍 详细结果

### 架构合规性
- 状态: ${report.metrics.architecture.status}
- 违规数量: ${report.metrics.architecture.violations || 0}
- 警告数量: ${report.metrics.architecture.warnings || 0}

### 测试覆盖率
- 整体覆盖率: ${report.metrics.coverage.overall || 'N/A'}%
- 行覆盖率: ${report.metrics.coverage.lines || 'N/A'}%
- 函数覆盖率: ${report.metrics.coverage.functions || 'N/A'}%
- 分支覆盖率: ${report.metrics.coverage.branches || 'N/A'}%

## 📝 改进建议

${report.recommendations.map(rec => `- ${rec}`).join('\n')}

---
*报告由质量监控系统自动生成*`
  }

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    switch (status) {
      case 'passed': return '✅'
      case 'warning': return '⚠️'
      case 'failed': return '❌'
      default: return '❓'
    }
  }

  /**
   * 解析覆盖率输出
   */
  parseCoverageOutput(output) {
    // 简化的覆盖率解析
    const lines = output.split('\n')
    const summaryLine = lines.find(line => line.includes('All files'))
    
    if (summaryLine) {
      const matches = summaryLine.match(/(\d+\.?\d*)/g)
      if (matches && matches.length >= 4) {
        return {
          statements: parseFloat(matches[0]),
          branches: parseFloat(matches[1]),
          functions: parseFloat(matches[2]),
          lines: parseFloat(matches[3]),
          overall: parseFloat(matches[0]) // 使用语句覆盖率作为整体覆盖率
        }
      }
    }
    
    return { overall: 0, lines: 0, functions: 0, branches: 0, statements: 0 }
  }

  /**
   * 提取违规数量
   */
  extractViolationCount(output) {
    const match = output.match(/(\d+)\s*个违规/)
    return match ? parseInt(match[1]) : 0
  }

  /**
   * 提取警告数量
   */
  extractWarningCount(output) {
    const match = output.match(/(\d+)\s*个警告/)
    return match ? parseInt(match[1]) : 0
  }
}

// 运行监控
if (require.main === module) {
  const monitor = new QualityMonitor()
  monitor.runFullMonitoring().catch(error => {
    console.error('质量监控过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = QualityMonitor
