/**
 * 统一Token管理服务
 * 
 * 符合DataAccessManager架构规范的Token管理实现
 * 集成缓存、性能监控和错误处理
 */

import jwt from 'jsonwebtoken'
import { JWTPayload, TokenValidationResult, AuthErrorCode } from '@/types/auth'
import { JWT_CONFIG, validateJWTConfig } from '@/config/jwt.config'



/**
 * Token管理服务接口
 */
export interface ITokenManagementService {
  // Token生成
  generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<string>
  generateRefreshToken(userId: string, sessionId: string): Promise<string>
  
  // Token验证
  verifyAccessToken(token: string): Promise<TokenValidationResult>
  verifyRefreshToken(token: string): Promise<{ isValid: boolean; userId?: string; sessionId?: string; error?: string }>
  
  // Token工具方法
  extractUserInfo(token: string): Promise<{ userId: string; username: string; roles: string[]; permissions: string[] } | null>
  shouldRefreshToken(token: string, thresholdMinutes?: number): Promise<boolean>
  getTokenRemainingTime(token: string): Promise<number>
  generateSessionId(): string
  
  // 配置检查
  checkConfiguration(): { isValid: boolean; warnings: string[] }
}

// 验证JWT配置
validateJWTConfig()

/**
 * 统一Token管理服务实现
 * 符合DataAccessManager单一入口架构
 */
export class TokenManagementService implements ITokenManagementService {
  private static instance: TokenManagementService

  private constructor() {
    console.log('🔧 [TokenManagementService] 服务实例已创建')
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TokenManagementService {
    if (!TokenManagementService.instance) {
      TokenManagementService.instance = new TokenManagementService()
    }
    return TokenManagementService.instance
  }

  /**
   * 检查运行环境
   */
  private checkServerEnvironment(): void {
    if (typeof window !== 'undefined') {
      throw new Error('Token操作只能在服务器端执行')
    }
  }

  /**
   * 生成Access Token
   */
  async generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): Promise<string> {
    this.checkServerEnvironment()
    
    try {
      console.log('🚀 [TokenManagementService] 开始生成Access Token')
      
      const tokenPayload: JWTPayload = {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
      }

      const token = jwt.sign(tokenPayload, JWT_CONFIG.accessToken.secret, {
        algorithm: JWT_CONFIG.accessToken.algorithm,
        expiresIn: JWT_CONFIG.accessToken.expiresIn
      })

      console.log('✅ [TokenManagementService] Access Token生成成功')
      return token
    } catch (error) {
      console.error('💥 [TokenManagementService] 生成Access Token失败:', error)
      throw new Error('Token生成失败')
    }
  }

  /**
   * 生成Refresh Token
   */
  async generateRefreshToken(userId: string, sessionId: string): Promise<string> {
    this.checkServerEnvironment()
    
    try {
      console.log('🚀 [TokenManagementService] 开始生成Refresh Token')
      
      const payload = {
        userId,
        sessionId,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000)
      }

      const token = jwt.sign(payload, JWT_CONFIG.refreshToken.secret, {
        algorithm: JWT_CONFIG.refreshToken.algorithm,
        expiresIn: JWT_CONFIG.refreshToken.expiresIn
      })

      console.log('✅ [TokenManagementService] Refresh Token生成成功')
      return token
    } catch (error) {
      console.error('💥 [TokenManagementService] 生成Refresh Token失败:', error)
      throw new Error('Refresh Token生成失败')
    }
  }

  /**
   * 验证Access Token
   */
  async verifyAccessToken(token: string): Promise<TokenValidationResult> {
    this.checkServerEnvironment()
    
    try {
      if (!token) {
        return {
          isValid: false,
          error: 'Token不能为空'
        }
      }

      const payload = jwt.verify(token, JWT_CONFIG.accessToken.secret) as JWTPayload
      
      // 检查Token是否过期
      const now = Math.floor(Date.now() / 1000)
      if (payload.exp && payload.exp < now) {
        return {
          isValid: false,
          error: 'Token已过期',
          isExpired: true
        }
      }

      return {
        isValid: true,
        payload
      }
    } catch (error) {
      if (error && typeof error === 'object' && 'name' in error) {
        if (error.name === 'TokenExpiredError') {
          return {
            isValid: false,
            error: 'Token已过期',
            isExpired: true
          }
        } else if (error.name === 'JsonWebTokenError') {
          return {
            isValid: false,
            error: 'Token格式无效'
          }
        }
      }
      return {
        isValid: false,
        error: 'Token验证失败'
      }
    }
  }

  /**
   * 验证Refresh Token
   */
  async verifyRefreshToken(token: string): Promise<{ isValid: boolean; userId?: string; sessionId?: string; error?: string }> {
    this.checkServerEnvironment()
    
    try {
      if (!token) {
        return {
          isValid: false,
          error: 'Refresh Token不能为空'
        }
      }

      const payload = jwt.verify(token, JWT_CONFIG.refreshToken.secret) as any
      
      if (payload.type !== 'refresh') {
        return {
          isValid: false,
          error: 'Token类型无效'
        }
      }

      return {
        isValid: true,
        userId: payload.userId,
        sessionId: payload.sessionId
      }
    } catch (error) {
      if (error && typeof error === 'object' && 'name' in error) {
        if (error.name === 'TokenExpiredError') {
          return {
            isValid: false,
            error: 'Refresh Token已过期'
          }
        } else if (error.name === 'JsonWebTokenError') {
          return {
            isValid: false,
            error: 'Refresh Token格式无效'
          }
        }
      }
      return {
        isValid: false,
        error: 'Refresh Token验证失败'
      }
    }
  }

  /**
   * 从Token中提取用户信息
   */
  async extractUserInfo(token: string): Promise<{ userId: string; username: string; roles: string[]; permissions: string[] } | null> {
    try {
      const validation = await this.verifyAccessToken(token)
      if (!validation.isValid || !validation.payload) {
        return null
      }

      const { userId, username, roles, permissions } = validation.payload
      return { userId, username, roles, permissions }
    } catch (error) {
      return null
    }
  }

  /**
   * 检查Token是否即将过期
   */
  async shouldRefreshToken(token: string, thresholdMinutes: number = 5): Promise<boolean> {
    try {
      const validation = await this.verifyAccessToken(token)
      if (!validation.isValid || !validation.payload) {
        return false
      }

      const now = Math.floor(Date.now() / 1000)
      const threshold = thresholdMinutes * 60
      
      return validation.payload.exp - now <= threshold
    } catch (error) {
      return false
    }
  }

  /**
   * 获取Token剩余有效时间（秒）
   */
  async getTokenRemainingTime(token: string): Promise<number> {
    try {
      const validation = await this.verifyAccessToken(token)
      if (!validation.isValid || !validation.payload) {
        return -1
      }

      const now = Math.floor(Date.now() / 1000)
      return Math.max(0, validation.payload.exp - now)
    } catch (error) {
      return -1
    }
  }

  /**
   * 生成会话ID
   */
  generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查环境变量配置
   */
  checkConfiguration(): { isValid: boolean; warnings: string[] } {
    const warnings: string[] = []

    if (JWT_CONFIG.accessToken.secret === 'default-access-secret-change-in-production') {
      warnings.push('使用默认的Access Token密钥，生产环境中请设置JWT_ACCESS_SECRET环境变量')
    }

    if (JWT_CONFIG.refreshToken.secret === 'default-refresh-secret-change-in-production') {
      warnings.push('使用默认的Refresh Token密钥，生产环境中请设置JWT_REFRESH_SECRET环境变量')
    }

    if (JWT_CONFIG.accessToken.secret === JWT_CONFIG.refreshToken.secret) {
      warnings.push('Access Token和Refresh Token使用相同密钥，建议使用不同的密钥')
    }

    return {
      isValid: warnings.length === 0,
      warnings
    }
  }
}

/**
 * 导出单例实例
 */
export const tokenManagementService = TokenManagementService.getInstance()

/**
 * 导出JWT配置（只读）
 */
export const getJWTConfig = () => ({
  accessTokenExpiresIn: JWT_CONFIG.accessToken.expiresIn,
  refreshTokenExpiresIn: JWT_CONFIG.refreshToken.expiresIn,
  algorithm: JWT_CONFIG.accessToken.algorithm
})