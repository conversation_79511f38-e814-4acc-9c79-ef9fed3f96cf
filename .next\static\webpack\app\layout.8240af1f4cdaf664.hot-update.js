"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/services/dataAccess/AuthDataAccessService.ts":
/*!**********************************************************!*\
  !*** ./src/services/dataAccess/AuthDataAccessService.ts ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthDataAccessService: function() { return /* binding */ AuthDataAccessService; }\n/* harmony export */ });\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types/auth */ \"(app-pages-browser)/./src/types/auth.ts\");\n/* harmony import */ var _utils_auth_PasswordManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/auth/PasswordManager */ \"(app-pages-browser)/./src/utils/auth/PasswordManager.ts\");\n/**\n * 认证数据访问服务\n * \n * 负责用户认证相关的数据访问操作\n * 遵循DataAccessManager的架构规范\n */ \n\n/**\n * 模拟数据存储\n * 在实际项目中，这些数据应该存储在数据库中\n */ class MockDataStore {\n    static getUsers() {\n        return [\n            ...this.users\n        ];\n    }\n    static getUserById(id) {\n        return this.users.find((user)=>user.id === id);\n    }\n    static getUserByUsername(username) {\n        return this.users.find((user)=>user.username === username);\n    }\n    static getUserPassword(userId) {\n        return this.userPasswords.get(userId);\n    }\n    static setUserPassword(userId, passwordHash) {\n        // 保存旧密码到历史记录\n        const currentPassword = this.userPasswords.get(userId);\n        if (currentPassword) {\n            const history = this.passwordHistory.get(userId) || [];\n            history.unshift(currentPassword) // 添加到历史记录开头\n            ;\n            // 只保留最近5次密码\n            if (history.length > 5) {\n                history.splice(5);\n            }\n            this.passwordHistory.set(userId, history);\n        }\n        this.userPasswords.set(userId, passwordHash);\n    }\n    static getPasswordHistory(userId) {\n        return this.passwordHistory.get(userId) || [];\n    }\n    static isPasswordInHistory(userId, passwordHash) {\n        const history = this.passwordHistory.get(userId) || [];\n        return history.includes(passwordHash);\n    }\n    static setForcePasswordChange(userId, force) {\n        this.forcePasswordChange.set(userId, force);\n    }\n    static shouldForcePasswordChange(userId) {\n        return this.forcePasswordChange.get(userId) || false;\n    }\n    static addUser(user) {\n        this.users.push(user);\n    }\n    static updateUser(id, updates) {\n        const index = this.users.findIndex((user)=>user.id === id);\n        if (index !== -1) {\n            this.users[index] = {\n                ...this.users[index],\n                ...updates,\n                updatedAt: new Date().toISOString()\n            };\n            return true;\n        }\n        return false;\n    }\n    static deleteUser(id) {\n        const index = this.users.findIndex((user)=>user.id === id);\n        if (index !== -1) {\n            this.users.splice(index, 1);\n            this.userPasswords.delete(id);\n            return true;\n        }\n        return false;\n    }\n    static getSessions() {\n        return [\n            ...this.sessions\n        ];\n    }\n    static updateSession(sessionId, updates) {\n        const index = this.sessions.findIndex((session)=>session.id === sessionId);\n        if (index !== -1) {\n            this.sessions[index] = {\n                ...this.sessions[index],\n                ...updates\n            };\n            return true;\n        }\n        return false;\n    }\n    static getSessionById(id) {\n        return this.sessions.find((session)=>session.id === id);\n    }\n    static getUserSessions(userId) {\n        return this.sessions.filter((session)=>session.userId === userId && session.isActive);\n    }\n    static addSession(session) {\n        this.sessions.push(session);\n    }\n    static updateSession(id, updates) {\n        const index = this.sessions.findIndex((session)=>session.id === id);\n        if (index !== -1) {\n            this.sessions[index] = {\n                ...this.sessions[index],\n                ...updates\n            };\n            return true;\n        }\n        return false;\n    }\n    static deleteSession(id) {\n        const index = this.sessions.findIndex((session)=>session.id === id);\n        if (index !== -1) {\n            this.sessions.splice(index, 1);\n            return true;\n        }\n        return false;\n    }\n    static getLoginAttempts(username) {\n        return this.loginAttempts.get(username) || {\n            count: 0,\n            lastAttempt: 0\n        };\n    }\n    static recordLoginAttempt(username, success) {\n        const now = Date.now();\n        const attempts = this.getLoginAttempts(username);\n        if (success) {\n            // 登录成功，清除失败记录\n            this.loginAttempts.delete(username);\n        } else {\n            // 登录失败，增加计数\n            const newCount = attempts.count + 1;\n            const lockedUntil = newCount >= 5 ? now + 30 * 60 * 1000 : undefined // 5次失败锁定30分钟\n            ;\n            this.loginAttempts.set(username, {\n                count: newCount,\n                lastAttempt: now,\n                lockedUntil\n            });\n        }\n    }\n    static isAccountLocked(username) {\n        const attempts = this.getLoginAttempts(username);\n        return attempts.lockedUntil ? attempts.lockedUntil > Date.now() : false;\n    }\n}\nMockDataStore.users = [\n    {\n        id: \"user-1\",\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        fullName: \"系统管理员\",\n        status: \"active\",\n        roles: [\n            {\n                id: \"role-1\",\n                code: \"admin\",\n                name: \"系统管理员\",\n                description: \"拥有系统所有权限\",\n                permissions: [\n                    {\n                        id: \"perm-1\",\n                        code: \"system:admin\",\n                        name: \"系统管理\",\n                        type: \"module\",\n                        resource: \"system\",\n                        action: \"admin\",\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    }\n                ],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            }\n        ],\n        permissions: [],\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\nMockDataStore.userPasswords = new Map([\n    [\n        \"user-1\",\n        \"$2b$12$20ynYICgWvP5peyWMeUNquDINYFT7Q0vAMmLOaoTy1x7duhiHmyYi\"\n    ] // 密码: admin123\n]);\n// 密码历史记录（用户ID -> 最近5次密码哈希）\nMockDataStore.passwordHistory = new Map();\n// 强制修改密码标记（用户ID -> 是否需要强制修改密码）\nMockDataStore.forcePasswordChange = new Map();\nMockDataStore.sessions = [];\nMockDataStore.loginAttempts = new Map();\n/**\n * 认证数据访问服务实现\n */ class AuthDataAccessService {\n    /**\n   * 获取单例实例\n   */ static getInstance() {\n        if (!AuthDataAccessService.instance) {\n            AuthDataAccessService.instance = new AuthDataAccessService();\n        }\n        return AuthDataAccessService.instance;\n    }\n    /**\n   * 用户登录\n   */ async login(credentials) {\n        try {\n            console.log(\"\\uD83D\\uDD10 [AuthDataAccessService] 开始处理登录请求:\", credentials.username);\n            const { username, password, rememberMe } = credentials;\n            // 检查账户是否被锁定\n            if (MockDataStore.isAccountLocked(username)) {\n                console.log(\"\\uD83D\\uDD12 [AuthDataAccessService] 账户被锁定:\", username);\n                MockDataStore.recordLoginAttempt(username, false);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"账户已被锁定，请30分钟后重试\",\n                    code: _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthErrorCode.ACCOUNT_LOCKED,\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 查找用户\n            console.log(\"\\uD83D\\uDC64 [AuthDataAccessService] 查找用户:\", username);\n            const user = MockDataStore.getUserByUsername(username);\n            if (!user) {\n                console.log(\"❌ [AuthDataAccessService] 用户不存在:\", username);\n                MockDataStore.recordLoginAttempt(username, false);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户名或密码错误\",\n                    code: _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthErrorCode.INVALID_CREDENTIALS,\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            console.log(\"✅ [AuthDataAccessService] 找到用户:\", user.id, user.username);\n            // 检查用户状态\n            console.log(\"\\uD83D\\uDD0D [AuthDataAccessService] 检查用户状态:\", user.status);\n            if (user.status !== \"active\") {\n                MockDataStore.recordLoginAttempt(username, false);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: user.status === \"locked\" ? \"账户已被锁定\" : \"账户未激活\",\n                    code: user.status === \"locked\" ? _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthErrorCode.ACCOUNT_LOCKED : _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthErrorCode.ACCOUNT_INACTIVE,\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证密码\n            console.log(\"\\uD83D\\uDD11 [AuthDataAccessService] 开始验证密码\");\n            const storedPasswordHash = MockDataStore.getUserPassword(user.id);\n            console.log(\"\\uD83D\\uDD0D [AuthDataAccessService] 获取到的密码哈希:\", storedPasswordHash ? \"存在\" : \"不存在\");\n            if (!storedPasswordHash || !await _utils_auth_PasswordManager__WEBPACK_IMPORTED_MODULE_1__.PasswordManager.verifyPassword(password, storedPasswordHash)) {\n                console.log(\"❌ [AuthDataAccessService] 密码验证失败\");\n                MockDataStore.recordLoginAttempt(username, false);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户名或密码错误\",\n                    code: _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthErrorCode.INVALID_CREDENTIALS,\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 登录成功，清除失败记录\n            console.log(\"✅ [AuthDataAccessService] 密码验证成功，开始生成会话\");\n            MockDataStore.recordLoginAttempt(username, true);\n            // 生成会话ID（不生成JWT token，将在API层处理）\n            const sessionId = \"session-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            // 更新用户最后登录时间\n            MockDataStore.updateUser(user.id, {\n                lastLoginAt: new Date().toISOString()\n            });\n            // 返回用户信息和会话ID，让API层生成JWT\n            const result = {\n                user,\n                sessionId,\n                rememberMe,\n                // accessToken 和 refreshToken 将在API层生成\n                expiresIn: 3600 // 1小时\n            };\n            return {\n                status: \"success\",\n                data: result,\n                message: \"登录成功\",\n                code: \"LOGIN_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            console.error(\"登录失败:\", error);\n            return {\n                status: \"error\",\n                data: null,\n                message: \"登录失败，请稍后重试\",\n                code: \"LOGIN_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    /**\n   * 用户登出\n   */ async logout(refreshToken) {\n        try {\n            // TODO: Token验证应该在API路由层处理，这里简化处理\n            if (!refreshToken) {\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"Token无效\",\n                    code: _types_auth__WEBPACK_IMPORTED_MODULE_0__.AuthErrorCode.TOKEN_INVALID,\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            console.log(\"\\uD83D\\uDD13 [AuthDataAccessService] 处理用户登出请求\");\n            return {\n                status: \"success\",\n                data: null,\n                message: \"登出成功\",\n                code: \"LOGOUT_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            console.error(\"登出失败:\", error);\n            return {\n                status: \"error\",\n                data: null,\n                message: \"登出失败\",\n                code: \"LOGOUT_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    /**\n   * 刷新Token\n   * ✅ 架构合规：使用统一Token管理服务\n   */ async refreshToken(request) {\n        try {\n            console.log(\"\\uD83D\\uDD04 [AuthDataAccessService] 开始处理Token刷新请求\");\n            const { refreshToken } = request;\n            // 在服务器端验证Refresh Token\n            const isServer = \"object\" === \"undefined\";\n            if (!isServer) {\n                console.log(\"❌ [AuthDataAccessService] Token刷新必须在服务器端进行\");\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"Token刷新必须在服务器端进行\",\n                    code: \"INVALID_ENVIRONMENT\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // ✅ 架构合规：使用统一Token管理服务\n            const { tokenManagementService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_dataAccess_TokenManagementService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./TokenManagementService */ \"(app-pages-browser)/./src/services/dataAccess/TokenManagementService.ts\"));\n            // 验证Refresh Token\n            const refreshResult = await tokenManagementService.verifyRefreshToken(refreshToken);\n            if (!refreshResult.isValid) {\n                console.log(\"❌ [AuthDataAccessService] Refresh Token验证失败:\", refreshResult.error);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: refreshResult.error || \"Refresh Token无效或已过期\",\n                    code: \"TOKEN_INVALID\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            if (!refreshResult.userId || !refreshResult.sessionId) {\n                console.log(\"❌ [AuthDataAccessService] Refresh Token payload无效\");\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"Refresh Token格式无效\",\n                    code: \"TOKEN_INVALID\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证用户是否存在\n            const user = MockDataStore.getUserById(refreshResult.userId);\n            if (!user) {\n                console.log(\"❌ [AuthDataAccessService] 用户不存在:\", refreshResult.userId);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 检查用户状态\n            if (user.status !== \"active\") {\n                console.log(\"❌ [AuthDataAccessService] 用户状态不正常:\", user.status);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户账户状态异常\",\n                    code: \"ACCOUNT_INACTIVE\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证会话是否有效\n            const session = MockDataStore.getSessionById(refreshResult.sessionId);\n            if (!session || !session.isActive) {\n                console.log(\"❌ [AuthDataAccessService] 会话无效或已过期:\", refreshResult.sessionId);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"会话已过期\",\n                    code: \"SESSION_EXPIRED\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // ✅ 架构合规：使用统一Token管理服务生成新Token\n            const newAccessToken = await tokenManagementService.generateAccessToken({\n                userId: user.id,\n                username: user.username,\n                roles: user.roles.map((role)=>role.code),\n                permissions: user.roles.flatMap((role)=>role.permissions.map((perm)=>perm.code)),\n                sessionId: session.id\n            });\n            const newRefreshToken = await tokenManagementService.generateRefreshToken(user.id, session.id);\n            // 更新会话的最后活动时间\n            MockDataStore.updateSession(session.id, {\n                lastActivity: new Date().toISOString()\n            });\n            console.log(\"✅ [AuthDataAccessService] Token刷新成功:\", user.username);\n            return {\n                status: \"success\",\n                data: {\n                    accessToken: newAccessToken,\n                    refreshToken: newRefreshToken,\n                    expiresIn: 3600 // 1小时\n                },\n                message: \"Token刷新成功\",\n                code: \"SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [AuthDataAccessService] Token刷新异常:\", error);\n            return {\n                status: \"error\",\n                data: null,\n                message: \"Token刷新失败\",\n                code: \"TOKEN_REFRESH_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    /**\n   * 验证Token并获取用户信息\n   */ async validateToken(token) {\n        try {\n            console.log(\"\\uD83D\\uDD0D [AuthDataAccessService] 开始验证Token\");\n            // 在服务器端验证JWT Token\n            const isServer = \"object\" === \"undefined\";\n            if (!isServer) {\n                console.log(\"❌ [AuthDataAccessService] Token验证必须在服务器端进行\");\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"Token验证必须在服务器端进行\",\n                    code: \"INVALID_ENVIRONMENT\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 使用TokenManagementService验证Token\n            const tokenManagement = new (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_dataAccess_TokenManagementService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./TokenManagementService */ \"(app-pages-browser)/./src/services/dataAccess/TokenManagementService.ts\"))).TokenManagementService();\n            // 验证Token并解析payload\n            const tokenValidation = await tokenManagement.verifyAccessToken(token);\n            if (!tokenValidation.isValid) {\n                console.log(\"❌ [AuthDataAccessService] Token验证失败:\", tokenValidation.error);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: tokenValidation.error || \"Token无效或已过期\",\n                    code: \"TOKEN_INVALID\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 从验证结果获取用户信息\n            const payload = tokenValidation.payload;\n            if (!payload || !payload.userId) {\n                console.log(\"❌ [AuthDataAccessService] Token payload无效\");\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"Token格式无效\",\n                    code: \"TOKEN_INVALID\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 根据Token中的userId获取用户信息\n            const user = MockDataStore.getUserById(payload.userId);\n            if (!user) {\n                console.log(\"❌ [AuthDataAccessService] Token中的用户不存在:\", payload.userId);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 检查用户状态\n            if (user.status !== \"active\") {\n                console.log(\"❌ [AuthDataAccessService] 用户状态不正常:\", user.status);\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户账户状态异常\",\n                    code: \"ACCOUNT_INACTIVE\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证会话是否有效（如果Token包含sessionId）\n            if (payload.sessionId) {\n                const session = MockDataStore.getSessionById(payload.sessionId);\n                if (!session || !session.isActive) {\n                    console.log(\"❌ [AuthDataAccessService] 会话无效或已过期:\", payload.sessionId);\n                    return {\n                        status: \"error\",\n                        data: null,\n                        message: \"会话已过期\",\n                        code: \"SESSION_EXPIRED\",\n                        timestamp: new Date().toISOString(),\n                        requestId: this.generateRequestId()\n                    };\n                }\n            }\n            console.log(\"✅ [AuthDataAccessService] Token验证成功:\", user.username);\n            return {\n                status: \"success\",\n                data: user,\n                message: \"Token验证成功\",\n                code: \"SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [AuthDataAccessService] Token验证异常:\", error);\n            return {\n                status: \"error\",\n                data: null,\n                message: \"Token验证失败\",\n                code: \"TOKEN_VALIDATION_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 其他方法的实现将在下一个文件中继续...\n    /**\n   * 生成请求ID\n   */ generateRequestId() {\n        return \"req-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    // 占位方法，将在扩展文件中实现\n    async getUserById(id) {\n        const user = MockDataStore.getUserById(id);\n        if (!user) {\n            return {\n                status: \"error\",\n                data: null,\n                message: \"用户不存在\",\n                code: \"USER_NOT_FOUND\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n        return {\n            status: \"success\",\n            data: user,\n            message: \"获取用户信息成功\",\n            code: \"GET_USER_SUCCESS\",\n            timestamp: new Date().toISOString(),\n            requestId: this.generateRequestId()\n        };\n    }\n    async getUserByUsername(username) {\n        const user = MockDataStore.getUserByUsername(username);\n        if (!user) {\n            return {\n                status: \"error\",\n                data: null,\n                message: \"用户不存在\",\n                code: \"USER_NOT_FOUND\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n        return {\n            status: \"success\",\n            data: user,\n            message: \"获取用户信息成功\",\n            code: \"GET_USER_SUCCESS\",\n            timestamp: new Date().toISOString(),\n            requestId: this.generateRequestId()\n        };\n    }\n    // 用户管理方法实现\n    async createUser(userData) {\n        try {\n            // 验证用户名是否已存在\n            const existingUser = MockDataStore.getUserByUsername(userData.username);\n            if (existingUser) {\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户名已存在\",\n                    code: \"USERNAME_EXISTS\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证邮箱是否已存在（如果提供）\n            if (userData.email) {\n                const existingEmailUser = MockDataStore.getUsers().find((u)=>u.email === userData.email);\n                if (existingEmailUser) {\n                    return {\n                        status: \"error\",\n                        data: null,\n                        message: \"邮箱已存在\",\n                        code: \"EMAIL_EXISTS\",\n                        timestamp: new Date().toISOString(),\n                        requestId: this.generateRequestId()\n                    };\n                }\n            }\n            // 生成用户ID\n            const userId = \"user-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            // 加密密码\n            const { hash: passwordHash } = await _utils_auth_PasswordManager__WEBPACK_IMPORTED_MODULE_1__.PasswordManager.hashPassword(userData.password);\n            // 获取角色信息（简化实现，实际应从数据库获取）\n            const roles = userData.roleIds.map((roleId)=>({\n                    id: roleId,\n                    code: roleId === \"admin\" ? \"admin\" : \"user\",\n                    name: roleId === \"admin\" ? \"管理员\" : \"普通用户\",\n                    description: roleId === \"admin\" ? \"系统管理员角色\" : \"普通用户角色\",\n                    permissions: roleId === \"admin\" ? [\n                        {\n                            id: \"perm-admin\",\n                            code: \"system:admin\",\n                            name: \"系统管理\",\n                            type: \"module\",\n                            resource: \"system\",\n                            action: \"admin\",\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        }\n                    ] : [],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }));\n            // 创建用户对象\n            const newUser = {\n                id: userId,\n                username: userData.username,\n                email: userData.email,\n                fullName: userData.fullName,\n                phone: userData.phone,\n                departmentId: userData.departmentId,\n                employeeId: userData.employeeId,\n                status: \"active\",\n                roles,\n                permissions: [],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // 保存用户和密码\n            MockDataStore.addUser(newUser);\n            MockDataStore.setUserPassword(userId, passwordHash);\n            return {\n                status: \"success\",\n                data: newUser,\n                message: \"用户创建成功\",\n                code: \"CREATE_USER_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: null,\n                message: error instanceof Error ? error.message : \"创建用户失败\",\n                code: \"CREATE_USER_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    async updateUser(id, updates) {\n        try {\n            const existingUser = MockDataStore.getUserById(id);\n            if (!existingUser) {\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证邮箱唯一性（如果更新邮箱）\n            if (updates.email && updates.email !== existingUser.email) {\n                const existingEmailUser = MockDataStore.getUsers().find((u)=>u.email === updates.email && u.id !== id);\n                if (existingEmailUser) {\n                    return {\n                        status: \"error\",\n                        data: null,\n                        message: \"邮箱已存在\",\n                        code: \"EMAIL_EXISTS\",\n                        timestamp: new Date().toISOString(),\n                        requestId: this.generateRequestId()\n                    };\n                }\n            }\n            // 处理角色更新\n            let updatedRoles = existingUser.roles;\n            if (updates.roleIds) {\n                updatedRoles = updates.roleIds.map((roleId)=>({\n                        id: roleId,\n                        code: roleId === \"admin\" ? \"admin\" : \"user\",\n                        name: roleId === \"admin\" ? \"管理员\" : \"普通用户\",\n                        description: roleId === \"admin\" ? \"系统管理员角色\" : \"普通用户角色\",\n                        permissions: roleId === \"admin\" ? [\n                            {\n                                id: \"perm-admin\",\n                                code: \"system:admin\",\n                                name: \"系统管理\",\n                                type: \"module\",\n                                resource: \"system\",\n                                action: \"admin\",\n                                createdAt: new Date().toISOString(),\n                                updatedAt: new Date().toISOString()\n                            }\n                        ] : [],\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    }));\n            }\n            // 更新用户信息\n            const updateData = {\n                ...updates,\n                roles: updatedRoles,\n                updatedAt: new Date().toISOString()\n            };\n            const success = MockDataStore.updateUser(id, updateData);\n            if (!success) {\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"更新用户失败\",\n                    code: \"UPDATE_USER_ERROR\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 如果用户状态变更为禁用或锁定，终止该用户的所有会话\n            if (updates.status && (updates.status === \"inactive\" || updates.status === \"locked\")) {\n                await this.terminateUserSessions(id);\n            }\n            const updatedUser = MockDataStore.getUserById(id);\n            return {\n                status: \"success\",\n                data: updatedUser,\n                message: \"用户更新成功\",\n                code: \"UPDATE_USER_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: null,\n                message: error instanceof Error ? error.message : \"更新用户失败\",\n                code: \"UPDATE_USER_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    async deleteUser(id) {\n        try {\n            const existingUser = MockDataStore.getUserById(id);\n            if (!existingUser) {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 检查是否为系统管理员（不允许删除）\n            if (existingUser.username === \"admin\") {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"不能删除系统管理员账户\",\n                    code: \"CANNOT_DELETE_ADMIN\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            const success = MockDataStore.deleteUser(id);\n            return {\n                status: \"success\",\n                data: success,\n                message: success ? \"用户删除成功\" : \"用户删除失败\",\n                code: success ? \"DELETE_USER_SUCCESS\" : \"DELETE_USER_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: false,\n                message: error instanceof Error ? error.message : \"删除用户失败\",\n                code: \"DELETE_USER_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    async createSession(userId, sessionData) {\n        // TODO: 实现会话创建逻辑\n        throw new Error(\"Method not implemented.\");\n    }\n    async getSession(sessionId) {\n        // TODO: 实现会话获取逻辑\n        throw new Error(\"Method not implemented.\");\n    }\n    async updateSession(sessionId, updates) {\n        // TODO: 实现会话更新逻辑\n        throw new Error(\"Method not implemented.\");\n    }\n    async deleteSession(sessionId) {\n        // TODO: 实现会话删除逻辑\n        throw new Error(\"Method not implemented.\");\n    }\n    async getUserSessions(userId) {\n        // TODO: 实现用户会话获取逻辑\n        throw new Error(\"Method not implemented.\");\n    }\n    async changePassword(userId, currentPassword, newPassword) {\n        // TODO: 实现密码修改逻辑\n        throw new Error(\"Method not implemented.\");\n    }\n    async resetPassword(userId, newPassword) {\n        try {\n            const user = MockDataStore.getUserById(userId);\n            if (!user) {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 验证密码强度\n            const validation = _utils_auth_PasswordManager__WEBPACK_IMPORTED_MODULE_1__.PasswordManager.validatePassword(newPassword);\n            if (!validation.isValid) {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"密码不符合要求: \".concat(validation.feedback.join(\", \")),\n                    code: \"INVALID_PASSWORD\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 加密新密码\n            const { hash: passwordHash } = await _utils_auth_PasswordManager__WEBPACK_IMPORTED_MODULE_1__.PasswordManager.hashPassword(newPassword);\n            // 检查密码是否与历史密码重复\n            if (MockDataStore.isPasswordInHistory(userId, passwordHash)) {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"新密码不能与最近5次使用的密码相同\",\n                    code: \"PASSWORD_REUSED\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 更新密码\n            MockDataStore.setUserPassword(userId, passwordHash);\n            return {\n                status: \"success\",\n                data: true,\n                message: \"密码重置成功\",\n                code: \"RESET_PASSWORD_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: false,\n                message: error instanceof Error ? error.message : \"密码重置失败\",\n                code: \"RESET_PASSWORD_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 新增用户列表获取方法\n    async getUsersList(params) {\n        try {\n            let users = MockDataStore.getUsers();\n            // 搜索过滤\n            if (params === null || params === void 0 ? void 0 : params.search) {\n                const searchTerm = params.search.toLowerCase();\n                users = users.filter((user)=>user.username.toLowerCase().includes(searchTerm) || user.fullName.toLowerCase().includes(searchTerm) || user.email && user.email.toLowerCase().includes(searchTerm));\n            }\n            // 状态过滤\n            if (params === null || params === void 0 ? void 0 : params.status) {\n                users = users.filter((user)=>user.status === params.status);\n            }\n            const total = users.length;\n            // 分页\n            if ((params === null || params === void 0 ? void 0 : params.page) && (params === null || params === void 0 ? void 0 : params.pageSize)) {\n                const start = (params.page - 1) * params.pageSize;\n                const end = start + params.pageSize;\n                users = users.slice(start, end);\n            }\n            return {\n                status: \"success\",\n                data: {\n                    users,\n                    total\n                },\n                message: \"获取用户列表成功\",\n                code: \"GET_USERS_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: null,\n                message: error instanceof Error ? error.message : \"获取用户列表失败\",\n                code: \"GET_USERS_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 新增用户角色分配方法\n    async assignUserRoles(userId, roleIds) {\n        try {\n            const user = MockDataStore.getUserById(userId);\n            if (!user) {\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            // 构建角色对象（简化实现）\n            const roles = roleIds.map((roleId)=>({\n                    id: roleId,\n                    code: roleId === \"admin\" ? \"admin\" : \"user\",\n                    name: roleId === \"admin\" ? \"管理员\" : \"普通用户\",\n                    description: roleId === \"admin\" ? \"系统管理员角色\" : \"普通用户角色\",\n                    permissions: roleId === \"admin\" ? [\n                        {\n                            id: \"perm-admin\",\n                            code: \"system:admin\",\n                            name: \"系统管理\",\n                            type: \"module\",\n                            resource: \"system\",\n                            action: \"admin\",\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        }\n                    ] : [],\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }));\n            // 更新用户角色\n            const success = MockDataStore.updateUser(userId, {\n                roles,\n                updatedAt: new Date().toISOString()\n            });\n            if (!success) {\n                return {\n                    status: \"error\",\n                    data: null,\n                    message: \"角色分配失败\",\n                    code: \"ASSIGN_ROLES_ERROR\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            const updatedUser = MockDataStore.getUserById(userId);\n            return {\n                status: \"success\",\n                data: updatedUser,\n                message: \"角色分配成功\",\n                code: \"ASSIGN_ROLES_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: null,\n                message: error instanceof Error ? error.message : \"角色分配失败\",\n                code: \"ASSIGN_ROLES_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 终止用户所有会话的私有方法\n    async terminateUserSessions(userId) {\n        try {\n            // 获取用户的所有活跃会话\n            const userSessions = MockDataStore.getSessions().filter((session)=>session.userId === userId && session.isActive);\n            // 将所有会话标记为非活跃\n            for (const session of userSessions){\n                MockDataStore.updateSession(session.id, {\n                    isActive: false,\n                    lastActivityAt: new Date().toISOString()\n                });\n            }\n            console.log(\"已终止用户 \".concat(userId, \" 的 \").concat(userSessions.length, \" 个会话\"));\n        } catch (error) {\n            console.error(\"终止用户会话失败:\", error);\n        }\n    }\n    // 批量更新用户状态的方法\n    async batchUpdateUserStatus(userIds, status) {\n        try {\n            let successCount = 0;\n            let failureCount = 0;\n            const errors = [];\n            for (const userId of userIds){\n                try {\n                    const user = MockDataStore.getUserById(userId);\n                    if (!user) {\n                        failureCount++;\n                        errors.push(\"用户 \".concat(userId, \" 不存在\"));\n                        continue;\n                    }\n                    // 检查是否为系统管理员（不允许禁用）\n                    if (user.username === \"admin\" && status !== \"active\") {\n                        failureCount++;\n                        errors.push(\"不能禁用系统管理员账户\");\n                        continue;\n                    }\n                    const success = MockDataStore.updateUser(userId, {\n                        status,\n                        updatedAt: new Date().toISOString()\n                    });\n                    if (success) {\n                        // 如果状态变更为禁用或锁定，终止该用户的所有会话\n                        if (status === \"inactive\" || status === \"locked\") {\n                            await this.terminateUserSessions(userId);\n                        }\n                        successCount++;\n                    } else {\n                        failureCount++;\n                        errors.push(\"更新用户 \".concat(userId, \" 状态失败\"));\n                    }\n                } catch (error) {\n                    failureCount++;\n                    errors.push(\"处理用户 \".concat(userId, \" 时发生错误: \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                }\n            }\n            return {\n                status: \"success\",\n                data: {\n                    successCount,\n                    failureCount\n                },\n                message: \"批量更新完成：成功 \".concat(successCount, \" 个，失败 \").concat(failureCount, \" 个\").concat(errors.length > 0 ? \"。错误：\".concat(errors.join(\"; \")) : \"\"),\n                code: \"BATCH_UPDATE_STATUS_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: null,\n                message: error instanceof Error ? error.message : \"批量更新用户状态失败\",\n                code: \"BATCH_UPDATE_STATUS_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 强制修改密码\n    async forcePasswordChange(userId) {\n        let force = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        try {\n            const user = MockDataStore.getUserById(userId);\n            if (!user) {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            MockDataStore.setForcePasswordChange(userId, force);\n            return {\n                status: \"success\",\n                data: true,\n                message: force ? \"已设置强制修改密码\" : \"已取消强制修改密码\",\n                code: \"FORCE_PASSWORD_CHANGE_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: false,\n                message: error instanceof Error ? error.message : \"设置强制修改密码失败\",\n                code: \"FORCE_PASSWORD_CHANGE_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 生成安全密码\n    async generateSecurePassword() {\n        let length = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 12;\n        try {\n            const password = _utils_auth_PasswordManager__WEBPACK_IMPORTED_MODULE_1__.PasswordManager.generateRandomPassword(length, true);\n            return {\n                status: \"success\",\n                data: password,\n                message: \"安全密码生成成功\",\n                code: \"GENERATE_PASSWORD_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: null,\n                message: error instanceof Error ? error.message : \"生成密码失败\",\n                code: \"GENERATE_PASSWORD_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    // 检查用户是否需要强制修改密码\n    async checkForcePasswordChange(userId) {\n        try {\n            const user = MockDataStore.getUserById(userId);\n            if (!user) {\n                return {\n                    status: \"error\",\n                    data: false,\n                    message: \"用户不存在\",\n                    code: \"USER_NOT_FOUND\",\n                    timestamp: new Date().toISOString(),\n                    requestId: this.generateRequestId()\n                };\n            }\n            const shouldForce = MockDataStore.shouldForcePasswordChange(userId);\n            return {\n                status: \"success\",\n                data: shouldForce,\n                message: shouldForce ? \"需要强制修改密码\" : \"无需强制修改密码\",\n                code: \"CHECK_FORCE_PASSWORD_SUCCESS\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        } catch (error) {\n            return {\n                status: \"error\",\n                data: false,\n                message: error instanceof Error ? error.message : \"检查强制修改密码状态失败\",\n                code: \"CHECK_FORCE_PASSWORD_ERROR\",\n                timestamp: new Date().toISOString(),\n                requestId: this.generateRequestId()\n            };\n        }\n    }\n    constructor(){\n        console.log(\"\\uD83D\\uDD27 [AuthDataAccessService] 服务实例已创建\");\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9kYXRhQWNjZXNzL0F1dGhEYXRhQWNjZXNzU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQWlCb0I7QUFDeUM7QUEwQzlEOzs7Q0FHQyxHQUNELE1BQU1FO0lBaURKLE9BQU9DLFdBQW1CO1FBQ3hCLE9BQU87ZUFBSSxJQUFJLENBQUNDLEtBQUs7U0FBQztJQUN4QjtJQUVBLE9BQU9DLFlBQVlDLEVBQVUsRUFBb0I7UUFDL0MsT0FBTyxJQUFJLENBQUNGLEtBQUssQ0FBQ0csSUFBSSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLRixFQUFFLEtBQUtBO0lBQzdDO0lBRUEsT0FBT0csa0JBQWtCQyxRQUFnQixFQUFvQjtRQUMzRCxPQUFPLElBQUksQ0FBQ04sS0FBSyxDQUFDRyxJQUFJLENBQUNDLENBQUFBLE9BQVFBLEtBQUtFLFFBQVEsS0FBS0E7SUFDbkQ7SUFFQSxPQUFPQyxnQkFBZ0JDLE1BQWMsRUFBc0I7UUFDekQsT0FBTyxJQUFJLENBQUNDLGFBQWEsQ0FBQ0MsR0FBRyxDQUFDRjtJQUNoQztJQUVBLE9BQU9HLGdCQUFnQkgsTUFBYyxFQUFFSSxZQUFvQixFQUFRO1FBQ2pFLGFBQWE7UUFDYixNQUFNQyxrQkFBa0IsSUFBSSxDQUFDSixhQUFhLENBQUNDLEdBQUcsQ0FBQ0Y7UUFDL0MsSUFBSUssaUJBQWlCO1lBQ25CLE1BQU1DLFVBQVUsSUFBSSxDQUFDQyxlQUFlLENBQUNMLEdBQUcsQ0FBQ0YsV0FBVyxFQUFFO1lBQ3RETSxRQUFRRSxPQUFPLENBQUNILGlCQUFpQixZQUFZOztZQUU3QyxZQUFZO1lBQ1osSUFBSUMsUUFBUUcsTUFBTSxHQUFHLEdBQUc7Z0JBQ3RCSCxRQUFRSSxNQUFNLENBQUM7WUFDakI7WUFFQSxJQUFJLENBQUNILGVBQWUsQ0FBQ0ksR0FBRyxDQUFDWCxRQUFRTTtRQUNuQztRQUVBLElBQUksQ0FBQ0wsYUFBYSxDQUFDVSxHQUFHLENBQUNYLFFBQVFJO0lBQ2pDO0lBRUEsT0FBT1EsbUJBQW1CWixNQUFjLEVBQVk7UUFDbEQsT0FBTyxJQUFJLENBQUNPLGVBQWUsQ0FBQ0wsR0FBRyxDQUFDRixXQUFXLEVBQUU7SUFDL0M7SUFFQSxPQUFPYSxvQkFBb0JiLE1BQWMsRUFBRUksWUFBb0IsRUFBVztRQUN4RSxNQUFNRSxVQUFVLElBQUksQ0FBQ0MsZUFBZSxDQUFDTCxHQUFHLENBQUNGLFdBQVcsRUFBRTtRQUN0RCxPQUFPTSxRQUFRUSxRQUFRLENBQUNWO0lBQzFCO0lBRUEsT0FBT1csdUJBQXVCZixNQUFjLEVBQUVnQixLQUFjLEVBQVE7UUFDbEUsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQ04sR0FBRyxDQUFDWCxRQUFRZ0I7SUFDdkM7SUFFQSxPQUFPRSwwQkFBMEJsQixNQUFjLEVBQVc7UUFDeEQsT0FBTyxJQUFJLENBQUNpQixtQkFBbUIsQ0FBQ2YsR0FBRyxDQUFDRixXQUFXO0lBQ2pEO0lBRUEsT0FBT21CLFFBQVF2QixJQUFVLEVBQVE7UUFDL0IsSUFBSSxDQUFDSixLQUFLLENBQUM0QixJQUFJLENBQUN4QjtJQUNsQjtJQUVBLE9BQU95QixXQUFXM0IsRUFBVSxFQUFFNEIsT0FBc0IsRUFBVztRQUM3RCxNQUFNQyxRQUFRLElBQUksQ0FBQy9CLEtBQUssQ0FBQ2dDLFNBQVMsQ0FBQzVCLENBQUFBLE9BQVFBLEtBQUtGLEVBQUUsS0FBS0E7UUFDdkQsSUFBSTZCLFVBQVUsQ0FBQyxHQUFHO1lBQ2hCLElBQUksQ0FBQy9CLEtBQUssQ0FBQytCLE1BQU0sR0FBRztnQkFBRSxHQUFHLElBQUksQ0FBQy9CLEtBQUssQ0FBQytCLE1BQU07Z0JBQUUsR0FBR0QsT0FBTztnQkFBRUcsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQUc7WUFDNUYsT0FBTztRQUNUO1FBQ0EsT0FBTztJQUNUO0lBRUEsT0FBT0MsV0FBV2xDLEVBQVUsRUFBVztRQUNyQyxNQUFNNkIsUUFBUSxJQUFJLENBQUMvQixLQUFLLENBQUNnQyxTQUFTLENBQUM1QixDQUFBQSxPQUFRQSxLQUFLRixFQUFFLEtBQUtBO1FBQ3ZELElBQUk2QixVQUFVLENBQUMsR0FBRztZQUNoQixJQUFJLENBQUMvQixLQUFLLENBQUNrQixNQUFNLENBQUNhLE9BQU87WUFDekIsSUFBSSxDQUFDdEIsYUFBYSxDQUFDNEIsTUFBTSxDQUFDbkM7WUFDMUIsT0FBTztRQUNUO1FBQ0EsT0FBTztJQUNUO0lBRUEsT0FBT29DLGNBQTZCO1FBQ2xDLE9BQU87ZUFBSSxJQUFJLENBQUNDLFFBQVE7U0FBQztJQUMzQjtJQUVBLE9BQU9DLGNBQWNDLFNBQWlCLEVBQUVYLE9BQTZCLEVBQVc7UUFDOUUsTUFBTUMsUUFBUSxJQUFJLENBQUNRLFFBQVEsQ0FBQ1AsU0FBUyxDQUFDVSxDQUFBQSxVQUFXQSxRQUFReEMsRUFBRSxLQUFLdUM7UUFDaEUsSUFBSVYsVUFBVSxDQUFDLEdBQUc7WUFDaEIsSUFBSSxDQUFDUSxRQUFRLENBQUNSLE1BQU0sR0FBRztnQkFBRSxHQUFHLElBQUksQ0FBQ1EsUUFBUSxDQUFDUixNQUFNO2dCQUFFLEdBQUdELE9BQU87WUFBQztZQUM3RCxPQUFPO1FBQ1Q7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxPQUFPYSxlQUFlekMsRUFBVSxFQUEyQjtRQUN6RCxPQUFPLElBQUksQ0FBQ3FDLFFBQVEsQ0FBQ3BDLElBQUksQ0FBQ3VDLENBQUFBLFVBQVdBLFFBQVF4QyxFQUFFLEtBQUtBO0lBQ3REO0lBRUEsT0FBTzBDLGdCQUFnQnBDLE1BQWMsRUFBaUI7UUFDcEQsT0FBTyxJQUFJLENBQUMrQixRQUFRLENBQUNNLE1BQU0sQ0FBQ0gsQ0FBQUEsVUFBV0EsUUFBUWxDLE1BQU0sS0FBS0EsVUFBVWtDLFFBQVFJLFFBQVE7SUFDdEY7SUFFQSxPQUFPQyxXQUFXTCxPQUFvQixFQUFRO1FBQzVDLElBQUksQ0FBQ0gsUUFBUSxDQUFDWCxJQUFJLENBQUNjO0lBQ3JCO0lBRUEsT0FBT0YsY0FBY3RDLEVBQVUsRUFBRTRCLE9BQTZCLEVBQVc7UUFDdkUsTUFBTUMsUUFBUSxJQUFJLENBQUNRLFFBQVEsQ0FBQ1AsU0FBUyxDQUFDVSxDQUFBQSxVQUFXQSxRQUFReEMsRUFBRSxLQUFLQTtRQUNoRSxJQUFJNkIsVUFBVSxDQUFDLEdBQUc7WUFDaEIsSUFBSSxDQUFDUSxRQUFRLENBQUNSLE1BQU0sR0FBRztnQkFBRSxHQUFHLElBQUksQ0FBQ1EsUUFBUSxDQUFDUixNQUFNO2dCQUFFLEdBQUdELE9BQU87WUFBQztZQUM3RCxPQUFPO1FBQ1Q7UUFDQSxPQUFPO0lBQ1Q7SUFFQSxPQUFPa0IsY0FBYzlDLEVBQVUsRUFBVztRQUN4QyxNQUFNNkIsUUFBUSxJQUFJLENBQUNRLFFBQVEsQ0FBQ1AsU0FBUyxDQUFDVSxDQUFBQSxVQUFXQSxRQUFReEMsRUFBRSxLQUFLQTtRQUNoRSxJQUFJNkIsVUFBVSxDQUFDLEdBQUc7WUFDaEIsSUFBSSxDQUFDUSxRQUFRLENBQUNyQixNQUFNLENBQUNhLE9BQU87WUFDNUIsT0FBTztRQUNUO1FBQ0EsT0FBTztJQUNUO0lBRUEsT0FBT2tCLGlCQUFpQjNDLFFBQWdCLEVBQUU7UUFDeEMsT0FBTyxJQUFJLENBQUM0QyxhQUFhLENBQUN4QyxHQUFHLENBQUNKLGFBQWE7WUFBRTZDLE9BQU87WUFBR0MsYUFBYTtRQUFFO0lBQ3hFO0lBRUEsT0FBT0MsbUJBQW1CL0MsUUFBZ0IsRUFBRWdELE9BQWdCLEVBQVE7UUFDbEUsTUFBTUMsTUFBTXJCLEtBQUtxQixHQUFHO1FBQ3BCLE1BQU1DLFdBQVcsSUFBSSxDQUFDUCxnQkFBZ0IsQ0FBQzNDO1FBRXZDLElBQUlnRCxTQUFTO1lBQ1gsY0FBYztZQUNkLElBQUksQ0FBQ0osYUFBYSxDQUFDYixNQUFNLENBQUMvQjtRQUM1QixPQUFPO1lBQ0wsWUFBWTtZQUNaLE1BQU1tRCxXQUFXRCxTQUFTTCxLQUFLLEdBQUc7WUFDbEMsTUFBTU8sY0FBY0QsWUFBWSxJQUFJRixNQUFPLEtBQUssS0FBSyxPQUFRSSxVQUFVLGFBQWE7O1lBRXBGLElBQUksQ0FBQ1QsYUFBYSxDQUFDL0IsR0FBRyxDQUFDYixVQUFVO2dCQUMvQjZDLE9BQU9NO2dCQUNQTCxhQUFhRztnQkFDYkc7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxPQUFPRSxnQkFBZ0J0RCxRQUFnQixFQUFXO1FBQ2hELE1BQU1rRCxXQUFXLElBQUksQ0FBQ1AsZ0JBQWdCLENBQUMzQztRQUN2QyxPQUFPa0QsU0FBU0UsV0FBVyxHQUFHRixTQUFTRSxXQUFXLEdBQUd4QixLQUFLcUIsR0FBRyxLQUFLO0lBQ3BFO0FBQ0Y7QUFsTU16RCxjQUNXRSxRQUFnQjtJQUM3QjtRQUNFRSxJQUFJO1FBQ0pJLFVBQVU7UUFDVnVELE9BQU87UUFDUEMsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLE9BQU87WUFDTDtnQkFDRTlELElBQUk7Z0JBQ0orRCxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxhQUFhO2dCQUNiQyxhQUFhO29CQUNYO3dCQUNFbEUsSUFBSTt3QkFDSitELE1BQU07d0JBQ05DLE1BQU07d0JBQ05HLE1BQU07d0JBQ05DLFVBQVU7d0JBQ1ZDLFFBQVE7d0JBQ1JDLFdBQVcsSUFBSXRDLE9BQU9DLFdBQVc7d0JBQ2pDRixXQUFXLElBQUlDLE9BQU9DLFdBQVc7b0JBQ25DO2lCQUNEO2dCQUNEcUMsV0FBVyxJQUFJdEMsT0FBT0MsV0FBVztnQkFDakNGLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNuQztTQUNEO1FBQ0RpQyxhQUFhLEVBQUU7UUFDZkksV0FBVyxJQUFJdEMsT0FBT0MsV0FBVztRQUNqQ0YsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO0lBQ25DO0NBQ0Q7QUFsQ0dyQyxjQW9DV1csZ0JBQXFDLElBQUlnRSxJQUFJO0lBQzFEO1FBQUM7UUFBVTtLQUErRCxDQUFDLGVBQWU7Q0FDM0Y7QUFFRCwyQkFBMkI7QUF4Q3ZCM0UsY0F5Q1dpQixrQkFBeUMsSUFBSTBEO0FBRTVELCtCQUErQjtBQTNDM0IzRSxjQTRDVzJCLHNCQUE0QyxJQUFJZ0Q7QUE1QzNEM0UsY0E4Q1d5QyxXQUEwQixFQUFFO0FBOUN2Q3pDLGNBK0NXb0QsZ0JBQTJGLElBQUl1QjtBQXFKaEg7O0NBRUMsR0FDTSxNQUFNQztJQU9YOztHQUVDLEdBQ0QsT0FBY0MsY0FBcUM7UUFDakQsSUFBSSxDQUFDRCxzQkFBc0JFLFFBQVEsRUFBRTtZQUNuQ0Ysc0JBQXNCRSxRQUFRLEdBQUcsSUFBSUY7UUFDdkM7UUFDQSxPQUFPQSxzQkFBc0JFLFFBQVE7SUFDdkM7SUFFQTs7R0FFQyxHQUNELE1BQU1DLE1BQU1DLFdBQXlCLEVBQXFDO1FBQ3hFLElBQUk7WUFDRkMsUUFBUUMsR0FBRyxDQUFDLGtEQUF3Q0YsWUFBWXhFLFFBQVE7WUFDeEUsTUFBTSxFQUFFQSxRQUFRLEVBQUUyRSxRQUFRLEVBQUVDLFVBQVUsRUFBRSxHQUFHSjtZQUUzQyxZQUFZO1lBQ1osSUFBSWhGLGNBQWM4RCxlQUFlLENBQUN0RCxXQUFXO2dCQUMzQ3lFLFFBQVFDLEdBQUcsQ0FBQywrQ0FBcUMxRTtnQkFDakRSLGNBQWN1RCxrQkFBa0IsQ0FBQy9DLFVBQVU7Z0JBQzNDLE9BQU87b0JBQ0x5RCxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU1yRSxzREFBYUEsQ0FBQ3lGLGNBQWM7b0JBQ2xDQyxXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQSxPQUFPO1lBQ1BULFFBQVFDLEdBQUcsQ0FBQyw4Q0FBb0MxRTtZQUNoRCxNQUFNRixPQUFPTixjQUFjTyxpQkFBaUIsQ0FBQ0M7WUFDN0MsSUFBSSxDQUFDRixNQUFNO2dCQUNUMkUsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQzFFO2dCQUNoRFIsY0FBY3VELGtCQUFrQixDQUFDL0MsVUFBVTtnQkFDM0MsT0FBTztvQkFDTHlELFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTXJFLHNEQUFhQSxDQUFDNkYsbUJBQW1CO29CQUN2Q0gsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUFULFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUM1RSxLQUFLRixFQUFFLEVBQUVFLEtBQUtFLFFBQVE7WUFFckUsU0FBUztZQUNUeUUsUUFBUUMsR0FBRyxDQUFDLGdEQUFzQzVFLEtBQUsyRCxNQUFNO1lBQzdELElBQUkzRCxLQUFLMkQsTUFBTSxLQUFLLFVBQVU7Z0JBQzVCakUsY0FBY3VELGtCQUFrQixDQUFDL0MsVUFBVTtnQkFDM0MsT0FBTztvQkFDTHlELFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTaEYsS0FBSzJELE1BQU0sS0FBSyxXQUFXLFdBQVc7b0JBQy9DRSxNQUFNN0QsS0FBSzJELE1BQU0sS0FBSyxXQUFXbkUsc0RBQWFBLENBQUN5RixjQUFjLEdBQUd6RixzREFBYUEsQ0FBQzhGLGdCQUFnQjtvQkFDOUZKLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLE9BQU87WUFDUFQsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTVcscUJBQXFCN0YsY0FBY1MsZUFBZSxDQUFDSCxLQUFLRixFQUFFO1lBQ2hFNkUsUUFBUUMsR0FBRyxDQUFDLGtEQUF3Q1cscUJBQXFCLE9BQU87WUFDaEYsSUFBSSxDQUFDQSxzQkFBc0IsQ0FBQyxNQUFNOUYsd0VBQWVBLENBQUMrRixjQUFjLENBQUNYLFVBQVVVLHFCQUFxQjtnQkFDOUZaLFFBQVFDLEdBQUcsQ0FBQztnQkFDWmxGLGNBQWN1RCxrQkFBa0IsQ0FBQy9DLFVBQVU7Z0JBQzNDLE9BQU87b0JBQ0x5RCxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU1yRSxzREFBYUEsQ0FBQzZGLG1CQUFtQjtvQkFDdkNILFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLGNBQWM7WUFDZFQsUUFBUUMsR0FBRyxDQUFDO1lBQ1psRixjQUFjdUQsa0JBQWtCLENBQUMvQyxVQUFVO1lBRTNDLGdDQUFnQztZQUNoQyxNQUFNbUMsWUFBWSxXQUF5Qm9ELE9BQWQzRCxLQUFLcUIsR0FBRyxJQUFHLEtBQTJDLE9BQXhDc0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7WUFFaEYsYUFBYTtZQUNibEcsY0FBYytCLFVBQVUsQ0FBQ3pCLEtBQUtGLEVBQUUsRUFBRTtnQkFBRStGLGFBQWEsSUFBSS9ELE9BQU9DLFdBQVc7WUFBRztZQUUxRSx5QkFBeUI7WUFDekIsTUFBTStELFNBQXNCO2dCQUMxQjlGO2dCQUNBcUM7Z0JBQ0F5QztnQkFDQSxzQ0FBc0M7Z0JBQ3RDaUIsV0FBVyxLQUFLLE1BQU07WUFDeEI7WUFFQSxPQUFPO2dCQUNMcEMsUUFBUTtnQkFDUm9CLE1BQU1lO2dCQUNOZCxTQUFTO2dCQUNUbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGLEVBQUUsT0FBT1ksT0FBTztZQUNkckIsUUFBUXFCLEtBQUssQ0FBQyxTQUFTQTtZQUN2QixPQUFPO2dCQUNMckMsUUFBUTtnQkFDUm9CLE1BQU07Z0JBQ05DLFNBQVM7Z0JBQ1RuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTWEsT0FBT0MsWUFBb0IsRUFBOEI7UUFDN0QsSUFBSTtZQUNGLGtDQUFrQztZQUNsQyxJQUFJLENBQUNBLGNBQWM7Z0JBQ2pCLE9BQU87b0JBQ0x2QyxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU1yRSxzREFBYUEsQ0FBQzJHLGFBQWE7b0JBQ2pDakIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUFULFFBQVFDLEdBQUcsQ0FBQztZQUVaLE9BQU87Z0JBQ0xqQixRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBUztnQkFDVG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRixFQUFFLE9BQU9ZLE9BQU87WUFDZHJCLFFBQVFxQixLQUFLLENBQUMsU0FBU0E7WUFDdkIsT0FBTztnQkFDTHJDLFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTO2dCQUNUbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGO0lBQ0Y7SUFFQTs7O0dBR0MsR0FDRCxNQUFNYyxhQUFhRSxPQUE0QixFQUFxQztRQUNsRixJQUFJO1lBQ0Z6QixRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNLEVBQUVzQixZQUFZLEVBQUUsR0FBR0U7WUFFekIsdUJBQXVCO1lBQ3ZCLE1BQU1DLFdBQVcsYUFBa0I7WUFDbkMsSUFBSSxDQUFDQSxVQUFVO2dCQUNiMUIsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE9BQU87b0JBQ0xqQixRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU07b0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQSx1QkFBdUI7WUFDdkIsTUFBTSxFQUFFa0Isc0JBQXNCLEVBQUUsR0FBRyxNQUFNLDRRQUFPO1lBRWhELGtCQUFrQjtZQUNsQixNQUFNQyxnQkFBZ0IsTUFBTUQsdUJBQXVCRSxrQkFBa0IsQ0FBQ047WUFDdEUsSUFBSSxDQUFDSyxjQUFjRSxPQUFPLEVBQUU7Z0JBQzFCOUIsUUFBUUMsR0FBRyxDQUFDLGdEQUFnRDJCLGNBQWNQLEtBQUs7Z0JBQy9FLE9BQU87b0JBQ0xyQyxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBU3VCLGNBQWNQLEtBQUssSUFBSTtvQkFDaENuQyxNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsSUFBSSxDQUFDbUIsY0FBY25HLE1BQU0sSUFBSSxDQUFDbUcsY0FBY2xFLFNBQVMsRUFBRTtnQkFDckRzQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osT0FBTztvQkFDTGpCLFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLFdBQVc7WUFDWCxNQUFNcEYsT0FBT04sY0FBY0csV0FBVyxDQUFDMEcsY0FBY25HLE1BQU07WUFDM0QsSUFBSSxDQUFDSixNQUFNO2dCQUNUMkUsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQzJCLGNBQWNuRyxNQUFNO2dCQUNwRSxPQUFPO29CQUNMdUQsUUFBUTtvQkFDUm9CLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1RuQixNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsU0FBUztZQUNULElBQUlwRixLQUFLMkQsTUFBTSxLQUFLLFVBQVU7Z0JBQzVCZ0IsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQzVFLEtBQUsyRCxNQUFNO2dCQUM3RCxPQUFPO29CQUNMQSxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU07b0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQSxXQUFXO1lBQ1gsTUFBTTlDLFVBQVU1QyxjQUFjNkMsY0FBYyxDQUFDZ0UsY0FBY2xFLFNBQVM7WUFDcEUsSUFBSSxDQUFDQyxXQUFXLENBQUNBLFFBQVFJLFFBQVEsRUFBRTtnQkFDakNpQyxRQUFRQyxHQUFHLENBQUMsdUNBQXVDMkIsY0FBY2xFLFNBQVM7Z0JBQzFFLE9BQU87b0JBQ0xzQixRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU07b0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQSwrQkFBK0I7WUFDL0IsTUFBTXNCLGlCQUFpQixNQUFNSix1QkFBdUJLLG1CQUFtQixDQUFDO2dCQUN0RXZHLFFBQVFKLEtBQUtGLEVBQUU7Z0JBQ2ZJLFVBQVVGLEtBQUtFLFFBQVE7Z0JBQ3ZCMEQsT0FBTzVELEtBQUs0RCxLQUFLLENBQUNnRCxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUtoRCxJQUFJO2dCQUN2Q0csYUFBYWhFLEtBQUs0RCxLQUFLLENBQUNrRCxPQUFPLENBQUNELENBQUFBLE9BQVFBLEtBQUs3QyxXQUFXLENBQUM0QyxHQUFHLENBQUNHLENBQUFBLE9BQVFBLEtBQUtsRCxJQUFJO2dCQUM5RXhCLFdBQVdDLFFBQVF4QyxFQUFFO1lBQ3ZCO1lBRUEsTUFBTWtILGtCQUFrQixNQUFNVix1QkFBdUJXLG9CQUFvQixDQUFDakgsS0FBS0YsRUFBRSxFQUFFd0MsUUFBUXhDLEVBQUU7WUFFN0YsY0FBYztZQUNkSixjQUFjMEMsYUFBYSxDQUFDRSxRQUFReEMsRUFBRSxFQUFFO2dCQUN0Q29ILGNBQWMsSUFBSXBGLE9BQU9DLFdBQVc7WUFDdEM7WUFFQTRDLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0M1RSxLQUFLRSxRQUFRO1lBQ2pFLE9BQU87Z0JBQ0x5RCxRQUFRO2dCQUNSb0IsTUFBTTtvQkFDSm9DLGFBQWFUO29CQUNiUixjQUFjYztvQkFDZGpCLFdBQVcsS0FBSyxNQUFNO2dCQUN4QjtnQkFDQWYsU0FBUztnQkFDVG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRixFQUFFLE9BQU9ZLE9BQU87WUFDZHJCLFFBQVFxQixLQUFLLENBQUMsbURBQXlDQTtZQUN2RCxPQUFPO2dCQUNMckMsUUFBUTtnQkFDUm9CLE1BQU07Z0JBQ05DLFNBQVM7Z0JBQ1RuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTWdDLGNBQWNDLEtBQWEsRUFBOEI7UUFDN0QsSUFBSTtZQUNGMUMsUUFBUUMsR0FBRyxDQUFDO1lBRVosbUJBQW1CO1lBQ25CLE1BQU15QixXQUFXLGFBQWtCO1lBQ25DLElBQUksQ0FBQ0EsVUFBVTtnQkFDYjFCLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPO29CQUNMakIsUUFBUTtvQkFDUm9CLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1RuQixNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsa0NBQWtDO1lBQ2xDLE1BQU1rQyxrQkFBa0IsSUFBSSxDQUFDLE1BQU0sNFFBQWlDLEVBQUdDLHNCQUFzQjtZQUU3RixvQkFBb0I7WUFDcEIsTUFBTUMsa0JBQWtCLE1BQU1GLGdCQUFnQkcsaUJBQWlCLENBQUNKO1lBQ2hFLElBQUksQ0FBQ0csZ0JBQWdCZixPQUFPLEVBQUU7Z0JBQzVCOUIsUUFBUUMsR0FBRyxDQUFDLHdDQUF3QzRDLGdCQUFnQnhCLEtBQUs7Z0JBQ3pFLE9BQU87b0JBQ0xyQyxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBU3dDLGdCQUFnQnhCLEtBQUssSUFBSTtvQkFDbENuQyxNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsY0FBYztZQUNkLE1BQU1zQyxVQUFVRixnQkFBZ0JFLE9BQU87WUFDdkMsSUFBSSxDQUFDQSxXQUFXLENBQUNBLFFBQVF0SCxNQUFNLEVBQUU7Z0JBQy9CdUUsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE9BQU87b0JBQ0xqQixRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU07b0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQSx3QkFBd0I7WUFDeEIsTUFBTXBGLE9BQU9OLGNBQWNHLFdBQVcsQ0FBQzZILFFBQVF0SCxNQUFNO1lBQ3JELElBQUksQ0FBQ0osTUFBTTtnQkFDVDJFLFFBQVFDLEdBQUcsQ0FBQywyQ0FBMkM4QyxRQUFRdEgsTUFBTTtnQkFDckUsT0FBTztvQkFDTHVELFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLFNBQVM7WUFDVCxJQUFJcEYsS0FBSzJELE1BQU0sS0FBSyxVQUFVO2dCQUM1QmdCLFFBQVFDLEdBQUcsQ0FBQyxzQ0FBc0M1RSxLQUFLMkQsTUFBTTtnQkFDN0QsT0FBTztvQkFDTEEsUUFBUTtvQkFDUm9CLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1RuQixNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsK0JBQStCO1lBQy9CLElBQUlzQyxRQUFRckYsU0FBUyxFQUFFO2dCQUNyQixNQUFNQyxVQUFVNUMsY0FBYzZDLGNBQWMsQ0FBQ21GLFFBQVFyRixTQUFTO2dCQUM5RCxJQUFJLENBQUNDLFdBQVcsQ0FBQ0EsUUFBUUksUUFBUSxFQUFFO29CQUNqQ2lDLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUM4QyxRQUFRckYsU0FBUztvQkFDcEUsT0FBTzt3QkFDTHNCLFFBQVE7d0JBQ1JvQixNQUFNO3dCQUNOQyxTQUFTO3dCQUNUbkIsTUFBTTt3QkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7d0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtvQkFDbkM7Z0JBQ0Y7WUFDRjtZQUVBVCxRQUFRQyxHQUFHLENBQUMsd0NBQXdDNUUsS0FBS0UsUUFBUTtZQUNqRSxPQUFPO2dCQUNMeUQsUUFBUTtnQkFDUm9CLE1BQU0vRTtnQkFDTmdGLFNBQVM7Z0JBQ1RuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0YsRUFBRSxPQUFPWSxPQUFPO1lBQ2RyQixRQUFRcUIsS0FBSyxDQUFDLG1EQUF5Q0E7WUFDdkQsT0FBTztnQkFDTHJDLFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTO2dCQUNUbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGO0lBQ0Y7SUFFQSx1QkFBdUI7SUFFdkI7O0dBRUMsR0FDRCxvQkFBb0M7UUFDbEMsT0FBTyxPQUFxQkssT0FBZDNELEtBQUtxQixHQUFHLElBQUcsS0FBMkMsT0FBeENzQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztJQUNuRTtJQUVBLGlCQUFpQjtJQUNqQixNQUFNL0YsWUFBWUMsRUFBVSxFQUE4QjtRQUN4RCxNQUFNRSxPQUFPTixjQUFjRyxXQUFXLENBQUNDO1FBQ3ZDLElBQUksQ0FBQ0UsTUFBTTtZQUNULE9BQU87Z0JBQ0wyRCxRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBUztnQkFDVG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRjtRQUVBLE9BQU87WUFDTHpCLFFBQVE7WUFDUm9CLE1BQU0vRTtZQUNOZ0YsU0FBUztZQUNUbkIsTUFBTTtZQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztZQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7UUFDbkM7SUFDRjtJQUVBLE1BQU1uRixrQkFBa0JDLFFBQWdCLEVBQThCO1FBQ3BFLE1BQU1GLE9BQU9OLGNBQWNPLGlCQUFpQixDQUFDQztRQUM3QyxJQUFJLENBQUNGLE1BQU07WUFDVCxPQUFPO2dCQUNMMkQsUUFBUTtnQkFDUm9CLE1BQU07Z0JBQ05DLFNBQVM7Z0JBQ1RuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0Y7UUFFQSxPQUFPO1lBQ0x6QixRQUFRO1lBQ1JvQixNQUFNL0U7WUFDTmdGLFNBQVM7WUFDVG5CLE1BQU07WUFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7WUFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1FBQ25DO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTXVDLFdBQVdDLFFBQTJCLEVBQThCO1FBQ3hFLElBQUk7WUFDRixhQUFhO1lBQ2IsTUFBTUMsZUFBZW5JLGNBQWNPLGlCQUFpQixDQUFDMkgsU0FBUzFILFFBQVE7WUFDdEUsSUFBSTJILGNBQWM7Z0JBQ2hCLE9BQU87b0JBQ0xsRSxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU07b0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQSxrQkFBa0I7WUFDbEIsSUFBSXdDLFNBQVNuRSxLQUFLLEVBQUU7Z0JBQ2xCLE1BQU1xRSxvQkFBb0JwSSxjQUFjQyxRQUFRLEdBQUdJLElBQUksQ0FBQ2dJLENBQUFBLElBQUtBLEVBQUV0RSxLQUFLLEtBQUttRSxTQUFTbkUsS0FBSztnQkFDdkYsSUFBSXFFLG1CQUFtQjtvQkFDckIsT0FBTzt3QkFDTG5FLFFBQVE7d0JBQ1JvQixNQUFNO3dCQUNOQyxTQUFTO3dCQUNUbkIsTUFBTTt3QkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7d0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtvQkFDbkM7Z0JBQ0Y7WUFDRjtZQUVBLFNBQVM7WUFDVCxNQUFNaEYsU0FBUyxRQUFzQnFGLE9BQWQzRCxLQUFLcUIsR0FBRyxJQUFHLEtBQTJDLE9BQXhDc0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7WUFFMUUsT0FBTztZQUNQLE1BQU0sRUFBRW9DLE1BQU14SCxZQUFZLEVBQUUsR0FBRyxNQUFNZix3RUFBZUEsQ0FBQ3dJLFlBQVksQ0FBQ0wsU0FBUy9DLFFBQVE7WUFFbkYseUJBQXlCO1lBQ3pCLE1BQU1qQixRQUFnQmdFLFNBQVNNLE9BQU8sQ0FBQ3RCLEdBQUcsQ0FBQ3VCLENBQUFBLFNBQVc7b0JBQ3BEckksSUFBSXFJO29CQUNKdEUsTUFBTXNFLFdBQVcsVUFBVSxVQUFVO29CQUNyQ3JFLE1BQU1xRSxXQUFXLFVBQVUsUUFBUTtvQkFDbkNwRSxhQUFhb0UsV0FBVyxVQUFVLFlBQVk7b0JBQzlDbkUsYUFBYW1FLFdBQVcsVUFBVTt3QkFDaEM7NEJBQ0VySSxJQUFJOzRCQUNKK0QsTUFBTTs0QkFDTkMsTUFBTTs0QkFDTkcsTUFBTTs0QkFDTkMsVUFBVTs0QkFDVkMsUUFBUTs0QkFDUkMsV0FBVyxJQUFJdEMsT0FBT0MsV0FBVzs0QkFDakNGLFdBQVcsSUFBSUMsT0FBT0MsV0FBVzt3QkFDbkM7cUJBQ0QsR0FBRyxFQUFFO29CQUNOcUMsV0FBVyxJQUFJdEMsT0FBT0MsV0FBVztvQkFDakNGLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztnQkFDbkM7WUFFQSxTQUFTO1lBQ1QsTUFBTXFHLFVBQWdCO2dCQUNwQnRJLElBQUlNO2dCQUNKRixVQUFVMEgsU0FBUzFILFFBQVE7Z0JBQzNCdUQsT0FBT21FLFNBQVNuRSxLQUFLO2dCQUNyQkMsVUFBVWtFLFNBQVNsRSxRQUFRO2dCQUMzQjJFLE9BQU9ULFNBQVNTLEtBQUs7Z0JBQ3JCQyxjQUFjVixTQUFTVSxZQUFZO2dCQUNuQ0MsWUFBWVgsU0FBU1csVUFBVTtnQkFDL0I1RSxRQUFRO2dCQUNSQztnQkFDQUksYUFBYSxFQUFFO2dCQUNmSSxXQUFXLElBQUl0QyxPQUFPQyxXQUFXO2dCQUNqQ0YsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1lBRUEsVUFBVTtZQUNWckMsY0FBYzZCLE9BQU8sQ0FBQzZHO1lBQ3RCMUksY0FBY2EsZUFBZSxDQUFDSCxRQUFRSTtZQUV0QyxPQUFPO2dCQUNMbUQsUUFBUTtnQkFDUm9CLE1BQU1xRDtnQkFDTnBELFNBQVM7Z0JBQ1RuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0YsRUFBRSxPQUFPWSxPQUFPO1lBQ2QsT0FBTztnQkFDTHJDLFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTZ0IsaUJBQWlCd0MsUUFBUXhDLE1BQU1oQixPQUFPLEdBQUc7Z0JBQ2xEbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGO0lBQ0Y7SUFFQSxNQUFNM0QsV0FBVzNCLEVBQVUsRUFBRTRCLE9BQTBCLEVBQThCO1FBQ25GLElBQUk7WUFDRixNQUFNbUcsZUFBZW5JLGNBQWNHLFdBQVcsQ0FBQ0M7WUFDL0MsSUFBSSxDQUFDK0gsY0FBYztnQkFDakIsT0FBTztvQkFDTGxFLFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLGtCQUFrQjtZQUNsQixJQUFJMUQsUUFBUStCLEtBQUssSUFBSS9CLFFBQVErQixLQUFLLEtBQUtvRSxhQUFhcEUsS0FBSyxFQUFFO2dCQUN6RCxNQUFNcUUsb0JBQW9CcEksY0FBY0MsUUFBUSxHQUFHSSxJQUFJLENBQUNnSSxDQUFBQSxJQUFLQSxFQUFFdEUsS0FBSyxLQUFLL0IsUUFBUStCLEtBQUssSUFBSXNFLEVBQUVqSSxFQUFFLEtBQUtBO2dCQUNuRyxJQUFJZ0ksbUJBQW1CO29CQUNyQixPQUFPO3dCQUNMbkUsUUFBUTt3QkFDUm9CLE1BQU07d0JBQ05DLFNBQVM7d0JBQ1RuQixNQUFNO3dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVzt3QkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO29CQUNuQztnQkFDRjtZQUNGO1lBRUEsU0FBUztZQUNULElBQUlxRCxlQUFlWixhQUFhakUsS0FBSztZQUNyQyxJQUFJbEMsUUFBUXdHLE9BQU8sRUFBRTtnQkFDbkJPLGVBQWUvRyxRQUFRd0csT0FBTyxDQUFDdEIsR0FBRyxDQUFDdUIsQ0FBQUEsU0FBVzt3QkFDNUNySSxJQUFJcUk7d0JBQ0p0RSxNQUFNc0UsV0FBVyxVQUFVLFVBQVU7d0JBQ3JDckUsTUFBTXFFLFdBQVcsVUFBVSxRQUFRO3dCQUNuQ3BFLGFBQWFvRSxXQUFXLFVBQVUsWUFBWTt3QkFDOUNuRSxhQUFhbUUsV0FBVyxVQUFVOzRCQUNoQztnQ0FDRXJJLElBQUk7Z0NBQ0orRCxNQUFNO2dDQUNOQyxNQUFNO2dDQUNORyxNQUFNO2dDQUNOQyxVQUFVO2dDQUNWQyxRQUFRO2dDQUNSQyxXQUFXLElBQUl0QyxPQUFPQyxXQUFXO2dDQUNqQ0YsV0FBVyxJQUFJQyxPQUFPQyxXQUFXOzRCQUNuQzt5QkFDRCxHQUFHLEVBQUU7d0JBQ05xQyxXQUFXLElBQUl0QyxPQUFPQyxXQUFXO3dCQUNqQ0YsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO29CQUNuQztZQUNGO1lBRUEsU0FBUztZQUNULE1BQU0yRyxhQUFhO2dCQUNqQixHQUFHaEgsT0FBTztnQkFDVmtDLE9BQU82RTtnQkFDUDVHLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztZQUNuQztZQUVBLE1BQU1tQixVQUFVeEQsY0FBYytCLFVBQVUsQ0FBQzNCLElBQUk0STtZQUM3QyxJQUFJLENBQUN4RixTQUFTO2dCQUNaLE9BQU87b0JBQ0xTLFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLDRCQUE0QjtZQUM1QixJQUFJMUQsUUFBUWlDLE1BQU0sSUFBS2pDLENBQUFBLFFBQVFpQyxNQUFNLEtBQUssY0FBY2pDLFFBQVFpQyxNQUFNLEtBQUssUUFBTyxHQUFJO2dCQUNwRixNQUFNLElBQUksQ0FBQ2dGLHFCQUFxQixDQUFDN0k7WUFDbkM7WUFFQSxNQUFNOEksY0FBY2xKLGNBQWNHLFdBQVcsQ0FBQ0M7WUFDOUMsT0FBTztnQkFDTDZELFFBQVE7Z0JBQ1JvQixNQUFNNkQ7Z0JBQ041RCxTQUFTO2dCQUNUbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGLEVBQUUsT0FBT1ksT0FBTztZQUNkLE9BQU87Z0JBQ0xyQyxRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBU2dCLGlCQUFpQndDLFFBQVF4QyxNQUFNaEIsT0FBTyxHQUFHO2dCQUNsRG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRjtJQUNGO0lBRUEsTUFBTXBELFdBQVdsQyxFQUFVLEVBQWlDO1FBQzFELElBQUk7WUFDRixNQUFNK0gsZUFBZW5JLGNBQWNHLFdBQVcsQ0FBQ0M7WUFDL0MsSUFBSSxDQUFDK0gsY0FBYztnQkFDakIsT0FBTztvQkFDTGxFLFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLG9CQUFvQjtZQUNwQixJQUFJeUMsYUFBYTNILFFBQVEsS0FBSyxTQUFTO2dCQUNyQyxPQUFPO29CQUNMeUQsUUFBUTtvQkFDUm9CLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1RuQixNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsTUFBTWxDLFVBQVV4RCxjQUFjc0MsVUFBVSxDQUFDbEM7WUFDekMsT0FBTztnQkFDTDZELFFBQVE7Z0JBQ1JvQixNQUFNN0I7Z0JBQ044QixTQUFTOUIsVUFBVSxXQUFXO2dCQUM5QlcsTUFBTVgsVUFBVSx3QkFBd0I7Z0JBQ3hDZ0MsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0YsRUFBRSxPQUFPWSxPQUFPO1lBQ2QsT0FBTztnQkFDTHJDLFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTZ0IsaUJBQWlCd0MsUUFBUXhDLE1BQU1oQixPQUFPLEdBQUc7Z0JBQ2xEbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGO0lBQ0Y7SUFFQSxNQUFNeUQsY0FBY3pJLE1BQWMsRUFBRTBJLFdBQWlDLEVBQXFDO1FBQ3hHLGlCQUFpQjtRQUNqQixNQUFNLElBQUlOLE1BQU07SUFDbEI7SUFFQSxNQUFNTyxXQUFXMUcsU0FBaUIsRUFBcUM7UUFDckUsaUJBQWlCO1FBQ2pCLE1BQU0sSUFBSW1HLE1BQU07SUFDbEI7SUFFQSxNQUFNcEcsY0FBY0MsU0FBaUIsRUFBRVgsT0FBNkIsRUFBcUM7UUFDdkcsaUJBQWlCO1FBQ2pCLE1BQU0sSUFBSThHLE1BQU07SUFDbEI7SUFFQSxNQUFNNUYsY0FBY1AsU0FBaUIsRUFBaUM7UUFDcEUsaUJBQWlCO1FBQ2pCLE1BQU0sSUFBSW1HLE1BQU07SUFDbEI7SUFFQSxNQUFNaEcsZ0JBQWdCcEMsTUFBYyxFQUF1QztRQUN6RSxtQkFBbUI7UUFDbkIsTUFBTSxJQUFJb0ksTUFBTTtJQUNsQjtJQUVBLE1BQU1RLGVBQWU1SSxNQUFjLEVBQUVLLGVBQXVCLEVBQUV3SSxXQUFtQixFQUFpQztRQUNoSCxpQkFBaUI7UUFDakIsTUFBTSxJQUFJVCxNQUFNO0lBQ2xCO0lBRUEsTUFBTVUsY0FBYzlJLE1BQWMsRUFBRTZJLFdBQW1CLEVBQWlDO1FBQ3RGLElBQUk7WUFDRixNQUFNakosT0FBT04sY0FBY0csV0FBVyxDQUFDTztZQUN2QyxJQUFJLENBQUNKLE1BQU07Z0JBQ1QsT0FBTztvQkFDTDJELFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLFNBQVM7WUFDVCxNQUFNK0QsYUFBYTFKLHdFQUFlQSxDQUFDMkosZ0JBQWdCLENBQUNIO1lBQ3BELElBQUksQ0FBQ0UsV0FBVzFDLE9BQU8sRUFBRTtnQkFDdkIsT0FBTztvQkFDTDlDLFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTLFlBQTJDLE9BQS9CbUUsV0FBV0UsUUFBUSxDQUFDQyxJQUFJLENBQUM7b0JBQzlDekYsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLFFBQVE7WUFDUixNQUFNLEVBQUU0QyxNQUFNeEgsWUFBWSxFQUFFLEdBQUcsTUFBTWYsd0VBQWVBLENBQUN3SSxZQUFZLENBQUNnQjtZQUVsRSxnQkFBZ0I7WUFDaEIsSUFBSXZKLGNBQWN1QixtQkFBbUIsQ0FBQ2IsUUFBUUksZUFBZTtnQkFDM0QsT0FBTztvQkFDTG1ELFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLE9BQU87WUFDUDFGLGNBQWNhLGVBQWUsQ0FBQ0gsUUFBUUk7WUFFdEMsT0FBTztnQkFDTG1ELFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTO2dCQUNUbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGLEVBQUUsT0FBT1ksT0FBTztZQUNkLE9BQU87Z0JBQ0xyQyxRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBU2dCLGlCQUFpQndDLFFBQVF4QyxNQUFNaEIsT0FBTyxHQUFHO2dCQUNsRG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRjtJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU1tRSxhQUFhQyxNQUtsQixFQUEwRDtRQUN6RCxJQUFJO1lBQ0YsSUFBSTVKLFFBQVFGLGNBQWNDLFFBQVE7WUFFbEMsT0FBTztZQUNQLElBQUk2SixtQkFBQUEsNkJBQUFBLE9BQVFDLE1BQU0sRUFBRTtnQkFDbEIsTUFBTUMsYUFBYUYsT0FBT0MsTUFBTSxDQUFDRSxXQUFXO2dCQUM1Qy9KLFFBQVFBLE1BQU02QyxNQUFNLENBQUN6QyxDQUFBQSxPQUNuQkEsS0FBS0UsUUFBUSxDQUFDeUosV0FBVyxHQUFHekksUUFBUSxDQUFDd0ksZUFDckMxSixLQUFLMEQsUUFBUSxDQUFDaUcsV0FBVyxHQUFHekksUUFBUSxDQUFDd0ksZUFDcEMxSixLQUFLeUQsS0FBSyxJQUFJekQsS0FBS3lELEtBQUssQ0FBQ2tHLFdBQVcsR0FBR3pJLFFBQVEsQ0FBQ3dJO1lBRXJEO1lBRUEsT0FBTztZQUNQLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUTdGLE1BQU0sRUFBRTtnQkFDbEIvRCxRQUFRQSxNQUFNNkMsTUFBTSxDQUFDekMsQ0FBQUEsT0FBUUEsS0FBSzJELE1BQU0sS0FBSzZGLE9BQU83RixNQUFNO1lBQzVEO1lBRUEsTUFBTWlHLFFBQVFoSyxNQUFNaUIsTUFBTTtZQUUxQixLQUFLO1lBQ0wsSUFBSTJJLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUssSUFBSSxNQUFJTCxtQkFBQUEsNkJBQUFBLE9BQVFNLFFBQVEsR0FBRTtnQkFDcEMsTUFBTUMsUUFBUSxDQUFDUCxPQUFPSyxJQUFJLEdBQUcsS0FBS0wsT0FBT00sUUFBUTtnQkFDakQsTUFBTUUsTUFBTUQsUUFBUVAsT0FBT00sUUFBUTtnQkFDbkNsSyxRQUFRQSxNQUFNcUssS0FBSyxDQUFDRixPQUFPQztZQUM3QjtZQUVBLE9BQU87Z0JBQ0xyRyxRQUFRO2dCQUNSb0IsTUFBTTtvQkFBRW5GO29CQUFPZ0s7Z0JBQU07Z0JBQ3JCNUUsU0FBUztnQkFDVG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRixFQUFFLE9BQU9ZLE9BQU87WUFDZCxPQUFPO2dCQUNMckMsUUFBUTtnQkFDUm9CLE1BQU07Z0JBQ05DLFNBQVNnQixpQkFBaUJ3QyxRQUFReEMsTUFBTWhCLE9BQU8sR0FBRztnQkFDbERuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0Y7SUFDRjtJQUVBLGFBQWE7SUFDYixNQUFNOEUsZ0JBQWdCOUosTUFBYyxFQUFFOEgsT0FBaUIsRUFBOEI7UUFDbkYsSUFBSTtZQUNGLE1BQU1sSSxPQUFPTixjQUFjRyxXQUFXLENBQUNPO1lBQ3ZDLElBQUksQ0FBQ0osTUFBTTtnQkFDVCxPQUFPO29CQUNMMkQsUUFBUTtvQkFDUm9CLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1RuQixNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsZUFBZTtZQUNmLE1BQU14QixRQUFnQnNFLFFBQVF0QixHQUFHLENBQUN1QixDQUFBQSxTQUFXO29CQUMzQ3JJLElBQUlxSTtvQkFDSnRFLE1BQU1zRSxXQUFXLFVBQVUsVUFBVTtvQkFDckNyRSxNQUFNcUUsV0FBVyxVQUFVLFFBQVE7b0JBQ25DcEUsYUFBYW9FLFdBQVcsVUFBVSxZQUFZO29CQUM5Q25FLGFBQWFtRSxXQUFXLFVBQVU7d0JBQ2hDOzRCQUNFckksSUFBSTs0QkFDSitELE1BQU07NEJBQ05DLE1BQU07NEJBQ05HLE1BQU07NEJBQ05DLFVBQVU7NEJBQ1ZDLFFBQVE7NEJBQ1JDLFdBQVcsSUFBSXRDLE9BQU9DLFdBQVc7NEJBQ2pDRixXQUFXLElBQUlDLE9BQU9DLFdBQVc7d0JBQ25DO3FCQUNELEdBQUcsRUFBRTtvQkFDTnFDLFdBQVcsSUFBSXRDLE9BQU9DLFdBQVc7b0JBQ2pDRixXQUFXLElBQUlDLE9BQU9DLFdBQVc7Z0JBQ25DO1lBRUEsU0FBUztZQUNULE1BQU1tQixVQUFVeEQsY0FBYytCLFVBQVUsQ0FBQ3JCLFFBQVE7Z0JBQy9Dd0Q7Z0JBQ0EvQixXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDbkM7WUFFQSxJQUFJLENBQUNtQixTQUFTO2dCQUNaLE9BQU87b0JBQ0xTLFFBQVE7b0JBQ1JvQixNQUFNO29CQUNOQyxTQUFTO29CQUNUbkIsTUFBTTtvQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7b0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtnQkFDbkM7WUFDRjtZQUVBLE1BQU13RCxjQUFjbEosY0FBY0csV0FBVyxDQUFDTztZQUM5QyxPQUFPO2dCQUNMdUQsUUFBUTtnQkFDUm9CLE1BQU02RDtnQkFDTjVELFNBQVM7Z0JBQ1RuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0YsRUFBRSxPQUFPWSxPQUFPO1lBQ2QsT0FBTztnQkFDTHJDLFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTZ0IsaUJBQWlCd0MsUUFBUXhDLE1BQU1oQixPQUFPLEdBQUc7Z0JBQ2xEbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBY3VELHNCQUFzQnZJLE1BQWMsRUFBaUI7UUFDakUsSUFBSTtZQUNGLGNBQWM7WUFDZCxNQUFNK0osZUFBZXpLLGNBQWN3QyxXQUFXLEdBQUdPLE1BQU0sQ0FDckRILENBQUFBLFVBQVdBLFFBQVFsQyxNQUFNLEtBQUtBLFVBQVVrQyxRQUFRSSxRQUFRO1lBRzFELGNBQWM7WUFDZCxLQUFLLE1BQU1KLFdBQVc2SCxhQUFjO2dCQUNsQ3pLLGNBQWMwQyxhQUFhLENBQUNFLFFBQVF4QyxFQUFFLEVBQUU7b0JBQ3RDNEMsVUFBVTtvQkFDVjBILGdCQUFnQixJQUFJdEksT0FBT0MsV0FBVztnQkFDeEM7WUFDRjtZQUVBNEMsUUFBUUMsR0FBRyxDQUFDLFNBQXFCdUYsT0FBWi9KLFFBQU8sT0FBeUIsT0FBcEIrSixhQUFhdEosTUFBTSxFQUFDO1FBQ3ZELEVBQUUsT0FBT21GLE9BQU87WUFDZHJCLFFBQVFxQixLQUFLLENBQUMsYUFBYUE7UUFDN0I7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNcUUsc0JBQXNCQyxPQUFpQixFQUFFM0csTUFBa0IsRUFBd0U7UUFDdkksSUFBSTtZQUNGLElBQUk0RyxlQUFlO1lBQ25CLElBQUlDLGVBQWU7WUFDbkIsTUFBTUMsU0FBbUIsRUFBRTtZQUUzQixLQUFLLE1BQU1ySyxVQUFVa0ssUUFBUztnQkFDNUIsSUFBSTtvQkFDRixNQUFNdEssT0FBT04sY0FBY0csV0FBVyxDQUFDTztvQkFDdkMsSUFBSSxDQUFDSixNQUFNO3dCQUNUd0s7d0JBQ0FDLE9BQU9qSixJQUFJLENBQUMsTUFBYSxPQUFQcEIsUUFBTzt3QkFDekI7b0JBQ0Y7b0JBRUEsb0JBQW9CO29CQUNwQixJQUFJSixLQUFLRSxRQUFRLEtBQUssV0FBV3lELFdBQVcsVUFBVTt3QkFDcEQ2Rzt3QkFDQUMsT0FBT2pKLElBQUksQ0FBRTt3QkFDYjtvQkFDRjtvQkFFQSxNQUFNMEIsVUFBVXhELGNBQWMrQixVQUFVLENBQUNyQixRQUFRO3dCQUMvQ3VEO3dCQUNBOUIsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO29CQUNuQztvQkFFQSxJQUFJbUIsU0FBUzt3QkFDWCwwQkFBMEI7d0JBQzFCLElBQUlTLFdBQVcsY0FBY0EsV0FBVyxVQUFVOzRCQUNoRCxNQUFNLElBQUksQ0FBQ2dGLHFCQUFxQixDQUFDdkk7d0JBQ25DO3dCQUNBbUs7b0JBQ0YsT0FBTzt3QkFDTEM7d0JBQ0FDLE9BQU9qSixJQUFJLENBQUMsUUFBZSxPQUFQcEIsUUFBTztvQkFDN0I7Z0JBQ0YsRUFBRSxPQUFPNEYsT0FBTztvQkFDZHdFO29CQUNBQyxPQUFPakosSUFBSSxDQUFDLFFBQXlCd0UsT0FBakI1RixRQUFPLFlBQTBELE9BQWhENEYsaUJBQWlCd0MsUUFBUXhDLE1BQU1oQixPQUFPLEdBQUc7Z0JBQ2hGO1lBQ0Y7WUFFQSxPQUFPO2dCQUNMckIsUUFBUTtnQkFDUm9CLE1BQU07b0JBQUV3RjtvQkFBY0M7Z0JBQWE7Z0JBQ25DeEYsU0FBUyxhQUFrQ3dGLE9BQXJCRCxjQUFhLFVBQXlCRSxPQUFqQkQsY0FBYSxNQUF3RCxPQUFwREMsT0FBTzVKLE1BQU0sR0FBRyxJQUFJLE9BQXlCLE9BQWxCNEosT0FBT25CLElBQUksQ0FBQyxTQUFVO2dCQUM3R3pGLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRixFQUFFLE9BQU9ZLE9BQU87WUFDZCxPQUFPO2dCQUNMckMsUUFBUTtnQkFDUm9CLE1BQU07Z0JBQ05DLFNBQVNnQixpQkFBaUJ3QyxRQUFReEMsTUFBTWhCLE9BQU8sR0FBRztnQkFDbERuQixNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0Y7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNL0Qsb0JBQW9CakIsTUFBYyxFQUF3RDtZQUF0RGdCLFFBQUFBLGlFQUFpQjtRQUN6RCxJQUFJO1lBQ0YsTUFBTXBCLE9BQU9OLGNBQWNHLFdBQVcsQ0FBQ087WUFDdkMsSUFBSSxDQUFDSixNQUFNO2dCQUNULE9BQU87b0JBQ0wyRCxRQUFRO29CQUNSb0IsTUFBTTtvQkFDTkMsU0FBUztvQkFDVG5CLE1BQU07b0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO29CQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7Z0JBQ25DO1lBQ0Y7WUFFQTFGLGNBQWN5QixzQkFBc0IsQ0FBQ2YsUUFBUWdCO1lBRTdDLE9BQU87Z0JBQ0x1QyxRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBUzVELFFBQVEsY0FBYztnQkFDL0J5QyxNQUFNO2dCQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztnQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO1lBQ25DO1FBQ0YsRUFBRSxPQUFPWSxPQUFPO1lBQ2QsT0FBTztnQkFDTHJDLFFBQVE7Z0JBQ1JvQixNQUFNO2dCQUNOQyxTQUFTZ0IsaUJBQWlCd0MsUUFBUXhDLE1BQU1oQixPQUFPLEdBQUc7Z0JBQ2xEbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXNGLHlCQUEwRTtZQUFuRDdKLFNBQUFBLGlFQUFpQjtRQUM1QyxJQUFJO1lBQ0YsTUFBTWdFLFdBQVdwRix3RUFBZUEsQ0FBQ2tMLHNCQUFzQixDQUFDOUosUUFBUTtZQUVoRSxPQUFPO2dCQUNMOEMsUUFBUTtnQkFDUm9CLE1BQU1GO2dCQUNORyxTQUFTO2dCQUNUbkIsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGLEVBQUUsT0FBT1ksT0FBTztZQUNkLE9BQU87Z0JBQ0xyQyxRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBU2dCLGlCQUFpQndDLFFBQVF4QyxNQUFNaEIsT0FBTyxHQUFHO2dCQUNsRG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRjtJQUNGO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU13Rix5QkFBeUJ4SyxNQUFjLEVBQWlDO1FBQzVFLElBQUk7WUFDRixNQUFNSixPQUFPTixjQUFjRyxXQUFXLENBQUNPO1lBQ3ZDLElBQUksQ0FBQ0osTUFBTTtnQkFDVCxPQUFPO29CQUNMMkQsUUFBUTtvQkFDUm9CLE1BQU07b0JBQ05DLFNBQVM7b0JBQ1RuQixNQUFNO29CQUNOcUIsV0FBVyxJQUFJcEQsT0FBT0MsV0FBVztvQkFDakNvRCxXQUFXLElBQUksQ0FBQ0MsaUJBQWlCO2dCQUNuQztZQUNGO1lBRUEsTUFBTXlGLGNBQWNuTCxjQUFjNEIseUJBQXlCLENBQUNsQjtZQUU1RCxPQUFPO2dCQUNMdUQsUUFBUTtnQkFDUm9CLE1BQU04RjtnQkFDTjdGLFNBQVM2RixjQUFjLGFBQWE7Z0JBQ3BDaEgsTUFBTTtnQkFDTnFCLFdBQVcsSUFBSXBELE9BQU9DLFdBQVc7Z0JBQ2pDb0QsV0FBVyxJQUFJLENBQUNDLGlCQUFpQjtZQUNuQztRQUNGLEVBQUUsT0FBT1ksT0FBTztZQUNkLE9BQU87Z0JBQ0xyQyxRQUFRO2dCQUNSb0IsTUFBTTtnQkFDTkMsU0FBU2dCLGlCQUFpQndDLFFBQVF4QyxNQUFNaEIsT0FBTyxHQUFHO2dCQUNsRG5CLE1BQU07Z0JBQ05xQixXQUFXLElBQUlwRCxPQUFPQyxXQUFXO2dCQUNqQ29ELFdBQVcsSUFBSSxDQUFDQyxpQkFBaUI7WUFDbkM7UUFDRjtJQUNGO0lBcm1DQSxhQUFzQjtRQUNwQlQsUUFBUUMsR0FBRyxDQUFDO0lBQ2Q7QUFvbUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zZXJ2aWNlcy9kYXRhQWNjZXNzL0F1dGhEYXRhQWNjZXNzU2VydmljZS50cz83NWYxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog6K6k6K+B5pWw5o2u6K6/6Zeu5pyN5YqhXG4gKiBcbiAqIOi0n+i0o+eUqOaIt+iupOivgeebuOWFs+eahOaVsOaNruiuv+mXruaTjeS9nFxuICog6YG15b6qRGF0YUFjY2Vzc01hbmFnZXLnmoTmnrbmnoTop4TojINcbiAqL1xuXG5pbXBvcnQge1xuICBVc2VyLFxuICBSb2xlLFxuICBQZXJtaXNzaW9uLFxuICBMb2dpblJlcXVlc3QsXG4gIExvZ2luUmVzdWx0LFxuICBSZWZyZXNoVG9rZW5SZXF1ZXN0LFxuICBUb2tlblJlc3VsdCxcbiAgVXNlclNlc3Npb24sXG4gIEFwaVJlc3BvbnNlLFxuICBBdXRoRXJyb3JDb2RlLFxuICBDcmVhdGVVc2VyUmVxdWVzdCxcbiAgVXBkYXRlVXNlclJlcXVlc3QsXG4gIFVzZXJTdGF0dXMsXG4gIEpXVFBheWxvYWRcbn0gZnJvbSAnQC90eXBlcy9hdXRoJ1xuaW1wb3J0IHsgUGFzc3dvcmRNYW5hZ2VyIH0gZnJvbSAnQC91dGlscy9hdXRoL1Bhc3N3b3JkTWFuYWdlcidcblxuLyoqXG4gKiDorqTor4HmlbDmja7orr/pl67mjqXlj6NcbiAqL1xuZXhwb3J0IGludGVyZmFjZSBJQXV0aERhdGFBY2Nlc3Mge1xuICAvLyDorqTor4Hnm7jlhbNcbiAgbG9naW4oY3JlZGVudGlhbHM6IExvZ2luUmVxdWVzdCk6IFByb21pc2U8QXBpUmVzcG9uc2U8TG9naW5SZXN1bHQ+PlxuICBsb2dvdXQocmVmcmVzaFRva2VuOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPHZvaWQ+PlxuICByZWZyZXNoVG9rZW4ocmVxdWVzdDogUmVmcmVzaFRva2VuUmVxdWVzdCk6IFByb21pc2U8QXBpUmVzcG9uc2U8VG9rZW5SZXN1bHQ+PlxuICB2YWxpZGF0ZVRva2VuKHRva2VuOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXI+PlxuXG4gIC8vIOeUqOaIt+euoeeQhlxuICBnZXRVc2VyQnlJZChpZDogc3RyaW5nKTogUHJvbWlzZTxBcGlSZXNwb25zZTxVc2VyPj5cbiAgZ2V0VXNlckJ5VXNlcm5hbWUodXNlcm5hbWU6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlcj4+XG4gIGdldFVzZXJzTGlzdChwYXJhbXM/OiB7XG4gICAgcGFnZT86IG51bWJlclxuICAgIHBhZ2VTaXplPzogbnVtYmVyXG4gICAgc2VhcmNoPzogc3RyaW5nXG4gICAgc3RhdHVzPzogVXNlclN0YXR1c1xuICB9KTogUHJvbWlzZTxBcGlSZXNwb25zZTx7IHVzZXJzOiBVc2VyW10sIHRvdGFsOiBudW1iZXIgfT4+XG4gIGNyZWF0ZVVzZXIodXNlckRhdGE6IENyZWF0ZVVzZXJSZXF1ZXN0KTogUHJvbWlzZTxBcGlSZXNwb25zZTxVc2VyPj5cbiAgdXBkYXRlVXNlcihpZDogc3RyaW5nLCB1cGRhdGVzOiBVcGRhdGVVc2VyUmVxdWVzdCk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlcj4+XG4gIGRlbGV0ZVVzZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+XG4gIGFzc2lnblVzZXJSb2xlcyh1c2VySWQ6IHN0cmluZywgcm9sZUlkczogc3RyaW5nW10pOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXI+PlxuICBiYXRjaFVwZGF0ZVVzZXJTdGF0dXModXNlcklkczogc3RyaW5nW10sIHN0YXR1czogVXNlclN0YXR1cyk6IFByb21pc2U8QXBpUmVzcG9uc2U8eyBzdWNjZXNzQ291bnQ6IG51bWJlciwgZmFpbHVyZUNvdW50OiBudW1iZXIgfT4+XG5cbiAgLy8g5Lya6K+d566h55CGXG4gIGNyZWF0ZVNlc3Npb24odXNlcklkOiBzdHJpbmcsIHNlc3Npb25EYXRhOiBQYXJ0aWFsPFVzZXJTZXNzaW9uPik6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlclNlc3Npb24+PlxuICBnZXRTZXNzaW9uKHNlc3Npb25JZDogc3RyaW5nKTogUHJvbWlzZTxBcGlSZXNwb25zZTxVc2VyU2Vzc2lvbj4+XG4gIHVwZGF0ZVNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8VXNlclNlc3Npb24+KTogUHJvbWlzZTxBcGlSZXNwb25zZTxVc2VyU2Vzc2lvbj4+XG4gIGRlbGV0ZVNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPGJvb2xlYW4+PlxuICBnZXRVc2VyU2Vzc2lvbnModXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXJTZXNzaW9uW10+PlxuXG4gIC8vIOWvhueggeeuoeeQhlxuICBjaGFuZ2VQYXNzd29yZCh1c2VySWQ6IHN0cmluZywgY3VycmVudFBhc3N3b3JkOiBzdHJpbmcsIG5ld1Bhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPGJvb2xlYW4+PlxuICByZXNldFBhc3N3b3JkKHVzZXJJZDogc3RyaW5nLCBuZXdQYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj5cbiAgZm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQ6IHN0cmluZywgZm9yY2U/OiBib29sZWFuKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj5cbiAgZ2VuZXJhdGVTZWN1cmVQYXNzd29yZChsZW5ndGg/OiBudW1iZXIpOiBQcm9taXNlPEFwaVJlc3BvbnNlPHN0cmluZz4+XG4gIGNoZWNrRm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+XG59XG5cbi8qKlxuICog5qih5ouf5pWw5o2u5a2Y5YKoXG4gKiDlnKjlrp7pmYXpobnnm67kuK3vvIzov5nkupvmlbDmja7lupTor6XlrZjlgqjlnKjmlbDmja7lupPkuK1cbiAqL1xuY2xhc3MgTW9ja0RhdGFTdG9yZSB7XG4gIHByaXZhdGUgc3RhdGljIHVzZXJzOiBVc2VyW10gPSBbXG4gICAge1xuICAgICAgaWQ6ICd1c2VyLTEnLFxuICAgICAgdXNlcm5hbWU6ICdhZG1pbicsXG4gICAgICBlbWFpbDogJ2FkbWluQGV4YW1wbGUuY29tJyxcbiAgICAgIGZ1bGxOYW1lOiAn57O757uf566h55CG5ZGYJyxcbiAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICByb2xlczogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdyb2xlLTEnLFxuICAgICAgICAgIGNvZGU6ICdhZG1pbicsXG4gICAgICAgICAgbmFtZTogJ+ezu+e7n+euoeeQhuWRmCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfmi6XmnInns7vnu5/miYDmnInmnYPpmZAnLFxuICAgICAgICAgIHBlcm1pc3Npb25zOiBbXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGlkOiAncGVybS0xJyxcbiAgICAgICAgICAgICAgY29kZTogJ3N5c3RlbTphZG1pbicsXG4gICAgICAgICAgICAgIG5hbWU6ICfns7vnu5/nrqHnkIYnLFxuICAgICAgICAgICAgICB0eXBlOiAnbW9kdWxlJyxcbiAgICAgICAgICAgICAgcmVzb3VyY2U6ICdzeXN0ZW0nLFxuICAgICAgICAgICAgICBhY3Rpb246ICdhZG1pbicsXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF0sXG4gICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfVxuICAgICAgXSxcbiAgICAgIHBlcm1pc3Npb25zOiBbXSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG4gIF1cblxuICBwcml2YXRlIHN0YXRpYyB1c2VyUGFzc3dvcmRzOiBNYXA8c3RyaW5nLCBzdHJpbmc+ID0gbmV3IE1hcChbXG4gICAgWyd1c2VyLTEnLCAnJDJiJDEyJDIweW5ZSUNnV3ZQNXBleVdNZVVOcXVESU5ZRlQ3UTB2QU1tTE9hb1R5MXg3ZHVoaUhteVlpJ10gLy8g5a+G56CBOiBhZG1pbjEyM1xuICBdKVxuXG4gIC8vIOWvhueggeWOhuWPsuiusOW9le+8iOeUqOaIt0lEIC0+IOacgOi/kTXmrKHlr4bnoIHlk4jluIzvvIlcbiAgcHJpdmF0ZSBzdGF0aWMgcGFzc3dvcmRIaXN0b3J5OiBNYXA8c3RyaW5nLCBzdHJpbmdbXT4gPSBuZXcgTWFwKClcblxuICAvLyDlvLrliLbkv67mlLnlr4bnoIHmoIforrDvvIjnlKjmiLdJRCAtPiDmmK/lkKbpnIDopoHlvLrliLbkv67mlLnlr4bnoIHvvIlcbiAgcHJpdmF0ZSBzdGF0aWMgZm9yY2VQYXNzd29yZENoYW5nZTogTWFwPHN0cmluZywgYm9vbGVhbj4gPSBuZXcgTWFwKClcblxuICBwcml2YXRlIHN0YXRpYyBzZXNzaW9uczogVXNlclNlc3Npb25bXSA9IFtdXG4gIHByaXZhdGUgc3RhdGljIGxvZ2luQXR0ZW1wdHM6IE1hcDxzdHJpbmcsIHsgY291bnQ6IG51bWJlcjsgbGFzdEF0dGVtcHQ6IG51bWJlcjsgbG9ja2VkVW50aWw/OiBudW1iZXIgfT4gPSBuZXcgTWFwKClcblxuICBzdGF0aWMgZ2V0VXNlcnMoKTogVXNlcltdIHtcbiAgICByZXR1cm4gWy4uLnRoaXMudXNlcnNdXG4gIH1cblxuICBzdGF0aWMgZ2V0VXNlckJ5SWQoaWQ6IHN0cmluZyk6IFVzZXIgfCB1bmRlZmluZWQge1xuICAgIHJldHVybiB0aGlzLnVzZXJzLmZpbmQodXNlciA9PiB1c2VyLmlkID09PSBpZClcbiAgfVxuXG4gIHN0YXRpYyBnZXRVc2VyQnlVc2VybmFtZSh1c2VybmFtZTogc3RyaW5nKTogVXNlciB8IHVuZGVmaW5lZCB7XG4gICAgcmV0dXJuIHRoaXMudXNlcnMuZmluZCh1c2VyID0+IHVzZXIudXNlcm5hbWUgPT09IHVzZXJuYW1lKVxuICB9XG5cbiAgc3RhdGljIGdldFVzZXJQYXNzd29yZCh1c2VySWQ6IHN0cmluZyk6IHN0cmluZyB8IHVuZGVmaW5lZCB7XG4gICAgcmV0dXJuIHRoaXMudXNlclBhc3N3b3Jkcy5nZXQodXNlcklkKVxuICB9XG5cbiAgc3RhdGljIHNldFVzZXJQYXNzd29yZCh1c2VySWQ6IHN0cmluZywgcGFzc3dvcmRIYXNoOiBzdHJpbmcpOiB2b2lkIHtcbiAgICAvLyDkv53lrZjml6flr4bnoIHliLDljoblj7LorrDlvZVcbiAgICBjb25zdCBjdXJyZW50UGFzc3dvcmQgPSB0aGlzLnVzZXJQYXNzd29yZHMuZ2V0KHVzZXJJZClcbiAgICBpZiAoY3VycmVudFBhc3N3b3JkKSB7XG4gICAgICBjb25zdCBoaXN0b3J5ID0gdGhpcy5wYXNzd29yZEhpc3RvcnkuZ2V0KHVzZXJJZCkgfHwgW11cbiAgICAgIGhpc3RvcnkudW5zaGlmdChjdXJyZW50UGFzc3dvcmQpIC8vIOa3u+WKoOWIsOWOhuWPsuiusOW9leW8gOWktFxuXG4gICAgICAvLyDlj6rkv53nlZnmnIDov5E15qyh5a+G56CBXG4gICAgICBpZiAoaGlzdG9yeS5sZW5ndGggPiA1KSB7XG4gICAgICAgIGhpc3Rvcnkuc3BsaWNlKDUpXG4gICAgICB9XG5cbiAgICAgIHRoaXMucGFzc3dvcmRIaXN0b3J5LnNldCh1c2VySWQsIGhpc3RvcnkpXG4gICAgfVxuXG4gICAgdGhpcy51c2VyUGFzc3dvcmRzLnNldCh1c2VySWQsIHBhc3N3b3JkSGFzaClcbiAgfVxuXG4gIHN0YXRpYyBnZXRQYXNzd29yZEhpc3RvcnkodXNlcklkOiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgcmV0dXJuIHRoaXMucGFzc3dvcmRIaXN0b3J5LmdldCh1c2VySWQpIHx8IFtdXG4gIH1cblxuICBzdGF0aWMgaXNQYXNzd29yZEluSGlzdG9yeSh1c2VySWQ6IHN0cmluZywgcGFzc3dvcmRIYXNoOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBjb25zdCBoaXN0b3J5ID0gdGhpcy5wYXNzd29yZEhpc3RvcnkuZ2V0KHVzZXJJZCkgfHwgW11cbiAgICByZXR1cm4gaGlzdG9yeS5pbmNsdWRlcyhwYXNzd29yZEhhc2gpXG4gIH1cblxuICBzdGF0aWMgc2V0Rm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQ6IHN0cmluZywgZm9yY2U6IGJvb2xlYW4pOiB2b2lkIHtcbiAgICB0aGlzLmZvcmNlUGFzc3dvcmRDaGFuZ2Uuc2V0KHVzZXJJZCwgZm9yY2UpXG4gIH1cblxuICBzdGF0aWMgc2hvdWxkRm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQ6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0aGlzLmZvcmNlUGFzc3dvcmRDaGFuZ2UuZ2V0KHVzZXJJZCkgfHwgZmFsc2VcbiAgfVxuXG4gIHN0YXRpYyBhZGRVc2VyKHVzZXI6IFVzZXIpOiB2b2lkIHtcbiAgICB0aGlzLnVzZXJzLnB1c2godXNlcilcbiAgfVxuXG4gIHN0YXRpYyB1cGRhdGVVc2VyKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8VXNlcj4pOiBib29sZWFuIHtcbiAgICBjb25zdCBpbmRleCA9IHRoaXMudXNlcnMuZmluZEluZGV4KHVzZXIgPT4gdXNlci5pZCA9PT0gaWQpXG4gICAgaWYgKGluZGV4ICE9PSAtMSkge1xuICAgICAgdGhpcy51c2Vyc1tpbmRleF0gPSB7IC4uLnRoaXMudXNlcnNbaW5kZXhdLCAuLi51cGRhdGVzLCB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSB9XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHN0YXRpYyBkZWxldGVVc2VyKGlkOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBjb25zdCBpbmRleCA9IHRoaXMudXNlcnMuZmluZEluZGV4KHVzZXIgPT4gdXNlci5pZCA9PT0gaWQpXG4gICAgaWYgKGluZGV4ICE9PSAtMSkge1xuICAgICAgdGhpcy51c2Vycy5zcGxpY2UoaW5kZXgsIDEpXG4gICAgICB0aGlzLnVzZXJQYXNzd29yZHMuZGVsZXRlKGlkKVxuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICBzdGF0aWMgZ2V0U2Vzc2lvbnMoKTogVXNlclNlc3Npb25bXSB7XG4gICAgcmV0dXJuIFsuLi50aGlzLnNlc3Npb25zXVxuICB9XG5cbiAgc3RhdGljIHVwZGF0ZVNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8VXNlclNlc3Npb24+KTogYm9vbGVhbiB7XG4gICAgY29uc3QgaW5kZXggPSB0aGlzLnNlc3Npb25zLmZpbmRJbmRleChzZXNzaW9uID0+IHNlc3Npb24uaWQgPT09IHNlc3Npb25JZClcbiAgICBpZiAoaW5kZXggIT09IC0xKSB7XG4gICAgICB0aGlzLnNlc3Npb25zW2luZGV4XSA9IHsgLi4udGhpcy5zZXNzaW9uc1tpbmRleF0sIC4uLnVwZGF0ZXMgfVxuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICBzdGF0aWMgZ2V0U2Vzc2lvbkJ5SWQoaWQ6IHN0cmluZyk6IFVzZXJTZXNzaW9uIHwgdW5kZWZpbmVkIHtcbiAgICByZXR1cm4gdGhpcy5zZXNzaW9ucy5maW5kKHNlc3Npb24gPT4gc2Vzc2lvbi5pZCA9PT0gaWQpXG4gIH1cblxuICBzdGF0aWMgZ2V0VXNlclNlc3Npb25zKHVzZXJJZDogc3RyaW5nKTogVXNlclNlc3Npb25bXSB7XG4gICAgcmV0dXJuIHRoaXMuc2Vzc2lvbnMuZmlsdGVyKHNlc3Npb24gPT4gc2Vzc2lvbi51c2VySWQgPT09IHVzZXJJZCAmJiBzZXNzaW9uLmlzQWN0aXZlKVxuICB9XG5cbiAgc3RhdGljIGFkZFNlc3Npb24oc2Vzc2lvbjogVXNlclNlc3Npb24pOiB2b2lkIHtcbiAgICB0aGlzLnNlc3Npb25zLnB1c2goc2Vzc2lvbilcbiAgfVxuXG4gIHN0YXRpYyB1cGRhdGVTZXNzaW9uKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8VXNlclNlc3Npb24+KTogYm9vbGVhbiB7XG4gICAgY29uc3QgaW5kZXggPSB0aGlzLnNlc3Npb25zLmZpbmRJbmRleChzZXNzaW9uID0+IHNlc3Npb24uaWQgPT09IGlkKVxuICAgIGlmIChpbmRleCAhPT0gLTEpIHtcbiAgICAgIHRoaXMuc2Vzc2lvbnNbaW5kZXhdID0geyAuLi50aGlzLnNlc3Npb25zW2luZGV4XSwgLi4udXBkYXRlcyB9XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHN0YXRpYyBkZWxldGVTZXNzaW9uKGlkOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBjb25zdCBpbmRleCA9IHRoaXMuc2Vzc2lvbnMuZmluZEluZGV4KHNlc3Npb24gPT4gc2Vzc2lvbi5pZCA9PT0gaWQpXG4gICAgaWYgKGluZGV4ICE9PSAtMSkge1xuICAgICAgdGhpcy5zZXNzaW9ucy5zcGxpY2UoaW5kZXgsIDEpXG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHN0YXRpYyBnZXRMb2dpbkF0dGVtcHRzKHVzZXJuYW1lOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5sb2dpbkF0dGVtcHRzLmdldCh1c2VybmFtZSkgfHwgeyBjb3VudDogMCwgbGFzdEF0dGVtcHQ6IDAgfVxuICB9XG5cbiAgc3RhdGljIHJlY29yZExvZ2luQXR0ZW1wdCh1c2VybmFtZTogc3RyaW5nLCBzdWNjZXNzOiBib29sZWFuKTogdm9pZCB7XG4gICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKVxuICAgIGNvbnN0IGF0dGVtcHRzID0gdGhpcy5nZXRMb2dpbkF0dGVtcHRzKHVzZXJuYW1lKVxuXG4gICAgaWYgKHN1Y2Nlc3MpIHtcbiAgICAgIC8vIOeZu+W9leaIkOWKn++8jOa4hemZpOWksei0peiusOW9lVxuICAgICAgdGhpcy5sb2dpbkF0dGVtcHRzLmRlbGV0ZSh1c2VybmFtZSlcbiAgICB9IGVsc2Uge1xuICAgICAgLy8g55m75b2V5aSx6LSl77yM5aKe5Yqg6K6h5pWwXG4gICAgICBjb25zdCBuZXdDb3VudCA9IGF0dGVtcHRzLmNvdW50ICsgMVxuICAgICAgY29uc3QgbG9ja2VkVW50aWwgPSBuZXdDb3VudCA+PSA1ID8gbm93ICsgKDMwICogNjAgKiAxMDAwKSA6IHVuZGVmaW5lZCAvLyA15qyh5aSx6LSl6ZSB5a6aMzDliIbpkp9cblxuICAgICAgdGhpcy5sb2dpbkF0dGVtcHRzLnNldCh1c2VybmFtZSwge1xuICAgICAgICBjb3VudDogbmV3Q291bnQsXG4gICAgICAgIGxhc3RBdHRlbXB0OiBub3csXG4gICAgICAgIGxvY2tlZFVudGlsXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBpc0FjY291bnRMb2NrZWQodXNlcm5hbWU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIGNvbnN0IGF0dGVtcHRzID0gdGhpcy5nZXRMb2dpbkF0dGVtcHRzKHVzZXJuYW1lKVxuICAgIHJldHVybiBhdHRlbXB0cy5sb2NrZWRVbnRpbCA/IGF0dGVtcHRzLmxvY2tlZFVudGlsID4gRGF0ZS5ub3coKSA6IGZhbHNlXG4gIH1cbn1cblxuLyoqXG4gKiDorqTor4HmlbDmja7orr/pl67mnI3liqHlrp7njrBcbiAqL1xuZXhwb3J0IGNsYXNzIEF1dGhEYXRhQWNjZXNzU2VydmljZSBpbXBsZW1lbnRzIElBdXRoRGF0YUFjY2VzcyB7XG4gIHByaXZhdGUgc3RhdGljIGluc3RhbmNlOiBBdXRoRGF0YUFjY2Vzc1NlcnZpY2VcblxuICBwcml2YXRlIGNvbnN0cnVjdG9yKCkge1xuICAgIGNvbnNvbGUubG9nKCfwn5SnIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOacjeWKoeWunuS+i+W3suWIm+W7uicpXG4gIH1cblxuICAvKipcbiAgICog6I635Y+W5Y2V5L6L5a6e5L6LXG4gICAqL1xuICBwdWJsaWMgc3RhdGljIGdldEluc3RhbmNlKCk6IEF1dGhEYXRhQWNjZXNzU2VydmljZSB7XG4gICAgaWYgKCFBdXRoRGF0YUFjY2Vzc1NlcnZpY2UuaW5zdGFuY2UpIHtcbiAgICAgIEF1dGhEYXRhQWNjZXNzU2VydmljZS5pbnN0YW5jZSA9IG5ldyBBdXRoRGF0YUFjY2Vzc1NlcnZpY2UoKVxuICAgIH1cbiAgICByZXR1cm4gQXV0aERhdGFBY2Nlc3NTZXJ2aWNlLmluc3RhbmNlXG4gIH1cblxuICAvKipcbiAgICog55So5oi355m75b2VXG4gICAqL1xuICBhc3luYyBsb2dpbihjcmVkZW50aWFsczogTG9naW5SZXF1ZXN0KTogUHJvbWlzZTxBcGlSZXNwb25zZTxMb2dpblJlc3VsdD4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflJAgW0F1dGhEYXRhQWNjZXNzU2VydmljZV0g5byA5aeL5aSE55CG55m75b2V6K+35rGCOicsIGNyZWRlbnRpYWxzLnVzZXJuYW1lKVxuICAgICAgY29uc3QgeyB1c2VybmFtZSwgcGFzc3dvcmQsIHJlbWVtYmVyTWUgfSA9IGNyZWRlbnRpYWxzXG5cbiAgICAgIC8vIOajgOafpei0puaIt+aYr+WQpuiiq+mUgeWumlxuICAgICAgaWYgKE1vY2tEYXRhU3RvcmUuaXNBY2NvdW50TG9ja2VkKHVzZXJuYW1lKSkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UkiBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSDotKbmiLfooqvplIHlrpo6JywgdXNlcm5hbWUpXG4gICAgICAgIE1vY2tEYXRhU3RvcmUucmVjb3JkTG9naW5BdHRlbXB0KHVzZXJuYW1lLCBmYWxzZSlcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiAn6LSm5oi35bey6KKr6ZSB5a6a77yM6K+3MzDliIbpkp/lkI7ph43or5UnLFxuICAgICAgICAgIGNvZGU6IEF1dGhFcnJvckNvZGUuQUNDT1VOVF9MT0NLRUQsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDmn6Xmib7nlKjmiLdcbiAgICAgIGNvbnNvbGUubG9nKCfwn5GkIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOafpeaJvueUqOaItzonLCB1c2VybmFtZSlcbiAgICAgIGNvbnN0IHVzZXIgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJCeVVzZXJuYW1lKHVzZXJuYW1lKVxuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwgW0F1dGhEYXRhQWNjZXNzU2VydmljZV0g55So5oi35LiN5a2Y5ZyoOicsIHVzZXJuYW1lKVxuICAgICAgICBNb2NrRGF0YVN0b3JlLnJlY29yZExvZ2luQXR0ZW1wdCh1c2VybmFtZSwgZmFsc2UpXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+WQjeaIluWvhueggemUmeivrycsXG4gICAgICAgICAgY29kZTogQXV0aEVycm9yQ29kZS5JTlZBTElEX0NSRURFTlRJQUxTLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+KchSBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSDmib7liLDnlKjmiLc6JywgdXNlci5pZCwgdXNlci51c2VybmFtZSlcblxuICAgICAgLy8g5qOA5p+l55So5oi354q25oCBXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSDmo4Dmn6XnlKjmiLfnirbmgIE6JywgdXNlci5zdGF0dXMpXG4gICAgICBpZiAodXNlci5zdGF0dXMgIT09ICdhY3RpdmUnKSB7XG4gICAgICAgIE1vY2tEYXRhU3RvcmUucmVjb3JkTG9naW5BdHRlbXB0KHVzZXJuYW1lLCBmYWxzZSlcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiB1c2VyLnN0YXR1cyA9PT0gJ2xvY2tlZCcgPyAn6LSm5oi35bey6KKr6ZSB5a6aJyA6ICfotKbmiLfmnKrmv4DmtLsnLFxuICAgICAgICAgIGNvZGU6IHVzZXIuc3RhdHVzID09PSAnbG9ja2VkJyA/IEF1dGhFcnJvckNvZGUuQUNDT1VOVF9MT0NLRUQgOiBBdXRoRXJyb3JDb2RlLkFDQ09VTlRfSU5BQ1RJVkUsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDpqozor4Hlr4bnoIFcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SRIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOW8gOWni+mqjOivgeWvhueggScpXG4gICAgICBjb25zdCBzdG9yZWRQYXNzd29yZEhhc2ggPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJQYXNzd29yZCh1c2VyLmlkKVxuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0F1dGhEYXRhQWNjZXNzU2VydmljZV0g6I635Y+W5Yiw55qE5a+G56CB5ZOI5biMOicsIHN0b3JlZFBhc3N3b3JkSGFzaCA/ICflrZjlnKgnIDogJ+S4jeWtmOWcqCcpXG4gICAgICBpZiAoIXN0b3JlZFBhc3N3b3JkSGFzaCB8fCAhYXdhaXQgUGFzc3dvcmRNYW5hZ2VyLnZlcmlmeVBhc3N3b3JkKHBhc3N3b3JkLCBzdG9yZWRQYXNzd29yZEhhc2gpKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwgW0F1dGhEYXRhQWNjZXNzU2VydmljZV0g5a+G56CB6aqM6K+B5aSx6LSlJylcbiAgICAgICAgTW9ja0RhdGFTdG9yZS5yZWNvcmRMb2dpbkF0dGVtcHQodXNlcm5hbWUsIGZhbHNlKVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICAgIG1lc3NhZ2U6ICfnlKjmiLflkI3miJblr4bnoIHplJnor68nLFxuICAgICAgICAgIGNvZGU6IEF1dGhFcnJvckNvZGUuSU5WQUxJRF9DUkVERU5USUFMUyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOeZu+W9leaIkOWKn++8jOa4hemZpOWksei0peiusOW9lVxuICAgICAgY29uc29sZS5sb2coJ+KchSBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSDlr4bnoIHpqozor4HmiJDlip/vvIzlvIDlp4vnlJ/miJDkvJror50nKVxuICAgICAgTW9ja0RhdGFTdG9yZS5yZWNvcmRMb2dpbkF0dGVtcHQodXNlcm5hbWUsIHRydWUpXG5cbiAgICAgIC8vIOeUn+aIkOS8muivnUlE77yI5LiN55Sf5oiQSldUIHRva2Vu77yM5bCG5ZyoQVBJ5bGC5aSE55CG77yJXG4gICAgICBjb25zdCBzZXNzaW9uSWQgPSBgc2Vzc2lvbi0ke0RhdGUubm93KCl9LSR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWBcbiAgICAgIFxuICAgICAgLy8g5pu05paw55So5oi35pyA5ZCO55m75b2V5pe26Ze0XG4gICAgICBNb2NrRGF0YVN0b3JlLnVwZGF0ZVVzZXIodXNlci5pZCwgeyBsYXN0TG9naW5BdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpIH0pXG5cbiAgICAgIC8vIOi/lOWbnueUqOaIt+S/oeaBr+WSjOS8muivnUlE77yM6K6pQVBJ5bGC55Sf5oiQSldUXG4gICAgICBjb25zdCByZXN1bHQ6IExvZ2luUmVzdWx0ID0ge1xuICAgICAgICB1c2VyLFxuICAgICAgICBzZXNzaW9uSWQsIC8vIOaWsOWinuS8muivnUlE5a2X5q61XG4gICAgICAgIHJlbWVtYmVyTWUsIC8vIOaWsOWinuiusOS9j+aIkeagh+W/l1xuICAgICAgICAvLyBhY2Nlc3NUb2tlbiDlkowgcmVmcmVzaFRva2VuIOWwhuWcqEFQSeWxgueUn+aIkFxuICAgICAgICBleHBpcmVzSW46IDM2MDAgLy8gMeWwj+aXtlxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgZGF0YTogcmVzdWx0LFxuICAgICAgICBtZXNzYWdlOiAn55m75b2V5oiQ5YqfJyxcbiAgICAgICAgY29kZTogJ0xPR0lOX1NVQ0NFU1MnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign55m75b2V5aSx6LSlOicsIGVycm9yKVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICBtZXNzYWdlOiAn55m75b2V5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyxcbiAgICAgICAgY29kZTogJ0xPR0lOX0VSUk9SJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOeUqOaIt+eZu+WHulxuICAgKi9cbiAgYXN5bmMgbG9nb3V0KHJlZnJlc2hUb2tlbjogc3RyaW5nKTogUHJvbWlzZTxBcGlSZXNwb25zZTx2b2lkPj4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBUT0RPOiBUb2tlbumqjOivgeW6lOivpeWcqEFQSei3r+eUseWxguWkhOeQhu+8jOi/memHjOeugOWMluWkhOeQhlxuICAgICAgaWYgKCFyZWZyZXNoVG9rZW4pIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiAnVG9rZW7ml6DmlYgnLFxuICAgICAgICAgIGNvZGU6IEF1dGhFcnJvckNvZGUuVE9LRU5fSU5WQUxJRCxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5STIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOWkhOeQhueUqOaIt+eZu+WHuuivt+axgicpXG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICBtZXNzYWdlOiAn55m75Ye65oiQ5YqfJyxcbiAgICAgICAgY29kZTogJ0xPR09VVF9TVUNDRVNTJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+eZu+WHuuWksei0pTonLCBlcnJvcilcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgbWVzc2FnZTogJ+eZu+WHuuWksei0pScsXG4gICAgICAgIGNvZGU6ICdMT0dPVVRfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5Yi35pawVG9rZW5cbiAgICog4pyFIOaetuaehOWQiOinhO+8muS9v+eUqOe7n+S4gFRva2Vu566h55CG5pyN5YqhXG4gICAqL1xuICBhc3luYyByZWZyZXNoVG9rZW4ocmVxdWVzdDogUmVmcmVzaFRva2VuUmVxdWVzdCk6IFByb21pc2U8QXBpUmVzcG9uc2U8VG9rZW5SZXN1bHQ+PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOW8gOWni+WkhOeQhlRva2Vu5Yi35paw6K+35rGCJylcbiAgICAgIGNvbnN0IHsgcmVmcmVzaFRva2VuIH0gPSByZXF1ZXN0XG5cbiAgICAgIC8vIOWcqOacjeWKoeWZqOerr+mqjOivgVJlZnJlc2ggVG9rZW5cbiAgICAgIGNvbnN0IGlzU2VydmVyID0gdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCdcbiAgICAgIGlmICghaXNTZXJ2ZXIpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSBUb2tlbuWIt+aWsOW/hemhu+WcqOacjeWKoeWZqOerr+i/m+ihjCcpXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ1Rva2Vu5Yi35paw5b+F6aG75Zyo5pyN5Yqh5Zmo56uv6L+b6KGMJyxcbiAgICAgICAgICBjb2RlOiAnSU5WQUxJRF9FTlZJUk9OTUVOVCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDinIUg5p625p6E5ZCI6KeE77ya5L2/55So57uf5LiAVG9rZW7nrqHnkIbmnI3liqFcbiAgICAgIGNvbnN0IHsgdG9rZW5NYW5hZ2VtZW50U2VydmljZSB9ID0gYXdhaXQgaW1wb3J0KCcuL1Rva2VuTWFuYWdlbWVudFNlcnZpY2UnKVxuICAgICAgXG4gICAgICAvLyDpqozor4FSZWZyZXNoIFRva2VuXG4gICAgICBjb25zdCByZWZyZXNoUmVzdWx0ID0gYXdhaXQgdG9rZW5NYW5hZ2VtZW50U2VydmljZS52ZXJpZnlSZWZyZXNoVG9rZW4ocmVmcmVzaFRva2VuKVxuICAgICAgaWYgKCFyZWZyZXNoUmVzdWx0LmlzVmFsaWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSBSZWZyZXNoIFRva2Vu6aqM6K+B5aSx6LSlOicsIHJlZnJlc2hSZXN1bHQuZXJyb3IpXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogcmVmcmVzaFJlc3VsdC5lcnJvciB8fCAnUmVmcmVzaCBUb2tlbuaXoOaViOaIluW3sui/h+acnycsXG4gICAgICAgICAgY29kZTogJ1RPS0VOX0lOVkFMSUQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKCFyZWZyZXNoUmVzdWx0LnVzZXJJZCB8fCAhcmVmcmVzaFJlc3VsdC5zZXNzaW9uSWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSBSZWZyZXNoIFRva2VuIHBheWxvYWTml6DmlYgnKVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICAgIG1lc3NhZ2U6ICdSZWZyZXNoIFRva2Vu5qC85byP5peg5pWIJyxcbiAgICAgICAgICBjb2RlOiAnVE9LRU5fSU5WQUxJRCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDpqozor4HnlKjmiLfmmK/lkKblrZjlnKhcbiAgICAgIGNvbnN0IHVzZXIgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJCeUlkKHJlZnJlc2hSZXN1bHQudXNlcklkKVxuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwgW0F1dGhEYXRhQWNjZXNzU2VydmljZV0g55So5oi35LiN5a2Y5ZyoOicsIHJlZnJlc2hSZXN1bHQudXNlcklkKVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICAgIG1lc3NhZ2U6ICfnlKjmiLfkuI3lrZjlnKgnLFxuICAgICAgICAgIGNvZGU6ICdVU0VSX05PVF9GT1VORCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6XnlKjmiLfnirbmgIFcbiAgICAgIGlmICh1c2VyLnN0YXR1cyAhPT0gJ2FjdGl2ZScpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSDnlKjmiLfnirbmgIHkuI3mraPluLg6JywgdXNlci5zdGF0dXMpXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+i0puaIt+eKtuaAgeW8guW4uCcsXG4gICAgICAgICAgY29kZTogJ0FDQ09VTlRfSU5BQ1RJVkUnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g6aqM6K+B5Lya6K+d5piv5ZCm5pyJ5pWIXG4gICAgICBjb25zdCBzZXNzaW9uID0gTW9ja0RhdGFTdG9yZS5nZXRTZXNzaW9uQnlJZChyZWZyZXNoUmVzdWx0LnNlc3Npb25JZClcbiAgICAgIGlmICghc2Vzc2lvbiB8fCAhc2Vzc2lvbi5pc0FjdGl2ZSkge1xuICAgICAgICBjb25zb2xlLmxvZygn4p2MIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOS8muivneaXoOaViOaIluW3sui/h+acnzonLCByZWZyZXNoUmVzdWx0LnNlc3Npb25JZClcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiAn5Lya6K+d5bey6L+H5pyfJyxcbiAgICAgICAgICBjb2RlOiAnU0VTU0lPTl9FWFBJUkVEJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOKchSDmnrbmnoTlkIjop4TvvJrkvb/nlKjnu5/kuIBUb2tlbueuoeeQhuacjeWKoeeUn+aIkOaWsFRva2VuXG4gICAgICBjb25zdCBuZXdBY2Nlc3NUb2tlbiA9IGF3YWl0IHRva2VuTWFuYWdlbWVudFNlcnZpY2UuZ2VuZXJhdGVBY2Nlc3NUb2tlbih7XG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcbiAgICAgICAgdXNlcm5hbWU6IHVzZXIudXNlcm5hbWUsXG4gICAgICAgIHJvbGVzOiB1c2VyLnJvbGVzLm1hcChyb2xlID0+IHJvbGUuY29kZSksXG4gICAgICAgIHBlcm1pc3Npb25zOiB1c2VyLnJvbGVzLmZsYXRNYXAocm9sZSA9PiByb2xlLnBlcm1pc3Npb25zLm1hcChwZXJtID0+IHBlcm0uY29kZSkpLFxuICAgICAgICBzZXNzaW9uSWQ6IHNlc3Npb24uaWRcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IG5ld1JlZnJlc2hUb2tlbiA9IGF3YWl0IHRva2VuTWFuYWdlbWVudFNlcnZpY2UuZ2VuZXJhdGVSZWZyZXNoVG9rZW4odXNlci5pZCwgc2Vzc2lvbi5pZClcblxuICAgICAgLy8g5pu05paw5Lya6K+d55qE5pyA5ZCO5rS75Yqo5pe26Ze0XG4gICAgICBNb2NrRGF0YVN0b3JlLnVwZGF0ZVNlc3Npb24oc2Vzc2lvbi5pZCwge1xuICAgICAgICBsYXN0QWN0aXZpdHk6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSlcblxuICAgICAgY29uc29sZS5sb2coJ+KchSBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSBUb2tlbuWIt+aWsOaIkOWKnzonLCB1c2VyLnVzZXJuYW1lKVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycsXG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBhY2Nlc3NUb2tlbjogbmV3QWNjZXNzVG9rZW4sXG4gICAgICAgICAgcmVmcmVzaFRva2VuOiBuZXdSZWZyZXNoVG9rZW4sXG4gICAgICAgICAgZXhwaXJlc0luOiAzNjAwIC8vIDHlsI/ml7ZcbiAgICAgICAgfSxcbiAgICAgICAgbWVzc2FnZTogJ1Rva2Vu5Yi35paw5oiQ5YqfJyxcbiAgICAgICAgY29kZTogJ1NVQ0NFU1MnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign8J+SpSBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSBUb2tlbuWIt+aWsOW8guW4uDonLCBlcnJvcilcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgbWVzc2FnZTogJ1Rva2Vu5Yi35paw5aSx6LSlJyxcbiAgICAgICAgY29kZTogJ1RPS0VOX1JFRlJFU0hfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog6aqM6K+BVG9rZW7lubbojrflj5bnlKjmiLfkv6Hmga9cbiAgICovXG4gIGFzeW5jIHZhbGlkYXRlVG9rZW4odG9rZW46IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlcj4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gW0F1dGhEYXRhQWNjZXNzU2VydmljZV0g5byA5aeL6aqM6K+BVG9rZW4nKVxuICAgICAgXG4gICAgICAvLyDlnKjmnI3liqHlmajnq6/pqozor4FKV1QgVG9rZW5cbiAgICAgIGNvbnN0IGlzU2VydmVyID0gdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCdcbiAgICAgIGlmICghaXNTZXJ2ZXIpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBbQXV0aERhdGFBY2Nlc3NTZXJ2aWNlXSBUb2tlbumqjOivgeW/hemhu+WcqOacjeWKoeWZqOerr+i/m+ihjCcpXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ1Rva2Vu6aqM6K+B5b+F6aG75Zyo5pyN5Yqh5Zmo56uv6L+b6KGMJyxcbiAgICAgICAgICBjb2RlOiAnSU5WQUxJRF9FTlZJUk9OTUVOVCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDkvb/nlKhUb2tlbk1hbmFnZW1lbnRTZXJ2aWNl6aqM6K+BVG9rZW5cbiAgICAgIGNvbnN0IHRva2VuTWFuYWdlbWVudCA9IG5ldyAoYXdhaXQgaW1wb3J0KCcuL1Rva2VuTWFuYWdlbWVudFNlcnZpY2UnKSkuVG9rZW5NYW5hZ2VtZW50U2VydmljZSgpXG5cbiAgICAgIC8vIOmqjOivgVRva2Vu5bm26Kej5p6QcGF5bG9hZFxuICAgICAgY29uc3QgdG9rZW5WYWxpZGF0aW9uID0gYXdhaXQgdG9rZW5NYW5hZ2VtZW50LnZlcmlmeUFjY2Vzc1Rva2VuKHRva2VuKVxuICAgICAgaWYgKCF0b2tlblZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4p2MIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIFRva2Vu6aqM6K+B5aSx6LSlOicsIHRva2VuVmFsaWRhdGlvbi5lcnJvcilcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiB0b2tlblZhbGlkYXRpb24uZXJyb3IgfHwgJ1Rva2Vu5peg5pWI5oiW5bey6L+H5pyfJyxcbiAgICAgICAgICBjb2RlOiAnVE9LRU5fSU5WQUxJRCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDku47pqozor4Hnu5Pmnpzojrflj5bnlKjmiLfkv6Hmga9cbiAgICAgIGNvbnN0IHBheWxvYWQgPSB0b2tlblZhbGlkYXRpb24ucGF5bG9hZFxuICAgICAgaWYgKCFwYXlsb2FkIHx8ICFwYXlsb2FkLnVzZXJJZCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4p2MIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIFRva2VuIHBheWxvYWTml6DmlYgnKVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICAgIG1lc3NhZ2U6ICdUb2tlbuagvOW8j+aXoOaViCcsXG4gICAgICAgICAgY29kZTogJ1RPS0VOX0lOVkFMSUQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5qC55o2uVG9rZW7kuK3nmoR1c2VySWTojrflj5bnlKjmiLfkv6Hmga9cbiAgICAgIGNvbnN0IHVzZXIgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJCeUlkKHBheWxvYWQudXNlcklkKVxuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwgW0F1dGhEYXRhQWNjZXNzU2VydmljZV0gVG9rZW7kuK3nmoTnlKjmiLfkuI3lrZjlnKg6JywgcGF5bG9hZC51c2VySWQpXG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+S4jeWtmOWcqCcsXG4gICAgICAgICAgY29kZTogJ1VTRVJfTk9UX0ZPVU5EJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeeUqOaIt+eKtuaAgVxuICAgICAgaWYgKHVzZXIuc3RhdHVzICE9PSAnYWN0aXZlJykge1xuICAgICAgICBjb25zb2xlLmxvZygn4p2MIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOeUqOaIt+eKtuaAgeS4jeato+W4uDonLCB1c2VyLnN0YXR1cylcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiAn55So5oi36LSm5oi354q25oCB5byC5bi4JyxcbiAgICAgICAgICBjb2RlOiAnQUNDT1VOVF9JTkFDVElWRScsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDpqozor4HkvJror53mmK/lkKbmnInmlYjvvIjlpoLmnpxUb2tlbuWMheWQq3Nlc3Npb25JZO+8iVxuICAgICAgaWYgKHBheWxvYWQuc2Vzc2lvbklkKSB7XG4gICAgICAgIGNvbnN0IHNlc3Npb24gPSBNb2NrRGF0YVN0b3JlLmdldFNlc3Npb25CeUlkKHBheWxvYWQuc2Vzc2lvbklkKVxuICAgICAgICBpZiAoIXNlc3Npb24gfHwgIXNlc3Npb24uaXNBY3RpdmUpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4p2MIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIOS8muivneaXoOaViOaIluW3sui/h+acnzonLCBwYXlsb2FkLnNlc3Npb25JZClcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICAgIG1lc3NhZ2U6ICfkvJror53lt7Lov4fmnJ8nLFxuICAgICAgICAgICAgY29kZTogJ1NFU1NJT05fRVhQSVJFRCcsXG4gICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgW0F1dGhEYXRhQWNjZXNzU2VydmljZV0gVG9rZW7pqozor4HmiJDlip86JywgdXNlci51c2VybmFtZSlcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiB1c2VyLFxuICAgICAgICBtZXNzYWdlOiAnVG9rZW7pqozor4HmiJDlip8nLFxuICAgICAgICBjb2RlOiAnU1VDQ0VTUycsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfwn5KlIFtBdXRoRGF0YUFjY2Vzc1NlcnZpY2VdIFRva2Vu6aqM6K+B5byC5bi4OicsIGVycm9yKVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICBtZXNzYWdlOiAnVG9rZW7pqozor4HlpLHotKUnLFxuICAgICAgICBjb2RlOiAnVE9LRU5fVkFMSURBVElPTl9FUlJPUicsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIOWFtuS7luaWueazleeahOWunueOsOWwhuWcqOS4i+S4gOS4quaWh+S7tuS4ree7p+e7rS4uLlxuXG4gIC8qKlxuICAgKiDnlJ/miJDor7fmsYJJRFxuICAgKi9cbiAgcHJpdmF0ZSBnZW5lcmF0ZVJlcXVlc3RJZCgpOiBzdHJpbmcge1xuICAgIHJldHVybiBgcmVxLSR7RGF0ZS5ub3coKX0tJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YFxuICB9XG5cbiAgLy8g5Y2g5L2N5pa55rOV77yM5bCG5Zyo5omp5bGV5paH5Lu25Lit5a6e546wXG4gIGFzeW5jIGdldFVzZXJCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXI+PiB7XG4gICAgY29uc3QgdXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQoaWQpXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgIG1lc3NhZ2U6ICfnlKjmiLfkuI3lrZjlnKgnLFxuICAgICAgICBjb2RlOiAnVVNFUl9OT1RfRk9VTkQnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgc3RhdHVzOiAnc3VjY2VzcycsXG4gICAgICBkYXRhOiB1c2VyLFxuICAgICAgbWVzc2FnZTogJ+iOt+WPlueUqOaIt+S/oeaBr+aIkOWKnycsXG4gICAgICBjb2RlOiAnR0VUX1VTRVJfU1VDQ0VTUycsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZ2V0VXNlckJ5VXNlcm5hbWUodXNlcm5hbWU6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlcj4+IHtcbiAgICBjb25zdCB1c2VyID0gTW9ja0RhdGFTdG9yZS5nZXRVc2VyQnlVc2VybmFtZSh1c2VybmFtZSlcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+S4jeWtmOWcqCcsXG4gICAgICAgIGNvZGU6ICdVU0VSX05PVF9GT1VORCcsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgIGRhdGE6IHVzZXIsXG4gICAgICBtZXNzYWdlOiAn6I635Y+W55So5oi35L+h5oGv5oiQ5YqfJyxcbiAgICAgIGNvZGU6ICdHRVRfVVNFUl9TVUNDRVNTJyxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICB9XG4gIH1cblxuICAvLyDnlKjmiLfnrqHnkIbmlrnms5Xlrp7njrBcbiAgYXN5bmMgY3JlYXRlVXNlcih1c2VyRGF0YTogQ3JlYXRlVXNlclJlcXVlc3QpOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXI+PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIOmqjOivgeeUqOaIt+WQjeaYr+WQpuW3suWtmOWcqFxuICAgICAgY29uc3QgZXhpc3RpbmdVc2VyID0gTW9ja0RhdGFTdG9yZS5nZXRVc2VyQnlVc2VybmFtZSh1c2VyRGF0YS51c2VybmFtZSlcbiAgICAgIGlmIChleGlzdGluZ1VzZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiAn55So5oi35ZCN5bey5a2Y5ZyoJyxcbiAgICAgICAgICBjb2RlOiAnVVNFUk5BTUVfRVhJU1RTJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOmqjOivgemCrueuseaYr+WQpuW3suWtmOWcqO+8iOWmguaenOaPkOS+m++8iVxuICAgICAgaWYgKHVzZXJEYXRhLmVtYWlsKSB7XG4gICAgICAgIGNvbnN0IGV4aXN0aW5nRW1haWxVc2VyID0gTW9ja0RhdGFTdG9yZS5nZXRVc2VycygpLmZpbmQodSA9PiB1LmVtYWlsID09PSB1c2VyRGF0YS5lbWFpbClcbiAgICAgICAgaWYgKGV4aXN0aW5nRW1haWxVc2VyKSB7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgICBtZXNzYWdlOiAn6YKu566x5bey5a2Y5ZyoJyxcbiAgICAgICAgICAgIGNvZGU6ICdFTUFJTF9FWElTVFMnLFxuICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDnlJ/miJDnlKjmiLdJRFxuICAgICAgY29uc3QgdXNlcklkID0gYHVzZXItJHtEYXRlLm5vdygpfS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gXG5cbiAgICAgIC8vIOWKoOWvhuWvhueggVxuICAgICAgY29uc3QgeyBoYXNoOiBwYXNzd29yZEhhc2ggfSA9IGF3YWl0IFBhc3N3b3JkTWFuYWdlci5oYXNoUGFzc3dvcmQodXNlckRhdGEucGFzc3dvcmQpXG5cbiAgICAgIC8vIOiOt+WPluinkuiJsuS/oeaBr++8iOeugOWMluWunueOsO+8jOWunumZheW6lOS7juaVsOaNruW6k+iOt+WPlu+8iVxuICAgICAgY29uc3Qgcm9sZXM6IFJvbGVbXSA9IHVzZXJEYXRhLnJvbGVJZHMubWFwKHJvbGVJZCA9PiAoe1xuICAgICAgICBpZDogcm9sZUlkLFxuICAgICAgICBjb2RlOiByb2xlSWQgPT09ICdhZG1pbicgPyAnYWRtaW4nIDogJ3VzZXInLFxuICAgICAgICBuYW1lOiByb2xlSWQgPT09ICdhZG1pbicgPyAn566h55CG5ZGYJyA6ICfmma7pgJrnlKjmiLcnLFxuICAgICAgICBkZXNjcmlwdGlvbjogcm9sZUlkID09PSAnYWRtaW4nID8gJ+ezu+e7n+euoeeQhuWRmOinkuiJsicgOiAn5pmu6YCa55So5oi36KeS6ImyJyxcbiAgICAgICAgcGVybWlzc2lvbnM6IHJvbGVJZCA9PT0gJ2FkbWluJyA/IFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogJ3Blcm0tYWRtaW4nLFxuICAgICAgICAgICAgY29kZTogJ3N5c3RlbTphZG1pbicsXG4gICAgICAgICAgICBuYW1lOiAn57O757uf566h55CGJyxcbiAgICAgICAgICAgIHR5cGU6ICdtb2R1bGUnIGFzIGNvbnN0LFxuICAgICAgICAgICAgcmVzb3VyY2U6ICdzeXN0ZW0nLFxuICAgICAgICAgICAgYWN0aW9uOiAnYWRtaW4nLFxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH1cbiAgICAgICAgXSA6IFtdLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pKVxuXG4gICAgICAvLyDliJvlu7rnlKjmiLflr7nosaFcbiAgICAgIGNvbnN0IG5ld1VzZXI6IFVzZXIgPSB7XG4gICAgICAgIGlkOiB1c2VySWQsXG4gICAgICAgIHVzZXJuYW1lOiB1c2VyRGF0YS51c2VybmFtZSxcbiAgICAgICAgZW1haWw6IHVzZXJEYXRhLmVtYWlsLFxuICAgICAgICBmdWxsTmFtZTogdXNlckRhdGEuZnVsbE5hbWUsXG4gICAgICAgIHBob25lOiB1c2VyRGF0YS5waG9uZSxcbiAgICAgICAgZGVwYXJ0bWVudElkOiB1c2VyRGF0YS5kZXBhcnRtZW50SWQsXG4gICAgICAgIGVtcGxveWVlSWQ6IHVzZXJEYXRhLmVtcGxveWVlSWQsXG4gICAgICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgICAgIHJvbGVzLFxuICAgICAgICBwZXJtaXNzaW9uczogW10sXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfVxuXG4gICAgICAvLyDkv53lrZjnlKjmiLflkozlr4bnoIFcbiAgICAgIE1vY2tEYXRhU3RvcmUuYWRkVXNlcihuZXdVc2VyKVxuICAgICAgTW9ja0RhdGFTdG9yZS5zZXRVc2VyUGFzc3dvcmQodXNlcklkLCBwYXNzd29yZEhhc2gpXG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiBuZXdVc2VyLFxuICAgICAgICBtZXNzYWdlOiAn55So5oi35Yib5bu65oiQ5YqfJyxcbiAgICAgICAgY29kZTogJ0NSRUFURV9VU0VSX1NVQ0NFU1MnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfliJvlu7rnlKjmiLflpLHotKUnLFxuICAgICAgICBjb2RlOiAnQ1JFQVRFX1VTRVJfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBhc3luYyB1cGRhdGVVc2VyKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFVwZGF0ZVVzZXJSZXF1ZXN0KTogUHJvbWlzZTxBcGlSZXNwb25zZTxVc2VyPj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBleGlzdGluZ1VzZXIgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJCeUlkKGlkKVxuICAgICAgaWYgKCFleGlzdGluZ1VzZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgICBtZXNzYWdlOiAn55So5oi35LiN5a2Y5ZyoJyxcbiAgICAgICAgICBjb2RlOiAnVVNFUl9OT1RfRk9VTkQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g6aqM6K+B6YKu566x5ZSv5LiA5oCn77yI5aaC5p6c5pu05paw6YKu566x77yJXG4gICAgICBpZiAodXBkYXRlcy5lbWFpbCAmJiB1cGRhdGVzLmVtYWlsICE9PSBleGlzdGluZ1VzZXIuZW1haWwpIHtcbiAgICAgICAgY29uc3QgZXhpc3RpbmdFbWFpbFVzZXIgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJzKCkuZmluZCh1ID0+IHUuZW1haWwgPT09IHVwZGF0ZXMuZW1haWwgJiYgdS5pZCAhPT0gaWQpXG4gICAgICAgIGlmIChleGlzdGluZ0VtYWlsVXNlcikge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICAgICAgbWVzc2FnZTogJ+mCrueuseW3suWtmOWcqCcsXG4gICAgICAgICAgICBjb2RlOiAnRU1BSUxfRVhJU1RTJyxcbiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5aSE55CG6KeS6Imy5pu05pawXG4gICAgICBsZXQgdXBkYXRlZFJvbGVzID0gZXhpc3RpbmdVc2VyLnJvbGVzXG4gICAgICBpZiAodXBkYXRlcy5yb2xlSWRzKSB7XG4gICAgICAgIHVwZGF0ZWRSb2xlcyA9IHVwZGF0ZXMucm9sZUlkcy5tYXAocm9sZUlkID0+ICh7XG4gICAgICAgICAgaWQ6IHJvbGVJZCxcbiAgICAgICAgICBjb2RlOiByb2xlSWQgPT09ICdhZG1pbicgPyAnYWRtaW4nIDogJ3VzZXInLFxuICAgICAgICAgIG5hbWU6IHJvbGVJZCA9PT0gJ2FkbWluJyA/ICfnrqHnkIblkZgnIDogJ+aZrumAmueUqOaItycsXG4gICAgICAgICAgZGVzY3JpcHRpb246IHJvbGVJZCA9PT0gJ2FkbWluJyA/ICfns7vnu5/nrqHnkIblkZjop5LoibInIDogJ+aZrumAmueUqOaIt+inkuiJsicsXG4gICAgICAgICAgcGVybWlzc2lvbnM6IHJvbGVJZCA9PT0gJ2FkbWluJyA/IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6ICdwZXJtLWFkbWluJyxcbiAgICAgICAgICAgICAgY29kZTogJ3N5c3RlbTphZG1pbicsXG4gICAgICAgICAgICAgIG5hbWU6ICfns7vnu5/nrqHnkIYnLFxuICAgICAgICAgICAgICB0eXBlOiAnbW9kdWxlJyBhcyBjb25zdCxcbiAgICAgICAgICAgICAgcmVzb3VyY2U6ICdzeXN0ZW0nLFxuICAgICAgICAgICAgICBhY3Rpb246ICdhZG1pbicsXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF0gOiBbXSxcbiAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KSlcbiAgICAgIH1cblxuICAgICAgLy8g5pu05paw55So5oi35L+h5oGvXG4gICAgICBjb25zdCB1cGRhdGVEYXRhID0ge1xuICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgICByb2xlczogdXBkYXRlZFJvbGVzLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBzdWNjZXNzID0gTW9ja0RhdGFTdG9yZS51cGRhdGVVc2VyKGlkLCB1cGRhdGVEYXRhKVxuICAgICAgaWYgKCFzdWNjZXNzKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ+abtOaWsOeUqOaIt+Wksei0pScsXG4gICAgICAgICAgY29kZTogJ1VQREFURV9VU0VSX0VSUk9SJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOeUqOaIt+eKtuaAgeWPmOabtOS4uuemgeeUqOaIlumUgeWumu+8jOe7iOatouivpeeUqOaIt+eahOaJgOacieS8muivnVxuICAgICAgaWYgKHVwZGF0ZXMuc3RhdHVzICYmICh1cGRhdGVzLnN0YXR1cyA9PT0gJ2luYWN0aXZlJyB8fCB1cGRhdGVzLnN0YXR1cyA9PT0gJ2xvY2tlZCcpKSB7XG4gICAgICAgIGF3YWl0IHRoaXMudGVybWluYXRlVXNlclNlc3Npb25zKGlkKVxuICAgICAgfVxuXG4gICAgICBjb25zdCB1cGRhdGVkVXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQoaWQpIVxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycsXG4gICAgICAgIGRhdGE6IHVwZGF0ZWRVc2VyLFxuICAgICAgICBtZXNzYWdlOiAn55So5oi35pu05paw5oiQ5YqfJyxcbiAgICAgICAgY29kZTogJ1VQREFURV9VU0VSX1NVQ0NFU1MnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBkYXRhOiBudWxsLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmm7TmlrDnlKjmiLflpLHotKUnLFxuICAgICAgICBjb2RlOiAnVVBEQVRFX1VTRVJfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBhc3luYyBkZWxldGVVc2VyKGlkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPGJvb2xlYW4+PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGV4aXN0aW5nVXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQoaWQpXG4gICAgICBpZiAoIWV4aXN0aW5nVXNlcikge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBmYWxzZSxcbiAgICAgICAgICBtZXNzYWdlOiAn55So5oi35LiN5a2Y5ZyoJyxcbiAgICAgICAgICBjb2RlOiAnVVNFUl9OT1RfRk9VTkQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5piv5ZCm5Li657O757uf566h55CG5ZGY77yI5LiN5YWB6K645Yig6Zmk77yJXG4gICAgICBpZiAoZXhpc3RpbmdVc2VyLnVzZXJuYW1lID09PSAnYWRtaW4nKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IGZhbHNlLFxuICAgICAgICAgIG1lc3NhZ2U6ICfkuI3og73liKDpmaTns7vnu5/nrqHnkIblkZjotKbmiLcnLFxuICAgICAgICAgIGNvZGU6ICdDQU5OT1RfREVMRVRFX0FETUlOJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBNb2NrRGF0YVN0b3JlLmRlbGV0ZVVzZXIoaWQpXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgZGF0YTogc3VjY2VzcyxcbiAgICAgICAgbWVzc2FnZTogc3VjY2VzcyA/ICfnlKjmiLfliKDpmaTmiJDlip8nIDogJ+eUqOaIt+WIoOmZpOWksei0pScsXG4gICAgICAgIGNvZGU6IHN1Y2Nlc3MgPyAnREVMRVRFX1VTRVJfU1VDQ0VTUycgOiAnREVMRVRFX1VTRVJfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBkYXRhOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5Yig6Zmk55So5oi35aSx6LSlJyxcbiAgICAgICAgY29kZTogJ0RFTEVURV9VU0VSX0VSUk9SJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgY3JlYXRlU2Vzc2lvbih1c2VySWQ6IHN0cmluZywgc2Vzc2lvbkRhdGE6IFBhcnRpYWw8VXNlclNlc3Npb24+KTogUHJvbWlzZTxBcGlSZXNwb25zZTxVc2VyU2Vzc2lvbj4+IHtcbiAgICAvLyBUT0RPOiDlrp7njrDkvJror53liJvlu7rpgLvovpFcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01ldGhvZCBub3QgaW1wbGVtZW50ZWQuJylcbiAgfVxuXG4gIGFzeW5jIGdldFNlc3Npb24oc2Vzc2lvbklkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXJTZXNzaW9uPj4ge1xuICAgIC8vIFRPRE86IOWunueOsOS8muivneiOt+WPlumAu+i+kVxuICAgIHRocm93IG5ldyBFcnJvcignTWV0aG9kIG5vdCBpbXBsZW1lbnRlZC4nKVxuICB9XG5cbiAgYXN5bmMgdXBkYXRlU2Vzc2lvbihzZXNzaW9uSWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxVc2VyU2Vzc2lvbj4pOiBQcm9taXNlPEFwaVJlc3BvbnNlPFVzZXJTZXNzaW9uPj4ge1xuICAgIC8vIFRPRE86IOWunueOsOS8muivneabtOaWsOmAu+i+kVxuICAgIHRocm93IG5ldyBFcnJvcignTWV0aG9kIG5vdCBpbXBsZW1lbnRlZC4nKVxuICB9XG5cbiAgYXN5bmMgZGVsZXRlU2Vzc2lvbihzZXNzaW9uSWQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+IHtcbiAgICAvLyBUT0RPOiDlrp7njrDkvJror53liKDpmaTpgLvovpFcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01ldGhvZCBub3QgaW1wbGVtZW50ZWQuJylcbiAgfVxuXG4gIGFzeW5jIGdldFVzZXJTZXNzaW9ucyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlclNlc3Npb25bXT4+IHtcbiAgICAvLyBUT0RPOiDlrp7njrDnlKjmiLfkvJror53ojrflj5bpgLvovpFcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01ldGhvZCBub3QgaW1wbGVtZW50ZWQuJylcbiAgfVxuXG4gIGFzeW5jIGNoYW5nZVBhc3N3b3JkKHVzZXJJZDogc3RyaW5nLCBjdXJyZW50UGFzc3dvcmQ6IHN0cmluZywgbmV3UGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+IHtcbiAgICAvLyBUT0RPOiDlrp7njrDlr4bnoIHkv67mlLnpgLvovpFcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ01ldGhvZCBub3QgaW1wbGVtZW50ZWQuJylcbiAgfVxuXG4gIGFzeW5jIHJlc2V0UGFzc3dvcmQodXNlcklkOiBzdHJpbmcsIG5ld1Bhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPEFwaVJlc3BvbnNlPGJvb2xlYW4+PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJCeUlkKHVzZXJJZClcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBmYWxzZSxcbiAgICAgICAgICBtZXNzYWdlOiAn55So5oi35LiN5a2Y5ZyoJyxcbiAgICAgICAgICBjb2RlOiAnVVNFUl9OT1RfRk9VTkQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g6aqM6K+B5a+G56CB5by65bqmXG4gICAgICBjb25zdCB2YWxpZGF0aW9uID0gUGFzc3dvcmRNYW5hZ2VyLnZhbGlkYXRlUGFzc3dvcmQobmV3UGFzc3dvcmQpXG4gICAgICBpZiAoIXZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBkYXRhOiBmYWxzZSxcbiAgICAgICAgICBtZXNzYWdlOiBg5a+G56CB5LiN56ym5ZCI6KaB5rGCOiAke3ZhbGlkYXRpb24uZmVlZGJhY2suam9pbignLCAnKX1gLFxuICAgICAgICAgIGNvZGU6ICdJTlZBTElEX1BBU1NXT1JEJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOWKoOWvhuaWsOWvhueggVxuICAgICAgY29uc3QgeyBoYXNoOiBwYXNzd29yZEhhc2ggfSA9IGF3YWl0IFBhc3N3b3JkTWFuYWdlci5oYXNoUGFzc3dvcmQobmV3UGFzc3dvcmQpXG5cbiAgICAgIC8vIOajgOafpeWvhueggeaYr+WQpuS4juWOhuWPsuWvhueggemHjeWkjVxuICAgICAgaWYgKE1vY2tEYXRhU3RvcmUuaXNQYXNzd29yZEluSGlzdG9yeSh1c2VySWQsIHBhc3N3b3JkSGFzaCkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogZmFsc2UsXG4gICAgICAgICAgbWVzc2FnZTogJ+aWsOWvhueggeS4jeiDveS4juacgOi/kTXmrKHkvb/nlKjnmoTlr4bnoIHnm7jlkIwnLFxuICAgICAgICAgIGNvZGU6ICdQQVNTV09SRF9SRVVTRUQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5pu05paw5a+G56CBXG4gICAgICBNb2NrRGF0YVN0b3JlLnNldFVzZXJQYXNzd29yZCh1c2VySWQsIHBhc3N3b3JkSGFzaClcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnc3VjY2VzcycsXG4gICAgICAgIGRhdGE6IHRydWUsXG4gICAgICAgIG1lc3NhZ2U6ICflr4bnoIHph43nva7miJDlip8nLFxuICAgICAgICBjb2RlOiAnUkVTRVRfUEFTU1dPUkRfU1VDQ0VTUycsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgIGRhdGE6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICflr4bnoIHph43nva7lpLHotKUnLFxuICAgICAgICBjb2RlOiAnUkVTRVRfUEFTU1dPUkRfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyDmlrDlop7nlKjmiLfliJfooajojrflj5bmlrnms5VcbiAgYXN5bmMgZ2V0VXNlcnNMaXN0KHBhcmFtcz86IHtcbiAgICBwYWdlPzogbnVtYmVyXG4gICAgcGFnZVNpemU/OiBudW1iZXJcbiAgICBzZWFyY2g/OiBzdHJpbmdcbiAgICBzdGF0dXM/OiBVc2VyU3RhdHVzXG4gIH0pOiBQcm9taXNlPEFwaVJlc3BvbnNlPHsgdXNlcnM6IFVzZXJbXSwgdG90YWw6IG51bWJlciB9Pj4ge1xuICAgIHRyeSB7XG4gICAgICBsZXQgdXNlcnMgPSBNb2NrRGF0YVN0b3JlLmdldFVzZXJzKClcblxuICAgICAgLy8g5pCc57Si6L+H5rukXG4gICAgICBpZiAocGFyYW1zPy5zZWFyY2gpIHtcbiAgICAgICAgY29uc3Qgc2VhcmNoVGVybSA9IHBhcmFtcy5zZWFyY2gudG9Mb3dlckNhc2UoKVxuICAgICAgICB1c2VycyA9IHVzZXJzLmZpbHRlcih1c2VyID0+XG4gICAgICAgICAgdXNlci51c2VybmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pIHx8XG4gICAgICAgICAgdXNlci5mdWxsTmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pIHx8XG4gICAgICAgICAgKHVzZXIuZW1haWwgJiYgdXNlci5lbWFpbC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0pKVxuICAgICAgICApXG4gICAgICB9XG5cbiAgICAgIC8vIOeKtuaAgei/h+a7pFxuICAgICAgaWYgKHBhcmFtcz8uc3RhdHVzKSB7XG4gICAgICAgIHVzZXJzID0gdXNlcnMuZmlsdGVyKHVzZXIgPT4gdXNlci5zdGF0dXMgPT09IHBhcmFtcy5zdGF0dXMpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHRvdGFsID0gdXNlcnMubGVuZ3RoXG5cbiAgICAgIC8vIOWIhumhtVxuICAgICAgaWYgKHBhcmFtcz8ucGFnZSAmJiBwYXJhbXM/LnBhZ2VTaXplKSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0ID0gKHBhcmFtcy5wYWdlIC0gMSkgKiBwYXJhbXMucGFnZVNpemVcbiAgICAgICAgY29uc3QgZW5kID0gc3RhcnQgKyBwYXJhbXMucGFnZVNpemVcbiAgICAgICAgdXNlcnMgPSB1c2Vycy5zbGljZShzdGFydCwgZW5kKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgZGF0YTogeyB1c2VycywgdG90YWwgfSxcbiAgICAgICAgbWVzc2FnZTogJ+iOt+WPlueUqOaIt+WIl+ihqOaIkOWKnycsXG4gICAgICAgIGNvZGU6ICdHRVRfVVNFUlNfU1VDQ0VTUycsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pScsXG4gICAgICAgIGNvZGU6ICdHRVRfVVNFUlNfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyDmlrDlop7nlKjmiLfop5LoibLliIbphY3mlrnms5VcbiAgYXN5bmMgYXNzaWduVXNlclJvbGVzKHVzZXJJZDogc3RyaW5nLCByb2xlSWRzOiBzdHJpbmdbXSk6IFByb21pc2U8QXBpUmVzcG9uc2U8VXNlcj4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQodXNlcklkKVxuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+S4jeWtmOWcqCcsXG4gICAgICAgICAgY29kZTogJ1VTRVJfTk9UX0ZPVU5EJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOaehOW7uuinkuiJsuWvueixoe+8iOeugOWMluWunueOsO+8iVxuICAgICAgY29uc3Qgcm9sZXM6IFJvbGVbXSA9IHJvbGVJZHMubWFwKHJvbGVJZCA9PiAoe1xuICAgICAgICBpZDogcm9sZUlkLFxuICAgICAgICBjb2RlOiByb2xlSWQgPT09ICdhZG1pbicgPyAnYWRtaW4nIDogJ3VzZXInLFxuICAgICAgICBuYW1lOiByb2xlSWQgPT09ICdhZG1pbicgPyAn566h55CG5ZGYJyA6ICfmma7pgJrnlKjmiLcnLFxuICAgICAgICBkZXNjcmlwdGlvbjogcm9sZUlkID09PSAnYWRtaW4nID8gJ+ezu+e7n+euoeeQhuWRmOinkuiJsicgOiAn5pmu6YCa55So5oi36KeS6ImyJyxcbiAgICAgICAgcGVybWlzc2lvbnM6IHJvbGVJZCA9PT0gJ2FkbWluJyA/IFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogJ3Blcm0tYWRtaW4nLFxuICAgICAgICAgICAgY29kZTogJ3N5c3RlbTphZG1pbicsXG4gICAgICAgICAgICBuYW1lOiAn57O757uf566h55CGJyxcbiAgICAgICAgICAgIHR5cGU6ICdtb2R1bGUnIGFzIGNvbnN0LFxuICAgICAgICAgICAgcmVzb3VyY2U6ICdzeXN0ZW0nLFxuICAgICAgICAgICAgYWN0aW9uOiAnYWRtaW4nLFxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH1cbiAgICAgICAgXSA6IFtdLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pKVxuXG4gICAgICAvLyDmm7TmlrDnlKjmiLfop5LoibJcbiAgICAgIGNvbnN0IHN1Y2Nlc3MgPSBNb2NrRGF0YVN0b3JlLnVwZGF0ZVVzZXIodXNlcklkLCB7XG4gICAgICAgIHJvbGVzLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSlcblxuICAgICAgaWYgKCFzdWNjZXNzKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogJ+inkuiJsuWIhumFjeWksei0pScsXG4gICAgICAgICAgY29kZTogJ0FTU0lHTl9ST0xFU19FUlJPUicsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBjb25zdCB1cGRhdGVkVXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQodXNlcklkKSFcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiB1cGRhdGVkVXNlcixcbiAgICAgICAgbWVzc2FnZTogJ+inkuiJsuWIhumFjeaIkOWKnycsXG4gICAgICAgIGNvZGU6ICdBU1NJR05fUk9MRVNfU1VDQ0VTUycsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+inkuiJsuWIhumFjeWksei0pScsXG4gICAgICAgIGNvZGU6ICdBU1NJR05fUk9MRVNfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyDnu4jmraLnlKjmiLfmiYDmnInkvJror53nmoTnp4HmnInmlrnms5VcbiAgcHJpdmF0ZSBhc3luYyB0ZXJtaW5hdGVVc2VyU2Vzc2lvbnModXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgLy8g6I635Y+W55So5oi355qE5omA5pyJ5rS76LeD5Lya6K+dXG4gICAgICBjb25zdCB1c2VyU2Vzc2lvbnMgPSBNb2NrRGF0YVN0b3JlLmdldFNlc3Npb25zKCkuZmlsdGVyKFxuICAgICAgICBzZXNzaW9uID0+IHNlc3Npb24udXNlcklkID09PSB1c2VySWQgJiYgc2Vzc2lvbi5pc0FjdGl2ZVxuICAgICAgKVxuXG4gICAgICAvLyDlsIbmiYDmnInkvJror53moIforrDkuLrpnZ7mtLvot4NcbiAgICAgIGZvciAoY29uc3Qgc2Vzc2lvbiBvZiB1c2VyU2Vzc2lvbnMpIHtcbiAgICAgICAgTW9ja0RhdGFTdG9yZS51cGRhdGVTZXNzaW9uKHNlc3Npb24uaWQsIHtcbiAgICAgICAgICBpc0FjdGl2ZTogZmFsc2UsXG4gICAgICAgICAgbGFzdEFjdGl2aXR5QXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICB9KVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhg5bey57uI5q2i55So5oi3ICR7dXNlcklkfSDnmoQgJHt1c2VyU2Vzc2lvbnMubGVuZ3RofSDkuKrkvJror51gKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfnu4jmraLnlKjmiLfkvJror53lpLHotKU6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgLy8g5om56YeP5pu05paw55So5oi354q25oCB55qE5pa55rOVXG4gIGFzeW5jIGJhdGNoVXBkYXRlVXNlclN0YXR1cyh1c2VySWRzOiBzdHJpbmdbXSwgc3RhdHVzOiBVc2VyU3RhdHVzKTogUHJvbWlzZTxBcGlSZXNwb25zZTx7IHN1Y2Nlc3NDb3VudDogbnVtYmVyLCBmYWlsdXJlQ291bnQ6IG51bWJlciB9Pj4ge1xuICAgIHRyeSB7XG4gICAgICBsZXQgc3VjY2Vzc0NvdW50ID0gMFxuICAgICAgbGV0IGZhaWx1cmVDb3VudCA9IDBcbiAgICAgIGNvbnN0IGVycm9yczogc3RyaW5nW10gPSBbXVxuXG4gICAgICBmb3IgKGNvbnN0IHVzZXJJZCBvZiB1c2VySWRzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgdXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQodXNlcklkKVxuICAgICAgICAgIGlmICghdXNlcikge1xuICAgICAgICAgICAgZmFpbHVyZUNvdW50KytcbiAgICAgICAgICAgIGVycm9ycy5wdXNoKGDnlKjmiLcgJHt1c2VySWR9IOS4jeWtmOWcqGApXG4gICAgICAgICAgICBjb250aW51ZVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIOajgOafpeaYr+WQpuS4uuezu+e7n+euoeeQhuWRmO+8iOS4jeWFgeiuuOemgeeUqO+8iVxuICAgICAgICAgIGlmICh1c2VyLnVzZXJuYW1lID09PSAnYWRtaW4nICYmIHN0YXR1cyAhPT0gJ2FjdGl2ZScpIHtcbiAgICAgICAgICAgIGZhaWx1cmVDb3VudCsrXG4gICAgICAgICAgICBlcnJvcnMucHVzaChg5LiN6IO956aB55So57O757uf566h55CG5ZGY6LSm5oi3YClcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgY29uc3Qgc3VjY2VzcyA9IE1vY2tEYXRhU3RvcmUudXBkYXRlVXNlcih1c2VySWQsIHtcbiAgICAgICAgICAgIHN0YXR1cyxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSlcblxuICAgICAgICAgIGlmIChzdWNjZXNzKSB7XG4gICAgICAgICAgICAvLyDlpoLmnpznirbmgIHlj5jmm7TkuLrnpoHnlKjmiJbplIHlrprvvIznu4jmraLor6XnlKjmiLfnmoTmiYDmnInkvJror51cbiAgICAgICAgICAgIGlmIChzdGF0dXMgPT09ICdpbmFjdGl2ZScgfHwgc3RhdHVzID09PSAnbG9ja2VkJykge1xuICAgICAgICAgICAgICBhd2FpdCB0aGlzLnRlcm1pbmF0ZVVzZXJTZXNzaW9ucyh1c2VySWQpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzdWNjZXNzQ291bnQrK1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBmYWlsdXJlQ291bnQrK1xuICAgICAgICAgICAgZXJyb3JzLnB1c2goYOabtOaWsOeUqOaItyAke3VzZXJJZH0g54q25oCB5aSx6LSlYClcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgZmFpbHVyZUNvdW50KytcbiAgICAgICAgICBlcnJvcnMucHVzaChg5aSE55CG55So5oi3ICR7dXNlcklkfSDml7blj5HnlJ/plJnor686ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6ZSZ6K+vJ31gKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiB7IHN1Y2Nlc3NDb3VudCwgZmFpbHVyZUNvdW50IH0sXG4gICAgICAgIG1lc3NhZ2U6IGDmibnph4/mm7TmlrDlrozmiJDvvJrmiJDlip8gJHtzdWNjZXNzQ291bnR9IOS4qu+8jOWksei0pSAke2ZhaWx1cmVDb3VudH0g5LiqJHtlcnJvcnMubGVuZ3RoID4gMCA/IGDjgILplJnor6/vvJoke2Vycm9ycy5qb2luKCc7ICcpfWAgOiAnJ31gLFxuICAgICAgICBjb2RlOiAnQkFUQ0hfVVBEQVRFX1NUQVRVU19TVUNDRVNTJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgbWVzc2FnZTogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5om56YeP5pu05paw55So5oi354q25oCB5aSx6LSlJyxcbiAgICAgICAgY29kZTogJ0JBVENIX1VQREFURV9TVEFUVVNfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyDlvLrliLbkv67mlLnlr4bnoIFcbiAgYXN5bmMgZm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQ6IHN0cmluZywgZm9yY2U6IGJvb2xlYW4gPSB0cnVlKTogUHJvbWlzZTxBcGlSZXNwb25zZTxib29sZWFuPj4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB1c2VyID0gTW9ja0RhdGFTdG9yZS5nZXRVc2VyQnlJZCh1c2VySWQpXG4gICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgZGF0YTogZmFsc2UsXG4gICAgICAgICAgbWVzc2FnZTogJ+eUqOaIt+S4jeWtmOWcqCcsXG4gICAgICAgICAgY29kZTogJ1VTRVJfTk9UX0ZPVU5EJyxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIE1vY2tEYXRhU3RvcmUuc2V0Rm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQsIGZvcmNlKVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgZGF0YTogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogZm9yY2UgPyAn5bey6K6+572u5by65Yi25L+u5pS55a+G56CBJyA6ICflt7Llj5bmtojlvLrliLbkv67mlLnlr4bnoIEnLFxuICAgICAgICBjb2RlOiAnRk9SQ0VfUEFTU1dPUkRfQ0hBTkdFX1NVQ0NFU1MnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBkYXRhOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn6K6+572u5by65Yi25L+u5pS55a+G56CB5aSx6LSlJyxcbiAgICAgICAgY29kZTogJ0ZPUkNFX1BBU1NXT1JEX0NIQU5HRV9FUlJPUicsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIOeUn+aIkOWuieWFqOWvhueggVxuICBhc3luYyBnZW5lcmF0ZVNlY3VyZVBhc3N3b3JkKGxlbmd0aDogbnVtYmVyID0gMTIpOiBQcm9taXNlPEFwaVJlc3BvbnNlPHN0cmluZz4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcGFzc3dvcmQgPSBQYXNzd29yZE1hbmFnZXIuZ2VuZXJhdGVSYW5kb21QYXNzd29yZChsZW5ndGgsIHRydWUpXG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiBwYXNzd29yZCxcbiAgICAgICAgbWVzc2FnZTogJ+WuieWFqOWvhueggeeUn+aIkOaIkOWKnycsXG4gICAgICAgIGNvZGU6ICdHRU5FUkFURV9QQVNTV09SRF9TVUNDRVNTJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgZGF0YTogbnVsbCxcbiAgICAgICAgbWVzc2FnZTogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn55Sf5oiQ5a+G56CB5aSx6LSlJyxcbiAgICAgICAgY29kZTogJ0dFTkVSQVRFX1BBU1NXT1JEX0VSUk9SJyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHJlcXVlc3RJZDogdGhpcy5nZW5lcmF0ZVJlcXVlc3RJZCgpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8g5qOA5p+l55So5oi35piv5ZCm6ZyA6KaB5by65Yi25L+u5pS55a+G56CBXG4gIGFzeW5jIGNoZWNrRm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8QXBpUmVzcG9uc2U8Ym9vbGVhbj4+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlciA9IE1vY2tEYXRhU3RvcmUuZ2V0VXNlckJ5SWQodXNlcklkKVxuICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGRhdGE6IGZhbHNlLFxuICAgICAgICAgIG1lc3NhZ2U6ICfnlKjmiLfkuI3lrZjlnKgnLFxuICAgICAgICAgIGNvZGU6ICdVU0VSX05PVF9GT1VORCcsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBjb25zdCBzaG91bGRGb3JjZSA9IE1vY2tEYXRhU3RvcmUuc2hvdWxkRm9yY2VQYXNzd29yZENoYW5nZSh1c2VySWQpXG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogJ3N1Y2Nlc3MnLFxuICAgICAgICBkYXRhOiBzaG91bGRGb3JjZSxcbiAgICAgICAgbWVzc2FnZTogc2hvdWxkRm9yY2UgPyAn6ZyA6KaB5by65Yi25L+u5pS55a+G56CBJyA6ICfml6DpnIDlvLrliLbkv67mlLnlr4bnoIEnLFxuICAgICAgICBjb2RlOiAnQ0hFQ0tfRk9SQ0VfUEFTU1dPUkRfU1VDQ0VTUycsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICByZXF1ZXN0SWQ6IHRoaXMuZ2VuZXJhdGVSZXF1ZXN0SWQoKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgIGRhdGE6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmo4Dmn6XlvLrliLbkv67mlLnlr4bnoIHnirbmgIHlpLHotKUnLFxuICAgICAgICBjb2RlOiAnQ0hFQ0tfRk9SQ0VfUEFTU1dPUkRfRVJST1InLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgcmVxdWVzdElkOiB0aGlzLmdlbmVyYXRlUmVxdWVzdElkKClcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJBdXRoRXJyb3JDb2RlIiwiUGFzc3dvcmRNYW5hZ2VyIiwiTW9ja0RhdGFTdG9yZSIsImdldFVzZXJzIiwidXNlcnMiLCJnZXRVc2VyQnlJZCIsImlkIiwiZmluZCIsInVzZXIiLCJnZXRVc2VyQnlVc2VybmFtZSIsInVzZXJuYW1lIiwiZ2V0VXNlclBhc3N3b3JkIiwidXNlcklkIiwidXNlclBhc3N3b3JkcyIsImdldCIsInNldFVzZXJQYXNzd29yZCIsInBhc3N3b3JkSGFzaCIsImN1cnJlbnRQYXNzd29yZCIsImhpc3RvcnkiLCJwYXNzd29yZEhpc3RvcnkiLCJ1bnNoaWZ0IiwibGVuZ3RoIiwic3BsaWNlIiwic2V0IiwiZ2V0UGFzc3dvcmRIaXN0b3J5IiwiaXNQYXNzd29yZEluSGlzdG9yeSIsImluY2x1ZGVzIiwic2V0Rm9yY2VQYXNzd29yZENoYW5nZSIsImZvcmNlIiwiZm9yY2VQYXNzd29yZENoYW5nZSIsInNob3VsZEZvcmNlUGFzc3dvcmRDaGFuZ2UiLCJhZGRVc2VyIiwicHVzaCIsInVwZGF0ZVVzZXIiLCJ1cGRhdGVzIiwiaW5kZXgiLCJmaW5kSW5kZXgiLCJ1cGRhdGVkQXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJkZWxldGVVc2VyIiwiZGVsZXRlIiwiZ2V0U2Vzc2lvbnMiLCJzZXNzaW9ucyIsInVwZGF0ZVNlc3Npb24iLCJzZXNzaW9uSWQiLCJzZXNzaW9uIiwiZ2V0U2Vzc2lvbkJ5SWQiLCJnZXRVc2VyU2Vzc2lvbnMiLCJmaWx0ZXIiLCJpc0FjdGl2ZSIsImFkZFNlc3Npb24iLCJkZWxldGVTZXNzaW9uIiwiZ2V0TG9naW5BdHRlbXB0cyIsImxvZ2luQXR0ZW1wdHMiLCJjb3VudCIsImxhc3RBdHRlbXB0IiwicmVjb3JkTG9naW5BdHRlbXB0Iiwic3VjY2VzcyIsIm5vdyIsImF0dGVtcHRzIiwibmV3Q291bnQiLCJsb2NrZWRVbnRpbCIsInVuZGVmaW5lZCIsImlzQWNjb3VudExvY2tlZCIsImVtYWlsIiwiZnVsbE5hbWUiLCJzdGF0dXMiLCJyb2xlcyIsImNvZGUiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJwZXJtaXNzaW9ucyIsInR5cGUiLCJyZXNvdXJjZSIsImFjdGlvbiIsImNyZWF0ZWRBdCIsIk1hcCIsIkF1dGhEYXRhQWNjZXNzU2VydmljZSIsImdldEluc3RhbmNlIiwiaW5zdGFuY2UiLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwiY29uc29sZSIsImxvZyIsInBhc3N3b3JkIiwicmVtZW1iZXJNZSIsImRhdGEiLCJtZXNzYWdlIiwiQUNDT1VOVF9MT0NLRUQiLCJ0aW1lc3RhbXAiLCJyZXF1ZXN0SWQiLCJnZW5lcmF0ZVJlcXVlc3RJZCIsIklOVkFMSURfQ1JFREVOVElBTFMiLCJBQ0NPVU5UX0lOQUNUSVZFIiwic3RvcmVkUGFzc3dvcmRIYXNoIiwidmVyaWZ5UGFzc3dvcmQiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJsYXN0TG9naW5BdCIsInJlc3VsdCIsImV4cGlyZXNJbiIsImVycm9yIiwibG9nb3V0IiwicmVmcmVzaFRva2VuIiwiVE9LRU5fSU5WQUxJRCIsInJlcXVlc3QiLCJpc1NlcnZlciIsInRva2VuTWFuYWdlbWVudFNlcnZpY2UiLCJyZWZyZXNoUmVzdWx0IiwidmVyaWZ5UmVmcmVzaFRva2VuIiwiaXNWYWxpZCIsIm5ld0FjY2Vzc1Rva2VuIiwiZ2VuZXJhdGVBY2Nlc3NUb2tlbiIsIm1hcCIsInJvbGUiLCJmbGF0TWFwIiwicGVybSIsIm5ld1JlZnJlc2hUb2tlbiIsImdlbmVyYXRlUmVmcmVzaFRva2VuIiwibGFzdEFjdGl2aXR5IiwiYWNjZXNzVG9rZW4iLCJ2YWxpZGF0ZVRva2VuIiwidG9rZW4iLCJ0b2tlbk1hbmFnZW1lbnQiLCJUb2tlbk1hbmFnZW1lbnRTZXJ2aWNlIiwidG9rZW5WYWxpZGF0aW9uIiwidmVyaWZ5QWNjZXNzVG9rZW4iLCJwYXlsb2FkIiwiY3JlYXRlVXNlciIsInVzZXJEYXRhIiwiZXhpc3RpbmdVc2VyIiwiZXhpc3RpbmdFbWFpbFVzZXIiLCJ1IiwiaGFzaCIsImhhc2hQYXNzd29yZCIsInJvbGVJZHMiLCJyb2xlSWQiLCJuZXdVc2VyIiwicGhvbmUiLCJkZXBhcnRtZW50SWQiLCJlbXBsb3llZUlkIiwiRXJyb3IiLCJ1cGRhdGVkUm9sZXMiLCJ1cGRhdGVEYXRhIiwidGVybWluYXRlVXNlclNlc3Npb25zIiwidXBkYXRlZFVzZXIiLCJjcmVhdGVTZXNzaW9uIiwic2Vzc2lvbkRhdGEiLCJnZXRTZXNzaW9uIiwiY2hhbmdlUGFzc3dvcmQiLCJuZXdQYXNzd29yZCIsInJlc2V0UGFzc3dvcmQiLCJ2YWxpZGF0aW9uIiwidmFsaWRhdGVQYXNzd29yZCIsImZlZWRiYWNrIiwiam9pbiIsImdldFVzZXJzTGlzdCIsInBhcmFtcyIsInNlYXJjaCIsInNlYXJjaFRlcm0iLCJ0b0xvd2VyQ2FzZSIsInRvdGFsIiwicGFnZSIsInBhZ2VTaXplIiwic3RhcnQiLCJlbmQiLCJzbGljZSIsImFzc2lnblVzZXJSb2xlcyIsInVzZXJTZXNzaW9ucyIsImxhc3RBY3Rpdml0eUF0IiwiYmF0Y2hVcGRhdGVVc2VyU3RhdHVzIiwidXNlcklkcyIsInN1Y2Nlc3NDb3VudCIsImZhaWx1cmVDb3VudCIsImVycm9ycyIsImdlbmVyYXRlU2VjdXJlUGFzc3dvcmQiLCJnZW5lcmF0ZVJhbmRvbVBhc3N3b3JkIiwiY2hlY2tGb3JjZVBhc3N3b3JkQ2hhbmdlIiwic2hvdWxkRm9yY2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dataAccess/AuthDataAccessService.ts\n"));

/***/ })

});