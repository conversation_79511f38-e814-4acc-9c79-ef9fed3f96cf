"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts":
/*!******************************************************!*\
  !*** ./src/services/dataAccess/DataAccessManager.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CACHE_CONFIG: function() { return /* binding */ DEFAULT_CACHE_CONFIG; },\n/* harmony export */   DataAccessManager: function() { return /* binding */ DataAccessManager; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   costCalculations: function() { return /* binding */ costCalculations; },\n/* harmony export */   customers: function() { return /* binding */ customers; },\n/* harmony export */   dataAccessManager: function() { return /* binding */ dataAccessManager; },\n/* harmony export */   employees: function() { return /* binding */ employees; },\n/* harmony export */   inventory: function() { return /* binding */ inventory; },\n/* harmony export */   productionOrders: function() { return /* binding */ productionOrders; },\n/* harmony export */   productionWorkOrders: function() { return /* binding */ productionWorkOrders; },\n/* harmony export */   products: function() { return /* binding */ products; },\n/* harmony export */   workstations: function() { return /* binding */ workstations; }\n/* harmony export */ });\n/* harmony import */ var _ProductDataAccessService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProductDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductDataAccessService.ts\");\n/* harmony import */ var _CustomerDataAccessService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CustomerDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/CustomerDataAccessService.ts\");\n/* harmony import */ var _EmployeeDataAccessService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmployeeDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/EmployeeDataAccessService.ts\");\n/* harmony import */ var _InventoryDataAccessService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InventoryDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/InventoryDataAccessService.ts\");\n/* harmony import */ var _OrderDataAccessService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/OrderDataAccessService.ts\");\n/* harmony import */ var _WorkTimeDataAccessService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./WorkTimeDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/WorkTimeDataAccessService.ts\");\n/* harmony import */ var _ProductionOrderDataAccessService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ProductionOrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductionOrderDataAccessService.ts\");\n/* harmony import */ var _ProductionWorkOrderDataAccessService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProductionWorkOrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductionWorkOrderDataAccessService.ts\");\n/* harmony import */ var _WorkstationDataAccessService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./WorkstationDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/WorkstationDataAccessService.ts\");\n/* harmony import */ var _CostCalculationDataAccessService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CostCalculationDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/CostCalculationDataAccessService.ts\");\n/* harmony import */ var _AuthDataAccessService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./AuthDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/AuthDataAccessService.ts\");\n/* harmony import */ var _RoleDataAccessService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./RoleDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/RoleDataAccessService.ts\");\n/* harmony import */ var _TokenManagementService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TokenManagementService */ \"(app-pages-browser)/./src/services/dataAccess/TokenManagementService.ts\");\n/* harmony import */ var _DataChangeNotifier__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./DataChangeNotifier */ \"(app-pages-browser)/./src/services/dataAccess/DataChangeNotifier.ts\");\n/* harmony import */ var _PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./PerformanceOptimizer */ \"(app-pages-browser)/./src/services/dataAccess/PerformanceOptimizer.ts\");\n/* harmony import */ var _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./DataAccessPerformanceMonitor */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessPerformanceMonitor.ts\");\n/* harmony import */ var _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/concurrencyControl */ \"(app-pages-browser)/./src/utils/concurrencyControl.ts\");\n/* harmony import */ var _DataAccessLayer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./DataAccessLayer */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessLayer.ts\");\n/* harmony import */ var _utils_business__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/business */ \"(app-pages-browser)/./src/utils/business/index.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/**\r\n * 统一数据访问管理器\r\n * 提供单一入口点访问所有数据服务，实现标准化的跨模块数据调用\r\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// ✅ 架构合规：已移除独立CacheStrategyManager，使用内置缓存系统\n\n\n// ✅ 架构合规：移除违规的独立缓存管理器，使用内置缓存\n\n// 优先级同步服务已删除\n\n\n/**\r\n * 默认缓存配置 - 优化版\r\n */ const DEFAULT_CACHE_CONFIG = {\n    enableCaching: true,\n    defaultTTL: 5 * 60 * 1000,\n    maxSize: 1000,\n    strategies: {\n        orders: {\n            ttl: 2 * 60 * 1000,\n            enabled: true,\n            priority: \"high\",\n            maxEntries: 200\n        },\n        products: {\n            ttl: 10 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 300\n        },\n        workstations: {\n            ttl: 30 * 1000,\n            enabled: true,\n            priority: \"critical\",\n            maxEntries: 50\n        },\n        statistics: {\n            ttl: 30 * 1000,\n            enabled: true,\n            priority: \"low\",\n            maxEntries: 100\n        },\n        customers: {\n            ttl: 15 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 150\n        },\n        employees: {\n            ttl: 30 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 100\n        } // 30分钟\n    },\n    monitoring: {\n        enabled: true,\n        reportInterval: 60000 // 1分钟\n    },\n    // ✅ 优化：智能缓存管理配置\n    smartEviction: {\n        enabled: true,\n        algorithm: \"adaptive\",\n        thresholds: {\n            memoryWarning: 0.7,\n            memoryCritical: 0.85 // 85%内存使用率紧急清理\n        }\n    },\n    // ✅ 优化：缓存预热配置\n    prewarming: {\n        enabled: true,\n        strategies: [\n            \"workstations\",\n            \"orders\",\n            \"products\"\n        ],\n        scheduleInterval: 30000 // 30秒预热间隔\n    }\n};\n/**\r\n * 默认配置 - 简化版\r\n */ const DEFAULT_CONFIG = {\n    enableLogging: true,\n    enableCaching: true,\n    defaultTTL: 5 * 60 * 1000,\n    maxCacheSize: 1000,\n    retryAttempts: 3,\n    retryDelay: 1000 // 1秒\n};\n/**\r\n * 统一数据访问管理器\r\n */ class DataAccessManager {\n    /**\r\n   * 获取单例实例\r\n   */ static getInstance(config) {\n        if (!DataAccessManager.instance) {\n            DataAccessManager.instance = new DataAccessManager(config);\n        }\n        return DataAccessManager.instance;\n    }\n    // 优先级同步服务已删除\n    /**\r\n   * 记录访问日志\r\n   */ logAccess(service, method, params, duration, success, error, fromCache) {\n        if (!this.config.enableLogging) return;\n        const log = {\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_18__.timestampGenerator.now(),\n            service,\n            method,\n            params,\n            duration,\n            success,\n            error\n        };\n        this.accessLogs.push(log);\n        // 保持最近1000条日志\n        if (this.accessLogs.length > 1000) {\n            this.accessLogs = this.accessLogs.slice(-1000);\n        }\n        // 🔧 优化：使用批量报告代替单个日志\n        if (true) {\n            this.addToPerformanceBatch(service, method, duration, success, fromCache || false);\n        }\n    }\n    // 🔧 优化：批量性能报告方法\n    /**\r\n   * 添加到性能批次\r\n   */ addToPerformanceBatch(service, method, duration, success, fromCache) {\n        this.performanceBatch.push({\n            service,\n            method,\n            duration,\n            success,\n            fromCache,\n            timestamp: Date.now()\n        });\n        // 启动批量报告定时器\n        if (!this.batchReportTimer) {\n            this.batchReportTimer = setTimeout(()=>{\n                this.flushPerformanceBatch();\n            }, this.BATCH_REPORT_INTERVAL);\n        }\n    }\n    /**\r\n   * 输出批量性能报告\r\n   */ flushPerformanceBatch() {\n        if (this.performanceBatch.length === 0) {\n            this.batchReportTimer = null;\n            return;\n        }\n        // 统计数据\n        const stats = {\n            totalOperations: this.performanceBatch.length,\n            successCount: this.performanceBatch.filter((op)=>op.success).length,\n            cacheHits: this.performanceBatch.filter((op)=>op.fromCache).length,\n            averageTime: Math.round(this.performanceBatch.reduce((sum, op)=>sum + op.duration, 0) / this.performanceBatch.length),\n            services: new Set(this.performanceBatch.map((op)=>op.service)).size,\n            timeRange: {\n                start: new Date(Math.min(...this.performanceBatch.map((op)=>op.timestamp))).toLocaleTimeString(),\n                end: new Date(Math.max(...this.performanceBatch.map((op)=>op.timestamp))).toLocaleTimeString()\n            }\n        };\n        console.log(\"\\uD83D\\uDCCA [DataAccessManager] 批量性能报告:\", stats);\n        // 清空批次\n        this.performanceBatch = [];\n        this.batchReportTimer = null;\n    }\n    // 性能优化支持方法\n    /**\r\n   * 判断是否应该使用缓存\r\n   */ shouldUseCache(method) {\n        // 🔧 临时修复：禁用getById的缓存，避免ID混淆问题\n        if (method === \"getById\") {\n            return false;\n        }\n        // 读操作使用缓存，写操作不使用\n        const readMethods = [\n            \"get\",\n            \"find\",\n            \"search\",\n            \"list\",\n            \"statistics\",\n            \"utilization\"\n        ];\n        return readMethods.some((readMethod)=>method.toLowerCase().includes(readMethod));\n    }\n    /**\r\n   * 判断是否应该缓存结果\r\n   */ shouldCacheResult(method, result) {\n        // 成功的读操作结果才缓存\n        return this.shouldUseCache(method) && (result === null || result === void 0 ? void 0 : result.status) === \"success\";\n    }\n    /**\r\n   * 生成缓存键\r\n   * 🔧 修复：统一缓存键格式，确保与清理模式匹配\r\n   */ generateCacheKey(service, method, params) {\n        // 🔧 修复：使用冒号分隔符，与WorkstationUpdateService的清理模式保持一致\n        const baseKey = \"\".concat(service, \":\").concat(method);\n        // 🔧 修复：标准化参数处理，确保键的一致性\n        if (!params || typeof params === \"object\" && Object.keys(params).length === 0) {\n            return baseKey;\n        }\n        // 对参数进行标准化处理\n        let paramStr;\n        if (typeof params === \"object\") {\n            // 对对象参数进行排序，确保键的一致性\n            const sortedParams = Object.keys(params).sort().reduce((result, key)=>{\n                result[key] = params[key];\n                return result;\n            }, {});\n            paramStr = JSON.stringify(sortedParams);\n        } else {\n            paramStr = String(params);\n        }\n        return \"\".concat(baseKey, \":\").concat(Buffer.from(paramStr).toString(\"base64\").slice(0, 32));\n    }\n    /**\r\n   * 从服务名获取数据类型\r\n   */ getDataTypeFromService(service) {\n        if (service.includes(\"ProductionOrder\")) return \"orders\";\n        if (service.includes(\"ProductionWorkOrder\")) return \"workOrders\";\n        if (service.includes(\"OrderService\") || service.includes(\"SalesOrder\")) return \"orders\" // 🔧 新增：支持销售订单\n        ;\n        if (service.includes(\"Workstation\")) return \"workstations\";\n        return \"statistics\";\n    }\n    /**\r\n   * 获取访问频率（简化实现）\r\n   */ getAccessFrequency(operationKey) {\n        // 从访问日志中统计频率\n        const recentLogs = this.accessLogs.filter((log)=>Date.now() - new Date(log.timestamp).getTime() < 5 * 60 * 1000 // 最近5分钟\n        );\n        return recentLogs.filter((log)=>\"\".concat(log.service, \".\").concat(log.method) === operationKey).length;\n    }\n    /**\r\n   * 判断是否应该预加载\r\n   */ shouldPreload(method, result) {\n        var _result_data_items, _result_data;\n        // 获取列表数据时触发预加载\n        return method.includes(\"getAll\") && (result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : (_result_data_items = _result_data.items) === null || _result_data_items === void 0 ? void 0 : _result_data_items.length) > 0;\n    }\n    // ==================== 内置缓存系统核心方法 ====================\n    /**\r\n   * 从缓存获取数据\r\n   */ getFromCache(key) {\n        const entry = this.cache.get(key);\n        if (!entry) return null;\n        // 检查是否过期\n        if (Date.now() > entry.expiresAt) {\n            this.cache.delete(key);\n            return null;\n        }\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return entry.data;\n    }\n    /**\r\n   * 设置缓存数据 - 优化版\r\n   */ setToCache(key, data, service, method) {\n        // ✅ 优化：智能缓存容量管理\n        if (this.cache.size >= this.cacheConfig.maxSize) {\n            this.smartEvictEntries();\n        }\n        const strategy = this.cacheConfig.strategies[service] || {\n            ttl: this.cacheConfig.defaultTTL,\n            priority: \"medium\"\n        };\n        // ✅ 优化：智能数据分析\n        const dataType = this.getDataType(service, method);\n        const priority = strategy.priority || \"medium\";\n        const estimatedSize = this.estimateDataSize(data);\n        const accessPattern = this.predictAccessPattern(key, service);\n        const entry = {\n            data,\n            expiresAt: Date.now() + strategy.ttl,\n            accessCount: 1,\n            lastAccessed: Date.now(),\n            createdAt: Date.now(),\n            // ✅ 优化：智能缓存属性\n            priority,\n            dataType,\n            estimatedSize,\n            accessPattern,\n            refreshable: this.isRefreshableData(service, method)\n        };\n        this.cache.set(key, entry);\n        // ✅ 优化：记录访问历史\n        this.recordCacheAccess(key);\n    }\n    /**\r\n   * ✅ 优化：智能缓存清理策略\r\n   */ smartEvictEntries() {\n        const entries = Array.from(this.cache.entries());\n        const now = Date.now();\n        // 根据配置的算法选择清理策略\n        switch(this.cacheConfig.smartEviction.algorithm){\n            case \"lru\":\n                this.evictByLRU(entries);\n                break;\n            case \"lfu\":\n                this.evictByLFU(entries);\n                break;\n            case \"adaptive\":\n            default:\n                this.evictAdaptive(entries, now);\n                break;\n        }\n        this.cacheStats.evictions++;\n    }\n    /**\r\n   * ✅ 优化：自适应缓存清理\r\n   */ evictAdaptive(entries, now) {\n        // 综合考虑优先级、访问频率、大小等因素\n        const scored = entries.map((param)=>{\n            let [key, entry] = param;\n            let score = 0;\n            // 优先级评分 (优先级越高分数越高，越不容易被清理)\n            const priorityScores = {\n                critical: 100,\n                high: 70,\n                medium: 40,\n                low: 10\n            };\n            score += priorityScores[entry.priority] || 40;\n            // 访问频率评分\n            const age = (now - entry.createdAt) / (24 * 60 * 60 * 1000 // 天数\n            );\n            const frequency = entry.accessCount / Math.max(age, 0.1);\n            score += Math.min(frequency * 10, 50);\n            // 最近访问评分\n            const lastAccessAge = (now - entry.lastAccessed) / (60 * 60 * 1000 // 小时\n            );\n            score += Math.max(50 - lastAccessAge * 2, 0);\n            // 大小惩罚（大数据降低分数）\n            score -= Math.min(entry.estimatedSize / 1000, 20);\n            return {\n                key,\n                entry,\n                score\n            };\n        });\n        // 按分数排序，删除分数最低的条目\n        scored.sort((a, b)=>a.score - b.score);\n        const deleteCount = Math.max(Math.floor(entries.length * 0.15), 1);\n        for(let i = 0; i < deleteCount && i < scored.length; i++){\n            this.cache.delete(scored[i].key);\n            this.cacheAccessHistory.delete(scored[i].key);\n        }\n    }\n    /**\r\n   * ✅ 优化：LRU清理策略\r\n   */ evictByLRU(entries) {\n        entries.sort((a, b)=>a[1].lastAccessed - b[1].lastAccessed);\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n            this.cacheAccessHistory.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * ✅ 优化：LFU清理策略\r\n   */ evictByLFU(entries) {\n        entries.sort((a, b)=>a[1].accessCount - b[1].accessCount);\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n            this.cacheAccessHistory.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * 清理过期缓存 - 保留原有方法作为备用\r\n   */ evictOldestEntries() {\n        const entries = Array.from(this.cache.entries());\n        entries.sort((a, b)=>a[1].lastAccessed - b[1].lastAccessed);\n        // 删除最旧的10%条目\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * 统一的缓存执行方法\r\n   */ async executeWithCache(service, method, params, executor) {\n        // ✅ 架构合规：使用内置缓存系统决策\n        if (!this.config.enableCaching) {\n            return executor();\n        }\n        // 简化的缓存决策：读操作才缓存\n        const readMethods = [\n            \"get\",\n            \"find\",\n            \"search\",\n            \"list\",\n            \"statistics\"\n        ];\n        const shouldCache = readMethods.some((readMethod)=>method.toLowerCase().includes(readMethod));\n        if (!shouldCache) {\n            return executor();\n        }\n        const cacheKey = this.generateCacheKey(service, method, params);\n        // 请求去重\n        if (this.requestCache.has(cacheKey)) {\n            return this.requestCache.get(cacheKey);\n        }\n        // ✅ 架构合规：使用DataAccessManager内置缓存系统\n        const cachedEntry = this.cache.get(cacheKey);\n        if (cachedEntry && Date.now() < cachedEntry.expiresAt) {\n            this.cacheStats.hits++;\n            // ✅ 优化：更新访问信息\n            cachedEntry.accessCount++;\n            cachedEntry.lastAccessed = Date.now();\n            this.recordCacheAccess(cacheKey);\n            return cachedEntry.data;\n        }\n        // 执行请求\n        const requestPromise = executor();\n        this.requestCache.set(cacheKey, requestPromise);\n        try {\n            const result = await requestPromise;\n            this.cacheStats.misses++;\n            // ✅ 架构合规：使用DataAccessManager内置缓存系统设置缓存\n            if (this.shouldCacheResult(method, result)) {\n                this.setToCache(cacheKey, result, service, method);\n            }\n            return result;\n        } finally{\n            this.requestCache.delete(cacheKey);\n        }\n    }\n    // ==================== ✅ 优化：智能缓存辅助方法 ====================\n    /**\r\n   * ✅ 优化：初始化高级缓存功能\r\n   */ initializeAdvancedCaching() {\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 启动智能缓存管理\");\n        // 启动缓存预热\n        if (this.cacheConfig.prewarming.enabled) {\n            this.startCachePrewarming();\n        }\n        // 启动定期内存检查\n        this.startMemoryMonitoring();\n        console.log(\"✅ [DataAccessManager] 智能缓存管理已启动\");\n    }\n    /**\r\n   * ✅ 优化：启动缓存预热\r\n   */ startCachePrewarming() {\n        if (this.prewarmTimer) {\n            clearInterval(this.prewarmTimer);\n        }\n        this.prewarmTimer = setInterval(()=>{\n            this.performCachePrewarming();\n        }, this.cacheConfig.prewarming.scheduleInterval);\n        // 立即执行一次预热\n        setTimeout(()=>this.performCachePrewarming(), 5000) // 5秒后开始\n        ;\n    }\n    /**\r\n   * ✅ 优化：执行缓存预热\r\n   */ async performCachePrewarming() {\n        if (this.memoryPressure) {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 内存压力过高，跳过预热\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDD25 [DataAccessManager] 开始缓存预热\");\n        try {\n            for (const strategy of this.cacheConfig.prewarming.strategies){\n                await this.prewarmStrategy(strategy);\n            }\n        } catch (error) {\n            console.error(\"❌ [DataAccessManager] 缓存预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热特定策略\r\n   */ async prewarmStrategy(strategy) {\n        switch(strategy){\n            case \"workstations\":\n                await this.prewarmWorkstations();\n                break;\n            case \"orders\":\n                await this.prewarmOrders();\n                break;\n            case \"products\":\n                await this.prewarmProducts();\n                break;\n        }\n    }\n    /**\r\n   * ✅ 优化：预热工位数据\r\n   */ async prewarmWorkstations() {\n        try {\n            await this.workstations.getActiveWorkstations();\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 工位数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 工位数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热订单数据\r\n   */ async prewarmOrders() {\n        try {\n            await this.orders.getAll({\n                limit: 50\n            }) // 最近50个订单\n            ;\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 订单数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 订单数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热产品数据\r\n   */ async prewarmProducts() {\n        try {\n            await this.products.getActive();\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 产品数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 产品数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：启动内存监控\r\n   */ startMemoryMonitoring() {\n        setInterval(()=>{\n            this.checkMemoryPressure();\n        }, 30000) // 每30秒检查一次\n        ;\n    }\n    /**\r\n   * ✅ 优化：检查内存压力\r\n   */ checkMemoryPressure() {\n        const now = Date.now();\n        if (now - this.lastMemoryCheck < 10000) return; // 10秒内不重复检查\n        this.lastMemoryCheck = now;\n        try {\n            // 简单的内存压力检测（基于缓存大小）\n            const cacheMemoryRatio = this.cache.size / this.cacheConfig.maxSize;\n            if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryCritical) {\n                this.memoryPressure = true;\n                this.handleCriticalMemoryPressure();\n            } else if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryWarning) {\n                this.memoryPressure = false;\n                this.handleMemoryWarning();\n            } else {\n                this.memoryPressure = false;\n            }\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 内存压力检查失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：处理紧急内存压力\r\n   */ handleCriticalMemoryPressure() {\n        console.warn(\"\\uD83D\\uDEA8 [DataAccessManager] 检测到紧急内存压力，执行强制清理\");\n        // 强制清理缓存\n        this.smartEvictEntries();\n        // 暂停预热\n        if (this.prewarmTimer) {\n            clearInterval(this.prewarmTimer);\n            this.prewarmTimer = null;\n        }\n    }\n    /**\r\n   * ✅ 优化：处理内存警告\r\n   */ handleMemoryWarning() {\n        console.warn(\"⚠️ [DataAccessManager] 检测到内存压力警告，执行适度清理\");\n        // 清理低优先级缓存\n        this.cleanupLowPriorityCache();\n    }\n    /**\r\n   * ✅ 优化：清理低优先级缓存\r\n   */ cleanupLowPriorityCache() {\n        const entries = Array.from(this.cache.entries());\n        let deletedCount = 0;\n        for (const [key, entry] of entries){\n            if (entry.priority === \"low\" && Date.now() > entry.expiresAt - 60000) {\n                this.cache.delete(key);\n                this.cacheAccessHistory.delete(key);\n                deletedCount++;\n            }\n        }\n        console.log(\"\\uD83E\\uDDF9 [DataAccessManager] 清理了\".concat(deletedCount, \"个低优先级缓存条目\"));\n    }\n    /**\r\n   * ✅ 优化：获取数据类型\r\n   */ getDataType(service, method) {\n        if (!method) return service;\n        if (method.includes(\"statistics\") || method.includes(\"utilization\")) return \"statistics\";\n        if (method.includes(\"list\") || method.includes(\"getAll\")) return \"list\";\n        if (method.includes(\"getById\") || method.includes(\"getBy\")) return \"detail\";\n        return service;\n    }\n    /**\r\n   * ✅ 优化：预测访问模式\r\n   */ predictAccessPattern(key, service) {\n        const history = this.cacheAccessHistory.get(key) || [];\n        if (history.length < 2) {\n            // 根据服务类型预测\n            if (service === \"workstations\" || service === \"statistics\") return \"frequent\";\n            if (service === \"orders\" || service === \"products\") return \"occasional\";\n            return \"rare\";\n        }\n        // 基于历史访问频率\n        const recentAccesses = history.filter((time)=>Date.now() - time < 60 * 60 * 1000).length // 1小时内\n        ;\n        if (recentAccesses > 10) return \"frequent\";\n        if (recentAccesses > 3) return \"occasional\";\n        return \"rare\";\n    }\n    /**\r\n   * ✅ 优化：估算数据大小\r\n   */ estimateDataSize(data) {\n        try {\n            const jsonString = JSON.stringify(data);\n            return jsonString.length * 2 // 大致估算，考虑Unicode字符\n            ;\n        } catch (e) {\n            return 1000 // 默认估算\n            ;\n        }\n    }\n    /**\r\n   * ✅ 优化：判断数据是否可刷新\r\n   */ isRefreshableData(service, method) {\n        // 统计数据、状态数据等是可刷新的\n        if ((method === null || method === void 0 ? void 0 : method.includes(\"statistics\")) || (method === null || method === void 0 ? void 0 : method.includes(\"status\")) || (method === null || method === void 0 ? void 0 : method.includes(\"utilization\"))) {\n            return true;\n        }\n        // 工位数据是频繁变化的\n        if (service === \"workstations\") return true;\n        return false;\n    }\n    /**\r\n   * ✅ 优化：记录缓存访问\r\n   */ recordCacheAccess(key) {\n        const history = this.cacheAccessHistory.get(key) || [];\n        history.push(Date.now());\n        // 只保留最近100次访问记录\n        if (history.length > 100) {\n            history.splice(0, history.length - 100);\n        }\n        this.cacheAccessHistory.set(key, history);\n    }\n    /**\r\n   * ✅ 架构合规：获取缓存TTL\r\n   */ getCacheTTL(service, method) {\n        // 根据服务类型和方法返回不同的TTL\n        const serviceConfig = {\n            orders: 2 * 60 * 1000,\n            products: 10 * 60 * 1000,\n            workstations: 30 * 1000,\n            statistics: 30 * 1000,\n            customers: 15 * 60 * 1000,\n            employees: 30 * 60 * 1000 // 员工数据: 30分钟\n        };\n        return serviceConfig[service] || this.config.defaultTTL || 5 * 60 * 1000 // 默认5分钟\n        ;\n    }\n    /**\r\n   * 初始化性能监控\r\n   */ initializePerformanceMonitoring() {\n        this.performanceMetrics = {\n            totalCalls: 0,\n            successCalls: 0,\n            errorCalls: 0,\n            averageResponseTime: 0,\n            minResponseTime: Infinity,\n            maxResponseTime: 0,\n            cacheHitRate: 0,\n            slowQueries: [],\n            methodStats: new Map(),\n            hourlyStats: []\n        };\n        // 启动定期清理任务\n        this.startPerformanceCleanupTask();\n    }\n    /**\r\n   * 启动性能数据清理任务\r\n   */ startPerformanceCleanupTask() {\n        // 每小时清理一次过期的性能数据\n        setInterval(()=>{\n            this.cleanupPerformanceData();\n        }, 60 * 60 * 1000) // 1小时\n        ;\n    }\n    /**\r\n   * 清理过期的性能数据\r\n   */ cleanupPerformanceData() {\n        const now = Date.now();\n        const oneHourAgo = now - 60 * 60 * 1000;\n        const oneDayAgo = now - 24 * 60 * 60 * 1000;\n        // 清理最近调用记录（保留1小时）\n        this.recentCalls = this.recentCalls.filter((call)=>call.timestamp > oneHourAgo);\n        // 清理慢查询记录（保留1天，最多100条）\n        this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.filter((query)=>query.timestamp > oneDayAgo).slice(-100);\n        // 更新小时统计\n        this.updateHourlyStats();\n    }\n    /**\r\n   * 更新小时统计\r\n   */ updateHourlyStats() {\n        const now = new Date();\n        const currentHour = \"\".concat(now.getFullYear(), \"-\").concat(String(now.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(now.getDate()).padStart(2, \"0\"), \" \").concat(String(now.getHours()).padStart(2, \"0\"), \":00\");\n        const oneHourAgo = Date.now() - 60 * 60 * 1000;\n        const hourCalls = this.recentCalls.filter((call)=>call.timestamp > oneHourAgo);\n        if (hourCalls.length > 0) {\n            const totalTime = hourCalls.reduce((sum, call)=>sum + call.duration, 0);\n            const errorCount = hourCalls.filter((call)=>call.status === \"error\").length;\n            const hourStat = {\n                hour: currentHour,\n                calls: hourCalls.length,\n                averageTime: totalTime / hourCalls.length,\n                errorRate: errorCount / hourCalls.length\n            };\n            // 更新或添加当前小时的统计\n            const existingIndex = this.performanceMetrics.hourlyStats.findIndex((stat)=>stat.hour === currentHour);\n            if (existingIndex >= 0) {\n                this.performanceMetrics.hourlyStats[existingIndex] = hourStat;\n            } else {\n                this.performanceMetrics.hourlyStats.push(hourStat);\n            }\n            // 保留最近24小时的统计\n            this.performanceMetrics.hourlyStats = this.performanceMetrics.hourlyStats.slice(-24);\n        }\n    }\n    /**\r\n   * 更新性能指标\r\n   */ updatePerformanceMetrics(operationKey, duration, success) {\n        let cached = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        // 更新总体指标\n        this.performanceMetrics.totalCalls++;\n        if (success) {\n            this.performanceMetrics.successCalls++;\n        } else {\n            this.performanceMetrics.errorCalls++;\n        }\n        // 更新响应时间统计\n        this.performanceMetrics.minResponseTime = Math.min(this.performanceMetrics.minResponseTime, duration);\n        this.performanceMetrics.maxResponseTime = Math.max(this.performanceMetrics.maxResponseTime, duration);\n        // 更新平均响应时间\n        const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalCalls - 1) + duration;\n        this.performanceMetrics.averageResponseTime = totalTime / this.performanceMetrics.totalCalls;\n        // 更新缓存命中率\n        if (cached) {\n            const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate + 1;\n            this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls;\n        } else {\n            const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate;\n            this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls;\n        }\n        // 更新方法统计\n        const methodStat = this.methodStats.get(operationKey) || {\n            calls: 0,\n            totalTime: 0,\n            averageTime: 0,\n            successRate: 0,\n            lastCall: 0,\n            errors: 0\n        };\n        methodStat.calls++;\n        methodStat.totalTime += duration;\n        methodStat.averageTime = methodStat.totalTime / methodStat.calls;\n        methodStat.lastCall = Date.now();\n        if (success) {\n            methodStat.successRate = (methodStat.successRate * (methodStat.calls - 1) + 1) / methodStat.calls;\n        } else {\n            methodStat.errors++;\n            methodStat.successRate = methodStat.successRate * (methodStat.calls - 1) / methodStat.calls;\n        }\n        this.methodStats.set(operationKey, methodStat);\n        // 记录慢查询\n        if (duration > 1000) {\n            this.performanceMetrics.slowQueries.push({\n                method: operationKey,\n                params: {},\n                duration,\n                timestamp: Date.now(),\n                cached\n            });\n            // 保持慢查询记录数量限制\n            if (this.performanceMetrics.slowQueries.length > 100) {\n                this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.slice(-50);\n            }\n        }\n        // 记录最近调用\n        this.recentCalls.push({\n            method: operationKey,\n            duration,\n            status: success ? \"success\" : \"error\",\n            timestamp: Date.now(),\n            cached\n        });\n        // 保持最近调用记录数量限制\n        if (this.recentCalls.length > 1000) {\n            this.recentCalls = this.recentCalls.slice(-500);\n        }\n    }\n    // 注意：executeWithCaching方法已移动到内置缓存系统部分\n    /**\r\n   * 执行带日志记录和性能监控的方法调用 - 统一改造版\r\n   */ async executeWithLogging(service, method, params, executor) {\n        const startTime = Date.now();\n        const operationKey = \"\".concat(service, \".\").concat(method);\n        try {\n            // 使用内置缓存系统\n            const result = await this.executeWithCache(service, method, params, executor);\n            const duration = Date.now() - startTime;\n            // 更新性能指标（成功）\n            this.updatePerformanceMetrics(operationKey, duration, true, false);\n            this.logAccess(service, method, params, duration, true);\n            _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(operationKey, duration, true, params);\n            return result;\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // 更新性能指标（错误）\n            this.updatePerformanceMetrics(operationKey, duration, false, false);\n            this.logAccess(service, method, params, duration, false, errorMessage);\n            _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(operationKey, duration, false, params);\n            throw error;\n        }\n    }\n    // executeWithUnifiedCache方法已移除，使用内置缓存系统\n    // invalidateUnifiedCache方法已移除，使用内置缓存系统\n    // executeWithWorkstationCaching方法已移除，使用内置缓存系统\n    // 统一缓存相关方法已移除，使用内置缓存系统\n    // ==================== 产品数据访问 ====================\n    /**\r\n   * 产品数据访问器\r\n   */ get products() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductService\", \"getProducts\", params, ()=>this.productService.getProducts(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductService\", \"getProductById\", {\n                    id\n                }, ()=>this.productService.getProductById(id)),\n            getByCode: (code)=>this.executeWithLogging(\"ProductService\", \"getProductByCode\", {\n                    code\n                }, ()=>this.productService.getProductByCode(code)),\n            create: (data)=>this.executeWithLogging(\"ProductService\", \"createProduct\", data, ()=>this.productService.createProduct(data)),\n            update: (id, updates)=>this.executeWithLogging(\"ProductService\", \"updateProduct\", {\n                    id,\n                    updates\n                }, ()=>this.productService.updateProduct(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"ProductService\", \"deleteProduct\", {\n                    id\n                }, ()=>this.productService.deleteProduct(id)),\n            getActive: ()=>this.executeWithLogging(\"ProductService\", \"getActiveProducts\", {}, ()=>this.productService.getActiveProducts()),\n            getByCategory: (category)=>this.executeWithLogging(\"ProductService\", \"getProductsByCategory\", {\n                    category\n                }, ()=>this.productService.getProductsByCategory(category)),\n            search: (keyword)=>this.executeWithLogging(\"ProductService\", \"searchProducts\", {\n                    keyword\n                }, ()=>this.productService.searchProducts(keyword)),\n            getByMold: (moldId)=>this.executeWithLogging(\"ProductService\", \"getProductsByMold\", {\n                    moldId\n                }, ()=>this.productService.getProductsByMold(moldId)),\n            getMoldUsage: ()=>this.executeWithLogging(\"ProductService\", \"getMoldUsageStatistics\", {}, ()=>this.productService.getMoldUsageStatistics())\n        };\n    }\n    // ==================== 客户数据访问 ====================\n    /**\r\n   * 客户数据访问器\r\n   */ get customers() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"CustomerService\", \"getCustomers\", params, ()=>this.customerService.getCustomers(params)),\n            getById: (id)=>this.executeWithLogging(\"CustomerService\", \"getCustomerById\", {\n                    id\n                }, ()=>this.customerService.getCustomerById(id)),\n            create: (data)=>this.executeWithLogging(\"CustomerService\", \"createCustomer\", data, ()=>this.customerService.createCustomer(data)),\n            update: (id, updates)=>this.executeWithLogging(\"CustomerService\", \"updateCustomer\", {\n                    id,\n                    updates\n                }, ()=>this.customerService.updateCustomer(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"CustomerService\", \"deleteCustomer\", {\n                    id\n                }, ()=>this.customerService.deleteCustomer(id)),\n            getActive: ()=>this.executeWithLogging(\"CustomerService\", \"getActiveCustomers\", {}, ()=>this.customerService.getActiveCustomers()),\n            search: (keyword)=>this.executeWithLogging(\"CustomerService\", \"searchCustomers\", {\n                    keyword\n                }, ()=>this.customerService.searchCustomers(keyword))\n        };\n    }\n    // ==================== 员工数据访问 ====================\n    /**\r\n   * 员工数据访问器\r\n   */ get employees() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"EmployeeService\", \"getEmployees\", params, ()=>this.employeeService.getEmployees(params)),\n            getById: (id)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeeById\", {\n                    id\n                }, ()=>this.employeeService.getEmployeeById(id)),\n            create: (data)=>this.executeWithLogging(\"EmployeeService\", \"createEmployee\", data, ()=>this.employeeService.createEmployee(data)),\n            update: (id, updates)=>this.executeWithLogging(\"EmployeeService\", \"updateEmployee\", {\n                    id,\n                    updates\n                }, ()=>this.employeeService.updateEmployee(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"EmployeeService\", \"deleteEmployee\", {\n                    id\n                }, ()=>this.employeeService.deleteEmployee(id)),\n            getActive: ()=>this.executeWithLogging(\"EmployeeService\", \"getActiveEmployees\", {}, ()=>this.employeeService.getActiveEmployees()),\n            getByDepartment: (department)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeesByDepartment\", {\n                    department\n                }, ()=>this.employeeService.getEmployeesByDepartment(department)),\n            getByRole: (role)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeesByRole\", {\n                    role\n                }, ()=>this.employeeService.getEmployeesByRole(role)),\n            getSales: ()=>this.executeWithLogging(\"EmployeeService\", \"getSalesEmployees\", {}, ()=>this.employeeService.getSalesEmployees())\n        };\n    }\n    // ==================== 库存数据访问 ====================\n    /**\r\n   * 库存数据访问器\r\n   */ get inventory() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"InventoryService\", \"getProductInventory\", params, ()=>this.inventoryService.getProductInventory(params)),\n            getByProductCode: (productCode)=>this.executeWithLogging(\"InventoryService\", \"getInventoryByProductCode\", {\n                    productCode\n                }, ()=>this.inventoryService.getInventoryByProductCode(productCode)),\n            update: (productCode, updates)=>this.executeWithLogging(\"InventoryService\", \"updateInventory\", {\n                    productCode,\n                    updates\n                }, ()=>this.inventoryService.updateInventory(productCode, updates)),\n            getLowStock: (threshold)=>this.executeWithLogging(\"InventoryService\", \"getLowStockProducts\", {\n                    threshold\n                }, ()=>this.inventoryService.getLowStockProducts(threshold)),\n            getValue: ()=>this.executeWithLogging(\"InventoryService\", \"getInventoryValue\", {}, ()=>this.inventoryService.getInventoryValue())\n        };\n    }\n    // ==================== 认证数据访问 ====================\n    /**\r\n   * 认证数据访问器\r\n   */ get auth() {\n        return {\n            // 认证相关\n            login: (credentials)=>this.executeWithLogging(\"AuthService\", \"login\", credentials, ()=>this.authService.login(credentials)),\n            logout: (refreshToken)=>this.executeWithLogging(\"AuthService\", \"logout\", {\n                    refreshToken\n                }, ()=>this.authService.logout(refreshToken)),\n            refreshToken: (request)=>this.executeWithLogging(\"AuthService\", \"refreshToken\", request, ()=>this.authService.refreshToken(request)),\n            validateToken: (token)=>this.executeWithLogging(\"AuthService\", \"validateToken\", {\n                    token\n                }, ()=>this.authService.validateToken(token)),\n            // 用户管理\n            getUserById: (id)=>this.executeWithLogging(\"AuthService\", \"getUserById\", {\n                    id\n                }, ()=>this.authService.getUserById(id)),\n            getUserByUsername: (username)=>this.executeWithLogging(\"AuthService\", \"getUserByUsername\", {\n                    username\n                }, ()=>this.authService.getUserByUsername(username)),\n            createUser: (userData)=>this.executeWithLogging(\"AuthService\", \"createUser\", userData, ()=>this.authService.createUser(userData)),\n            updateUser: (id, updates)=>this.executeWithLogging(\"AuthService\", \"updateUser\", {\n                    id,\n                    updates\n                }, ()=>this.authService.updateUser(id, updates)),\n            deleteUser: (id)=>this.executeWithLogging(\"AuthService\", \"deleteUser\", {\n                    id\n                }, ()=>this.authService.deleteUser(id)),\n            // 会话管理\n            createSession: (userId, sessionData)=>this.executeWithLogging(\"AuthService\", \"createSession\", {\n                    userId,\n                    sessionData\n                }, ()=>this.authService.createSession(userId, sessionData)),\n            getSession: (sessionId)=>this.executeWithLogging(\"AuthService\", \"getSession\", {\n                    sessionId\n                }, ()=>this.authService.getSession(sessionId)),\n            updateSession: (sessionId, updates)=>this.executeWithLogging(\"AuthService\", \"updateSession\", {\n                    sessionId,\n                    updates\n                }, ()=>this.authService.updateSession(sessionId, updates)),\n            deleteSession: (sessionId)=>this.executeWithLogging(\"AuthService\", \"deleteSession\", {\n                    sessionId\n                }, ()=>this.authService.deleteSession(sessionId)),\n            getUserSessions: (userId)=>this.executeWithLogging(\"AuthService\", \"getUserSessions\", {\n                    userId\n                }, ()=>this.authService.getUserSessions(userId)),\n            // 密码管理\n            changePassword: (userId, currentPassword, newPassword)=>this.executeWithLogging(\"AuthService\", \"changePassword\", {\n                    userId\n                }, ()=>this.authService.changePassword(userId, currentPassword, newPassword)),\n            resetPassword: (userId, newPassword)=>this.executeWithLogging(\"AuthService\", \"resetPassword\", {\n                    userId\n                }, ()=>this.authService.resetPassword(userId, newPassword))\n        };\n    }\n    // ==================== Token管理数据访问 ====================\n    /**\r\n   * Token管理数据访问器\r\n   */ get tokenManagement() {\n        return {\n            // Token生成\n            generateAccessToken: (payload)=>this.executeWithLogging(\"TokenManagementService\", \"generateAccessToken\", payload, ()=>this.tokenManagementService.generateAccessToken(payload)),\n            generateRefreshToken: (userId, sessionId)=>this.executeWithLogging(\"TokenManagementService\", \"generateRefreshToken\", {\n                    userId,\n                    sessionId\n                }, ()=>this.tokenManagementService.generateRefreshToken(userId, sessionId)),\n            // Token验证\n            verifyAccessToken: (token)=>this.executeWithLogging(\"TokenManagementService\", \"verifyAccessToken\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.verifyAccessToken(token)),\n            verifyRefreshToken: (token)=>this.executeWithLogging(\"TokenManagementService\", \"verifyRefreshToken\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.verifyRefreshToken(token)),\n            // Token工具方法\n            extractUserInfo: (token)=>this.executeWithLogging(\"TokenManagementService\", \"extractUserInfo\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.extractUserInfo(token)),\n            shouldRefreshToken: (token, thresholdMinutes)=>this.executeWithLogging(\"TokenManagementService\", \"shouldRefreshToken\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length,\n                    thresholdMinutes\n                }, ()=>this.tokenManagementService.shouldRefreshToken(token, thresholdMinutes)),\n            getTokenRemainingTime: (token)=>this.executeWithLogging(\"TokenManagementService\", \"getTokenRemainingTime\", {\n                    tokenLength: token === null || token === void 0 ? void 0 : token.length\n                }, ()=>this.tokenManagementService.getTokenRemainingTime(token)),\n            generateSessionId: ()=>this.executeWithLogging(\"TokenManagementService\", \"generateSessionId\", {}, ()=>this.tokenManagementService.generateSessionId()),\n            // 配置检查\n            checkConfiguration: ()=>this.executeWithLogging(\"TokenManagementService\", \"checkConfiguration\", {}, ()=>this.tokenManagementService.checkConfiguration())\n        };\n    }\n    // ==================== 角色权限数据访问 ====================\n    /**\r\n   * 角色权限数据访问器\r\n   */ get roles() {\n        return {\n            // 角色管理\n            getAll: ()=>this.executeWithLogging(\"RoleService\", \"getRoles\", {}, ()=>this.roleService.getRoles()),\n            getById: (id)=>this.executeWithLogging(\"RoleService\", \"getRoleById\", {\n                    id\n                }, ()=>this.roleService.getRoleById(id)),\n            getByCode: (code)=>this.executeWithLogging(\"RoleService\", \"getRoleByCode\", {\n                    code\n                }, ()=>this.roleService.getRoleByCode(code)),\n            create: (roleData)=>this.executeWithLogging(\"RoleService\", \"createRole\", roleData, ()=>this.roleService.createRole(roleData)),\n            update: (id, updates)=>this.executeWithLogging(\"RoleService\", \"updateRole\", {\n                    id,\n                    updates\n                }, ()=>this.roleService.updateRole(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"RoleService\", \"deleteRole\", {\n                    id\n                }, ()=>this.roleService.deleteRole(id)),\n            // 权限管理\n            getPermissions: ()=>this.executeWithLogging(\"RoleService\", \"getPermissions\", {}, ()=>this.roleService.getPermissions()),\n            getPermissionsByModule: (module)=>this.executeWithLogging(\"RoleService\", \"getPermissionsByModule\", {\n                    module\n                }, ()=>this.roleService.getPermissionsByModule(module)),\n            assignPermissions: (roleId, permissionIds)=>this.executeWithLogging(\"RoleService\", \"assignRolePermissions\", {\n                    roleId,\n                    permissionIds\n                }, ()=>this.roleService.assignRolePermissions(roleId, permissionIds))\n        };\n    }\n    /**\r\n   * 订单数据访问器\r\n   */ get orders() {\n        return {\n            getAll: (params)=>{\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] orders.getAll 被调用，管理器ID:\", this.__managerId);\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] 使用的OrderService实例ID:\", this.orderService.__serviceId);\n                return this.executeWithLogging(\"OrderService\", \"getOrders\", params, ()=>this.orderService.getOrders(params));\n            },\n            getById: (id)=>this.executeWithLogging(\"OrderService\", \"getOrderById\", {\n                    id\n                }, ()=>this.orderService.getOrderById(id)),\n            getByNumber: (orderNumber)=>this.executeWithLogging(\"OrderService\", \"getOrderByNumber\", {\n                    orderNumber\n                }, ()=>this.orderService.getOrderByNumber(orderNumber)),\n            create: (data)=>{\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] orders.create 被调用，管理器ID:\", this.__managerId);\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] 使用的OrderService实例ID:\", this.orderService.__serviceId);\n                return this.executeWithLogging(\"OrderService\", \"createOrder\", data, ()=>this.orderService.createOrder(data));\n            },\n            update: (id, updates)=>this.executeWithLogging(\"OrderService\", \"updateOrder\", {\n                    id,\n                    updates\n                }, ()=>this.orderService.updateOrder(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"OrderService\", \"deleteOrder\", {\n                    id\n                }, ()=>this.orderService.deleteOrder(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"OrderService\", \"getOrdersByStatus\", {\n                    status\n                }, ()=>this.orderService.getOrdersByStatus(status)),\n            getByCustomer: (customerId)=>this.executeWithLogging(\"OrderService\", \"getOrdersByCustomer\", {\n                    customerId\n                }, ()=>this.orderService.getOrdersByCustomer(customerId)),\n            getBySalesRep: (salesRepId)=>this.executeWithLogging(\"OrderService\", \"getOrdersBySalesRep\", {\n                    salesRepId\n                }, ()=>this.orderService.getOrdersBySalesRep(salesRepId)),\n            getByDateRange: (startDate, endDate)=>this.executeWithLogging(\"OrderService\", \"getOrdersByDateRange\", {\n                    startDate,\n                    endDate\n                }, ()=>this.orderService.getOrdersByDateRange(startDate, endDate))\n        };\n    }\n    /**\r\n   * 工作时间配置数据访问器\r\n   */ get workTime() {\n        return {\n            getConfigurations: ()=>this.executeWithLogging(\"WorkTimeService\", \"getConfigurations\", {}, ()=>this.workTimeService.getConfigurations()),\n            getById: (id)=>this.executeWithLogging(\"WorkTimeService\", \"getConfigurationById\", {\n                    id\n                }, ()=>this.workTimeService.getConfigurationById(id)),\n            create: (data)=>this.executeWithLogging(\"WorkTimeService\", \"createConfiguration\", data, ()=>this.workTimeService.createConfiguration(data)),\n            update: (id, data)=>this.executeWithLogging(\"WorkTimeService\", \"updateConfiguration\", {\n                    id,\n                    ...data\n                }, ()=>this.workTimeService.updateConfiguration(id, data)),\n            delete: (id)=>this.executeWithLogging(\"WorkTimeService\", \"deleteConfiguration\", {\n                    id\n                }, ()=>this.workTimeService.deleteConfiguration(id)),\n            addWorkTimeSlot: (configId, slot)=>this.executeWithLogging(\"WorkTimeService\", \"addWorkTimeSlot\", {\n                    configId,\n                    slot\n                }, ()=>this.workTimeService.addWorkTimeSlot(configId, slot)),\n            updateWorkTimeSlot: (configId, slotId, data)=>this.executeWithLogging(\"WorkTimeService\", \"updateWorkTimeSlot\", {\n                    configId,\n                    slotId,\n                    ...data\n                }, ()=>this.workTimeService.updateWorkTimeSlot(configId, slotId, data)),\n            deleteWorkTimeSlot: (configId, slotId)=>this.executeWithLogging(\"WorkTimeService\", \"deleteWorkTimeSlot\", {\n                    configId,\n                    slotId\n                }, ()=>this.workTimeService.deleteWorkTimeSlot(configId, slotId)),\n            calculateWorkingMinutes: (workTimeSlots, breakTimeSlots)=>{\n                const startTime = Date.now();\n                const result = this.workTimeService.calculateWorkingMinutes(workTimeSlots, breakTimeSlots);\n                const duration = Date.now() - startTime;\n                this.logAccess(\"WorkTimeService\", \"calculateWorkingMinutes\", {\n                    workTimeSlots,\n                    breakTimeSlots\n                }, duration, true);\n                return result;\n            },\n            validateTimeSlot: (startTime, endTime)=>{\n                const start = Date.now();\n                const result = this.workTimeService.validateTimeSlot(startTime, endTime);\n                const duration = Date.now() - start;\n                this.logAccess(\"WorkTimeService\", \"validateTimeSlot\", {\n                    startTime,\n                    endTime\n                }, duration, true);\n                return result;\n            },\n            getDefault: ()=>this.executeWithLogging(\"WorkTimeService\", \"getDefaultConfiguration\", {}, ()=>this.workTimeService.getDefaultConfiguration())\n        };\n    }\n    /**\r\n   * 生产订单数据访问器\r\n   */ get productionOrders() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductionOrderService\", \"getAll\", params, ()=>this.productionOrderService.getAll(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductionOrderService\", \"getById\", {\n                    id\n                }, ()=>this.productionOrderService.getById(id)),\n            getByOrderNumber: (orderNumber)=>this.executeWithLogging(\"ProductionOrderService\", \"getByOrderNumber\", {\n                    orderNumber\n                }, ()=>this.productionOrderService.getByOrderNumber(orderNumber)),\n            createFromMRP: (data)=>this.executeWithLogging(\"ProductionOrderService\", \"createFromMRP\", data, ()=>this.productionOrderService.createFromMRP(data)),\n            // 添加通用的create方法（用于测试）\n            create: (data)=>this.executeWithLogging(\"ProductionOrderService\", \"createFromMRP\", data, ()=>{\n                    // 为测试目的添加默认MRP字段\n                    const mrpData = {\n                        ...data,\n                        mrpExecutionId: data.mrpExecutionId || \"mrp_\".concat(Date.now()),\n                        mrpExecutedBy: data.mrpExecutedBy || \"test-user\",\n                        mrpExecutedAt: data.mrpExecutedAt || new Date().toISOString()\n                    };\n                    return this.productionOrderService.createFromMRP(mrpData);\n                }),\n            update: (id, data)=>this.executeWithLogging(\"ProductionOrderService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.productionOrderService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"ProductionOrderService\", \"delete\", {\n                    id\n                }, ()=>this.productionOrderService.delete(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"ProductionOrderService\", \"getByStatus\", {\n                    status\n                }, ()=>this.productionOrderService.getByStatus(status)),\n            getBySalesOrderId: (salesOrderId)=>this.executeWithLogging(\"ProductionOrderService\", \"getBySalesOrderId\", {\n                    salesOrderId\n                }, ()=>this.productionOrderService.getBySalesOrderId(salesOrderId)),\n            // 添加统计方法\n            getStatistics: ()=>this.executeWithLogging(\"ProductionOrderService\", \"getStatistics\", {}, ()=>this.productionOrderService.getStatistics())\n        };\n    }\n    /**\r\n   * 生产工单数据访问器\r\n   */ get productionWorkOrders() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getAll\", params, ()=>this.productionWorkOrderService.getAll(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getById\", {\n                    id\n                }, ()=>this.productionWorkOrderService.getById(id)),\n            getByBatchNumber: (batchNumber)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByBatchNumber\", {\n                    batchNumber\n                }, ()=>this.productionWorkOrderService.getByBatchNumber(batchNumber)),\n            create: (data)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"create\", data, ()=>this.productionWorkOrderService.create(data)),\n            update: (id, data)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.productionWorkOrderService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"delete\", {\n                    id\n                }, ()=>this.productionWorkOrderService.delete(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByStatus\", {\n                    status\n                }, ()=>this.productionWorkOrderService.getByStatus(status)),\n            getBySourceOrderId: (sourceOrderId)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getBySourceOrderId\", {\n                    sourceOrderId\n                }, ()=>this.productionWorkOrderService.getBySourceOrderId(sourceOrderId)),\n            getByWorkstation: (workstation)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByWorkstation\", {\n                    workstation\n                }, ()=>this.productionWorkOrderService.getByWorkstation(workstation))\n        };\n    }\n    /**\r\n   * 工位数据访问器\r\n   * 🔧 修复：为工位数据添加特殊的缓存策略，确保排程算法获取最新状态\r\n   */ get workstations() {\n        return {\n            // 查询方法 - 使用内置缓存系统\n            getAll: (params)=>this.executeWithLogging(\"WorkstationService\", \"getAll\", params, ()=>this.workstationService.getAll()),\n            getWorkstations: (params)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstations\", params, ()=>this.workstationService.getWorkstations(params)),\n            getById: (id)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstationById\", {\n                    id\n                }, ()=>this.workstationService.getWorkstationById(id)),\n            getActiveWorkstations: ()=>this.executeWithLogging(\"WorkstationService\", \"getActiveWorkstations\", {}, ()=>this.workstationService.getActiveWorkstations()),\n            // CRUD操作\n            create: (data)=>this.executeWithLogging(\"WorkstationService\", \"create\", data, ()=>this.workstationService.create(data)),\n            update: (id, data)=>this.executeWithLogging(\"WorkstationService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.workstationService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"WorkstationService\", \"delete\", {\n                    id\n                }, ()=>this.workstationService.delete(id)),\n            // 状态管理\n            getStatus: (id)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstationStatus\", {\n                    id\n                }, ()=>this.workstationService.getWorkstationStatus(id)),\n            updateStatus: (id, status)=>this.executeWithLogging(\"WorkstationService\", \"updateWorkstationStatus\", {\n                    id,\n                    ...status\n                }, ()=>this.workstationService.updateWorkstationStatus(id, status)),\n            // 队列管理\n            addToQueue: (workstationId, batchNumber)=>this.executeWithLogging(\"WorkstationService\", \"addToQueue\", {\n                    workstationId,\n                    batchNumber\n                }, ()=>this.workstationService.addToQueue(workstationId, batchNumber)),\n            migrateBatchNumberFormats: ()=>this.executeWithLogging(\"WorkstationService\", \"migrateBatchNumberFormats\", {}, ()=>this.workstationService.migrateBatchNumberFormats()),\n            // 🔧 统一的工位重置方法\n            resetAllWorkstationsToIdle: ()=>this.executeWithLogging(\"WorkstationService\", \"resetAllWorkstationsToIdle\", {}, ()=>this.workstationService.resetAllWorkstationsToIdle())\n        };\n    }\n    // ==================== 管理功能 ====================\n    /**\r\n   * 获取访问日志\r\n   */ getAccessLogs(limit) {\n        return limit ? this.accessLogs.slice(-limit) : [\n            ...this.accessLogs\n        ];\n    }\n    /**\r\n   * 清除访问日志\r\n   */ clearAccessLogs() {\n        this.accessLogs = [];\n    }\n    /**\r\n   * 获取统计信息\r\n   */ getStatistics() {\n        const totalRequests = this.accessLogs.length;\n        const successfulRequests = this.accessLogs.filter((log)=>log.success).length;\n        const failedRequests = totalRequests - successfulRequests;\n        const averageDuration = totalRequests > 0 ? this.accessLogs.reduce((sum, log)=>sum + log.duration, 0) / totalRequests : 0;\n        return {\n            totalRequests,\n            successfulRequests,\n            failedRequests,\n            successRate: totalRequests > 0 ? successfulRequests / totalRequests * 100 : 0,\n            averageDuration: Math.round(averageDuration),\n            config: this.config\n        };\n    }\n    /**\r\n   * 初始化数据变更通知机制\r\n   */ initializeDataChangeNotification() {\n    // 这里可以添加额外的初始化逻辑\n    // 数据变更通知器已经在导入时自动初始化\n    }\n    /**\r\n   * 获取数据变更通知器\r\n   */ getDataChangeNotifier() {\n        return _DataChangeNotifier__WEBPACK_IMPORTED_MODULE_13__.dataChangeNotifier;\n    }\n    // 优先级同步服务已删除\n    /**\r\n   * 成本计算数据访问器\r\n   */ get costCalculations() {\n        return {\n            getAllCalculations: (params)=>this.executeWithLogging(\"CostCalculationService\", \"getAllCalculations\", params, ()=>this.costCalculationService.getAllCalculations(params)),\n            getCalculationById: (id)=>this.executeWithLogging(\"CostCalculationService\", \"getCalculationById\", {\n                    id\n                }, ()=>this.costCalculationService.getCalculationById(id)),\n            createCalculation: (data)=>this.executeWithLogging(\"CostCalculationService\", \"createCalculation\", data, ()=>this.costCalculationService.createCalculation(data)),\n            updateCalculation: (id, data)=>this.executeWithLogging(\"CostCalculationService\", \"updateCalculation\", {\n                    id,\n                    ...data\n                }, ()=>this.costCalculationService.updateCalculation(id, data)),\n            deleteCalculation: (id)=>this.executeWithLogging(\"CostCalculationService\", \"deleteCalculation\", {\n                    id\n                }, ()=>this.costCalculationService.deleteCalculation(id)),\n            getCalculationsByProduct: (productModelCode)=>this.executeWithLogging(\"CostCalculationService\", \"getCalculationsByProduct\", {\n                    productModelCode\n                }, ()=>this.costCalculationService.getCalculationsByProduct(productModelCode)),\n            getPendingReconciliations: ()=>this.executeWithLogging(\"CostCalculationService\", \"getPendingReconciliations\", {}, ()=>this.costCalculationService.getPendingReconciliations()),\n            getStatistics: ()=>this.executeWithLogging(\"CostCalculationService\", \"getStatistics\", {}, ()=>this.costCalculationService.getStatistics()),\n            getCostSummary: ()=>this.executeWithLogging(\"CostCalculationService\", \"getCostSummary\", {}, ()=>this.costCalculationService.getCostSummary())\n        };\n    }\n    // 旧缓存管理方法已移除，使用内置缓存系统\n    // 移除重复的方法定义，使用下面的统一缓存管理方法\n    // invalidateCache(tags: string[]): void {\n    //   this.cacheManager.deleteByTags(tags)\n    // }\n    /**\r\n   * 更新配置\r\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        // 如果禁用缓存，清空现有缓存\n        if (newConfig.enableCaching === false) {\n            this.cache.clear();\n            this.requestCache.clear();\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存已禁用并清空\");\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 配置已更新:\", this.config);\n        }\n    }\n    // ==================== 缓存管理接口 ====================\n    /**\r\n   * 清除特定服务的缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearServiceCache(serviceName, operation) {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return 0;\n        }\n        // ✅ 架构合规：使用内置缓存系统清理服务缓存\n        let deletedCount = 0;\n        const pattern = \"\".concat(serviceName, \":\");\n        for (const [key] of Array.from(this.cache)){\n            if (key.startsWith(pattern)) {\n                this.cache.delete(key);\n                deletedCount++;\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除服务缓存: \".concat(serviceName).concat(operation ? \" (\".concat(operation, \")\") : \"\", \"，删除了 \").concat(deletedCount, \" 个缓存项\"));\n        }\n        return deletedCount;\n    }\n    /**\r\n   * 清除特定数据类型的缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearDataTypeCache(dataType, affectedIds) {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return 0;\n        }\n        let deletedCount = 0;\n        const serviceMap = {\n            \"products\": \"ProductService\",\n            \"orders\": \"OrderService\",\n            \"workstations\": \"WorkstationService\",\n            \"statistics\": \"StatisticsService\"\n        };\n        const serviceName = serviceMap[dataType] || dataType;\n        for (const [key] of Array.from(this.cache)){\n            if (key.startsWith(\"\".concat(serviceName, \":\"))) {\n                // 如果指定了特定ID，只删除相关的缓存\n                if (affectedIds && affectedIds.length > 0) {\n                    const hasMatchingId = affectedIds.some((id)=>key.includes(id));\n                    if (hasMatchingId) {\n                        this.cache.delete(key);\n                        deletedCount++;\n                    }\n                } else {\n                    // 删除所有该数据类型的缓存\n                    this.cache.delete(key);\n                    deletedCount++;\n                }\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除数据类型缓存: \".concat(dataType, \"，影响ID: \").concat((affectedIds === null || affectedIds === void 0 ? void 0 : affectedIds.join(\", \")) || \"全部\", \"，删除了 \").concat(deletedCount, \" 个缓存项\"));\n        }\n        return deletedCount;\n    }\n    /**\r\n   * 清除所有缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearAllCache() {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return;\n        }\n        // ✅ 架构合规：使用内置缓存系统清理所有缓存\n        const beforeSize = this.cache.size;\n        this.cache.clear();\n        this.requestCache.clear();\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除所有缓存，删除了 \".concat(beforeSize, \" 个缓存项\"));\n        }\n    }\n    /**\r\n   * 获取缓存统计信息 - 优化版\r\n   * 符合数据调用规范的监控接口\r\n   */ getCacheStatistics() {\n        // ✅ 架构合规：使用内置缓存系统获取统计信息\n        const entries = Array.from(this.cache.values());\n        const totalSize = entries.reduce((sum, entry)=>sum + entry.estimatedSize, 0);\n        const averageEntrySize = entries.length > 0 ? totalSize / entries.length : 0;\n        // 计算优先级分布\n        const priorityDistribution = {\n            critical: 0,\n            high: 0,\n            medium: 0,\n            low: 0\n        };\n        entries.forEach((entry)=>{\n            priorityDistribution[entry.priority]++;\n        });\n        return {\n            enabled: this.config.enableCaching,\n            size: this.cache.size,\n            hits: this.cacheStats.hits,\n            misses: this.cacheStats.misses,\n            hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0,\n            totalRequests: this.cacheStats.totalRequests,\n            // ✅ 优化：新增统计信息\n            evictions: this.cacheStats.evictions,\n            prewarmHits: this.cacheStats.prewarmHits,\n            memoryPressure: this.memoryPressure,\n            averageEntrySize,\n            priorityDistribution\n        };\n    }\n    /**\r\n   * 更新缓存配置\r\n   */ updateCacheConfig(newConfig) {\n        this.cacheConfig = {\n            ...this.cacheConfig,\n            ...newConfig\n        };\n        console.log(\"[DataAccessManager] 缓存配置已更新\");\n    }\n    // ✅ 架构合规：移除违规的CacheStrategyManager方法，使用内置缓存系统替代\n    /**\r\n   * 批量操作方法\r\n   */ get batch() {\n        return {\n            // 批量创建生产订单\n            createProductionOrders: (orders)=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__.performanceOptimizer.batchCreateProductionOrders(orders),\n            // 批量更新生产订单\n            updateProductionOrders: (updates)=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__.performanceOptimizer.batchUpdateProductionOrders(updates),\n            // 批量创建生产工单 - 使用并发控制优化\n            createProductionWorkOrders: async (workOrders)=>{\n                const startTime = performance.now();\n                try {\n                    // 创建任务数组\n                    const tasks = workOrders.map((workOrder)=>async ()=>{\n                            const result = await this.productionWorkOrders.create(workOrder);\n                            if (result.status !== \"success\") {\n                                throw new Error(result.message || \"创建工单失败\");\n                            }\n                            return result.data;\n                        });\n                    const taskNames = workOrders.map((_, index)=>\"批量创建工单-\".concat(index + 1));\n                    const batchResult = await _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__.batchOperationController.executeBatch(tasks, taskNames);\n                    const duration = performance.now() - startTime;\n                    const { successful, failed } = batchResult;\n                    const successfulData = successful.map((result)=>result.data).filter(Boolean);\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.createProductionWorkOrders\", duration, failed.length === 0);\n                    return {\n                        status: \"success\",\n                        data: successfulData,\n                        message: \"成功批量创建\".concat(successfulData.length, \"个生产工单\").concat(failed.length > 0 ? \"，\".concat(failed.length, \"个失败\") : \"\"),\n                        batchResult: {\n                            successful: successful.length,\n                            failed: failed.length,\n                            successRate: batchResult.successRate,\n                            totalDuration: duration\n                        }\n                    };\n                } catch (error) {\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.createProductionWorkOrders\", duration, false);\n                    throw error;\n                }\n            },\n            // 批量更新工位状态\n            updateWorkstationStatuses: async (updates)=>{\n                const startTime = performance.now();\n                try {\n                    const results = await Promise.all(updates.map((param)=>{\n                        let { id, status } = param;\n                        return this.workstations.updateStatus(id, status);\n                    }));\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.updateWorkstationStatuses\", duration, true);\n                    // 使用内置缓存系统清理相关缓存\n                    this.clearDataTypeCache(\"workstations\", updates.map((u)=>u.id));\n                    return {\n                        status: \"success\",\n                        data: results.filter((r)=>r.status === \"success\").map((r)=>r.data),\n                        message: \"成功批量更新\".concat(results.length, \"个工位状态\")\n                    };\n                } catch (error) {\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.recordRequest(\"batch.updateWorkstationStatuses\", duration, false);\n                    throw error;\n                }\n            }\n        };\n    }\n    /**\r\n   * 性能监控方法\r\n   */ get performance() {\n        return {\n            // 获取性能指标\n            getMetrics: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.getMetrics(),\n            // 获取性能警告\n            getAlerts: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.getAlerts(),\n            // 获取优化建议\n            getSuggestions: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.getOptimizationSuggestions(),\n            // 生成性能报告\n            generateReport: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.generatePerformanceReport(),\n            // 重置性能指标\n            reset: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_15__.dataAccessPerformanceMonitor.reset(),\n            // 优化缓存策略\n            optimizeCache: ()=>{\n                // 使用内置缓存系统进行优化\n                this.evictOldestEntries();\n                console.log(\"[DataAccessManager] 缓存优化完成\");\n            },\n            // 获取缓存性能指标\n            getCacheMetrics: ()=>{\n                return this.getCacheStatistics();\n            },\n            // 获取性能优化器指标\n            getOptimizerMetrics: ()=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_14__.performanceOptimizer.getPerformanceMetrics()\n        };\n    }\n    // 旧的缓存管理方法已移除，使用内置缓存系统的统一接口\n    /**\r\n   * 性能监控管理方法\r\n   */ getPerformanceMetrics() {\n        // 更新方法统计到性能指标中\n        this.performanceMetrics.methodStats = this.methodStats;\n        return {\n            ...this.performanceMetrics\n        };\n    }\n    getRealTimeMetrics() {\n        const batchControllerStatus = _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__.batchOperationController.getStatus();\n        // 分析系统健康状况\n        const systemHealth = this.analyzeSystemHealth();\n        return {\n            currentConcurrency: batchControllerStatus.running,\n            queueLength: batchControllerStatus.queued,\n            recentCalls: [\n                ...this.recentCalls.slice(-50)\n            ],\n            systemHealth\n        };\n    }\n    analyzeSystemHealth() {\n        const issues = [];\n        const recommendations = [];\n        // 检查错误率\n        const recentErrorRate = this.recentCalls.length > 0 ? this.recentCalls.filter((call)=>call.status === \"error\").length / this.recentCalls.length : 0;\n        if (recentErrorRate > 0.1) {\n            issues.push(\"错误率过高: \".concat((recentErrorRate * 100).toFixed(2), \"%\"));\n            recommendations.push(\"检查网络连接和服务状态\");\n        }\n        // 检查响应时间\n        if (this.performanceMetrics.averageResponseTime > 2000) {\n            issues.push(\"平均响应时间过长: \".concat(this.performanceMetrics.averageResponseTime.toFixed(0), \"ms\"));\n            recommendations.push(\"考虑优化查询或增加缓存\");\n        }\n        // 检查缓存命中率\n        if (this.performanceMetrics.cacheHitRate < 0.3) {\n            issues.push(\"缓存命中率较低: \".concat((this.performanceMetrics.cacheHitRate * 100).toFixed(2), \"%\"));\n            recommendations.push(\"检查缓存策略和TTL设置\");\n        }\n        // 检查并发队列\n        const batchStatus = _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_16__.batchOperationController.getStatus();\n        if (batchStatus.queued > 10) {\n            issues.push(\"批量操作队列积压: \".concat(batchStatus.queued, \"个任务\"));\n            recommendations.push(\"考虑增加并发数或优化任务处理\");\n        }\n        // 确定系统状态\n        let status = \"healthy\";\n        if (issues.length > 0) {\n            status = recentErrorRate > 0.2 || this.performanceMetrics.averageResponseTime > 5000 ? \"critical\" : \"warning\";\n        }\n        return {\n            status,\n            issues,\n            recommendations\n        };\n    }\n    resetPerformanceMetrics() {\n        this.initializePerformanceMonitoring();\n        console.log(\"[DataAccessManager] 性能指标已重置\");\n    }\n    getSlowQueries() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n        return this.performanceMetrics.slowQueries.sort((a, b)=>b.duration - a.duration).slice(0, limit);\n    }\n    getMethodStatistics() {\n        return Array.from(this.methodStats.entries()).map((param)=>{\n            let [method, stats] = param;\n            return {\n                method,\n                calls: stats.calls,\n                averageTime: stats.averageTime,\n                successRate: stats.successRate,\n                lastCall: new Date(stats.lastCall).toLocaleString()\n            };\n        }).sort((a, b)=>b.calls - a.calls) // 按调用次数排序\n        ;\n    }\n    exportPerformanceReport() {\n        return {\n            timestamp: new Date().toISOString(),\n            summary: this.getPerformanceMetrics(),\n            realTime: this.getRealTimeMetrics(),\n            topMethods: this.getMethodStatistics().slice(0, 10),\n            slowQueries: this.getSlowQueries(10)\n        };\n    }\n    // ==================== 批量操作 ====================\n    /**\r\n   * 批量操作访问器\r\n   * 提供批量创建、更新、删除等操作\r\n   */ get batch() {\n        return {\n            // 批量创建生产订单\n            createProductionOrders: async (orders)=>{\n                const results = [];\n                const errors = [];\n                for (const order of orders){\n                    try {\n                        const result = await this.productionOrders.create(order);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(result.message || \"创建失败\");\n                        }\n                    } catch (error) {\n                        errors.push(error instanceof Error ? error.message : \"未知错误\");\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功创建\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"创建过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量更新生产订单\n            updateProductionOrders: async (updates)=>{\n                const results = [];\n                const errors = [];\n                for (const { id, data } of updates){\n                    try {\n                        const result = await this.productionOrders.update(id, data);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"更新失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功更新\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"更新过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量删除生产订单\n            deleteProductionOrders: async (ids)=>{\n                const successIds = [];\n                const errors = [];\n                for (const id of ids){\n                    try {\n                        const result = await this.productionOrders.delete(id);\n                        if (result.status === \"success\") {\n                            successIds.push(id);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"删除失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: true,\n                        message: \"成功删除\".concat(successIds.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: false,\n                        message: \"删除过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量查询生产订单\n            getProductionOrders: async (ids)=>{\n                const results = [];\n                const errors = [];\n                for (const id of ids){\n                    try {\n                        const result = await this.productionOrders.getById(id);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"查询失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功查询\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"查询过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            }\n        };\n    }\n    /**\r\n   * 获取系统配置\r\n   */ getConfig() {\n        return {\n            ...this.systemConfig\n        };\n    }\n    /**\r\n   * 更新系统配置\r\n   */ updateConfig(config) {\n        this.systemConfig = {\n            ...this.systemConfig,\n            ...config\n        };\n        console.log(\"[DataAccessManager] 系统配置已更新:\", config);\n        // 根据配置更新对应的系统行为\n        if (config.enableCaching !== undefined) {\n            console.log(\"[DataAccessManager] 缓存状态: \".concat(config.enableCaching ? \"已启用\" : \"已禁用\"));\n        }\n        if (config.cacheTimeout !== undefined) {\n            console.log(\"[DataAccessManager] 缓存超时时间: \".concat(config.cacheTimeout, \"ms\"));\n        }\n        if (config.enableLogging !== undefined) {\n            console.log(\"[DataAccessManager] 日志状态: \".concat(config.enableLogging ? \"已启用\" : \"已禁用\"));\n        }\n        if (config.logLevel !== undefined) {\n            console.log(\"[DataAccessManager] 日志级别: \".concat(config.logLevel));\n        }\n    }\n    /**\r\n   * 重置配置到默认值\r\n   */ resetConfig() {\n        this.systemConfig = {\n            enableCaching: true,\n            cacheTimeout: 5 * 60 * 1000,\n            enableLogging: true,\n            logLevel: \"INFO\",\n            enablePerformanceMonitoring: true,\n            maxConcurrentRequests: 10,\n            retryAttempts: 3,\n            retryDelay: 1000\n        };\n        console.log(\"[DataAccessManager] 系统配置已重置\");\n    }\n    constructor(config = {}){\n        this.accessLogs = [];\n        // ✅ 优化：增强缓存管理属性\n        this.cache = new Map();\n        this.cacheConfig = DEFAULT_CACHE_CONFIG;\n        this.requestCache = new Map();\n        this.cacheStats = {\n            hits: 0,\n            misses: 0,\n            totalRequests: 0,\n            evictions: 0,\n            prewarmHits: 0\n        };\n        // ✅ 优化：智能缓存管理\n        this.cacheAccessHistory = new Map() // 访问时间历史\n        ;\n        this.prewarmTimer = null;\n        this.lastMemoryCheck = 0;\n        this.memoryPressure = false;\n        // 性能监控相关属性\n        this.performanceMetrics = {\n            totalCalls: 0,\n            successCalls: 0,\n            errorCalls: 0,\n            averageResponseTime: 0,\n            minResponseTime: 0,\n            maxResponseTime: 0,\n            cacheHitRate: 0,\n            slowQueries: [],\n            methodStats: new Map(),\n            hourlyStats: []\n        };\n        this.recentCalls = [];\n        this.methodStats = new Map();\n        // 🔧 优化：批量性能报告\n        this.performanceBatch = [];\n        this.batchReportTimer = null;\n        this.BATCH_REPORT_INTERVAL = 5000 // 5秒批量报告一次\n        ;\n        // ==================== 配置管理 ====================\n        /**\r\n   * 系统配置管理\r\n   * 提供动态配置更新和查询功能\r\n   */ this.systemConfig = {\n            enableCaching: true,\n            cacheTimeout: 5 * 60 * 1000,\n            enableLogging: true,\n            logLevel: \"INFO\",\n            enablePerformanceMonitoring: true,\n            maxConcurrentRequests: 10,\n            retryAttempts: 3,\n            retryDelay: 1000\n        };\n        this.config = {\n            ...DEFAULT_CONFIG,\n            ...config\n        };\n        this.__managerId = Math.random().toString(36).substr(2, 9);\n        console.log(\"✅ [DataAccessManager] 创建实例，ID:\", this.__managerId, \"内置缓存系统已启用\");\n        // 初始化内置缓存系统\n        if (this.config.enableCaching) {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 内置缓存系统已启用\");\n            // ✅ 优化：启动智能缓存管理\n            this.initializeAdvancedCaching();\n        } else {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 缓存已禁用\");\n        }\n        // 初始化性能监控\n        this.initializePerformanceMonitoring();\n        // 初始化服务实例\n        this.productService = _ProductDataAccessService__WEBPACK_IMPORTED_MODULE_0__.ProductDataAccessService.getInstance();\n        this.customerService = _CustomerDataAccessService__WEBPACK_IMPORTED_MODULE_1__.CustomerDataAccessService.getInstance();\n        this.employeeService = _EmployeeDataAccessService__WEBPACK_IMPORTED_MODULE_2__.EmployeeDataAccessService.getInstance();\n        this.inventoryService = _InventoryDataAccessService__WEBPACK_IMPORTED_MODULE_3__.InventoryDataAccessService.getInstance();\n        this.orderService = _OrderDataAccessService__WEBPACK_IMPORTED_MODULE_4__.OrderDataAccessService.getInstance();\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 初始化OrderService，管理器ID:\", this.__managerId);\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] OrderService实例ID:\", this.orderService.__serviceId);\n        this.workTimeService = _WorkTimeDataAccessService__WEBPACK_IMPORTED_MODULE_5__.WorkTimeDataAccessService.getInstance();\n        this.productionOrderService = _ProductionOrderDataAccessService__WEBPACK_IMPORTED_MODULE_6__.ProductionOrderDataAccessService.getInstance();\n        this.productionWorkOrderService = _ProductionWorkOrderDataAccessService__WEBPACK_IMPORTED_MODULE_7__.ProductionWorkOrderDataAccessService.getInstance();\n        this.workstationService = _WorkstationDataAccessService__WEBPACK_IMPORTED_MODULE_8__.WorkstationDataAccessService.getInstance();\n        this.costCalculationService = _CostCalculationDataAccessService__WEBPACK_IMPORTED_MODULE_9__.CostCalculationDataAccessService.getInstance();\n        this.authService = _AuthDataAccessService__WEBPACK_IMPORTED_MODULE_10__.AuthDataAccessService.getInstance();\n        this.roleService = new _RoleDataAccessService__WEBPACK_IMPORTED_MODULE_11__.RoleDataAccessService();\n        this.tokenManagementService = _TokenManagementService__WEBPACK_IMPORTED_MODULE_12__.TokenManagementService.getInstance();\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 认证服务、角色服务和Token管理服务已初始化\");\n        // 初始化数据变更通知机制\n        this.initializeDataChangeNotification();\n        // 优先级同步服务已删除\n        if (true) {\n            console.log({\n                version: _DataAccessLayer__WEBPACK_IMPORTED_MODULE_17__.API_VERSION,\n                config: this.config,\n                dataChangeNotifier: \"已启用\"\n            });\n        }\n    }\n}\n// 创建单例实例\nconst dataAccessManager = DataAccessManager.getInstance();\n// 🔧 将DataAccessManager暴露到全局作用域（用于调试和测试）\nif (true) {\n    window.dataAccessManager = dataAccessManager;\n    console.log(\"\\uD83D\\uDD27 [DataAccessManager] 已暴露到全局作用域 window.dataAccessManager\");\n}\nconst { products, customers, employees, inventory, productionOrders, productionWorkOrders, workstations, costCalculations, auth } = dataAccessManager;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\n"));

/***/ })

});