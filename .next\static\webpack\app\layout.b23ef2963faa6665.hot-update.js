"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts":
/*!******************************************************!*\
  !*** ./src/services/dataAccess/DataAccessManager.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CACHE_CONFIG: function() { return /* binding */ DEFAULT_CACHE_CONFIG; },\n/* harmony export */   DataAccessManager: function() { return /* binding */ DataAccessManager; },\n/* harmony export */   auth: function() { return /* binding */ auth; },\n/* harmony export */   costCalculations: function() { return /* binding */ costCalculations; },\n/* harmony export */   customers: function() { return /* binding */ customers; },\n/* harmony export */   dataAccessManager: function() { return /* binding */ dataAccessManager; },\n/* harmony export */   employees: function() { return /* binding */ employees; },\n/* harmony export */   inventory: function() { return /* binding */ inventory; },\n/* harmony export */   productionOrders: function() { return /* binding */ productionOrders; },\n/* harmony export */   productionWorkOrders: function() { return /* binding */ productionWorkOrders; },\n/* harmony export */   products: function() { return /* binding */ products; },\n/* harmony export */   workstations: function() { return /* binding */ workstations; }\n/* harmony export */ });\n/* harmony import */ var _ProductDataAccessService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProductDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductDataAccessService.ts\");\n/* harmony import */ var _CustomerDataAccessService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CustomerDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/CustomerDataAccessService.ts\");\n/* harmony import */ var _EmployeeDataAccessService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmployeeDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/EmployeeDataAccessService.ts\");\n/* harmony import */ var _InventoryDataAccessService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InventoryDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/InventoryDataAccessService.ts\");\n/* harmony import */ var _OrderDataAccessService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./OrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/OrderDataAccessService.ts\");\n/* harmony import */ var _WorkTimeDataAccessService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./WorkTimeDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/WorkTimeDataAccessService.ts\");\n/* harmony import */ var _ProductionOrderDataAccessService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ProductionOrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductionOrderDataAccessService.ts\");\n/* harmony import */ var _ProductionWorkOrderDataAccessService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProductionWorkOrderDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/ProductionWorkOrderDataAccessService.ts\");\n/* harmony import */ var _WorkstationDataAccessService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./WorkstationDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/WorkstationDataAccessService.ts\");\n/* harmony import */ var _CostCalculationDataAccessService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CostCalculationDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/CostCalculationDataAccessService.ts\");\n/* harmony import */ var _AuthDataAccessService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./AuthDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/AuthDataAccessService.ts\");\n/* harmony import */ var _RoleDataAccessService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./RoleDataAccessService */ \"(app-pages-browser)/./src/services/dataAccess/RoleDataAccessService.ts\");\n/* harmony import */ var _DataChangeNotifier__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./DataChangeNotifier */ \"(app-pages-browser)/./src/services/dataAccess/DataChangeNotifier.ts\");\n/* harmony import */ var _PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./PerformanceOptimizer */ \"(app-pages-browser)/./src/services/dataAccess/PerformanceOptimizer.ts\");\n/* harmony import */ var _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./DataAccessPerformanceMonitor */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessPerformanceMonitor.ts\");\n/* harmony import */ var _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/concurrencyControl */ \"(app-pages-browser)/./src/utils/concurrencyControl.ts\");\n/* harmony import */ var _DataAccessLayer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./DataAccessLayer */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessLayer.ts\");\n/* harmony import */ var _utils_business__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/business */ \"(app-pages-browser)/./src/utils/business/index.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js\")[\"Buffer\"];\n/**\r\n * 统一数据访问管理器\r\n * 提供单一入口点访问所有数据服务，实现标准化的跨模块数据调用\r\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n// ✅ 架构合规：已移除独立CacheStrategyManager，使用内置缓存系统\n\n\n// ✅ 架构合规：移除违规的独立缓存管理器，使用内置缓存\n\n// 优先级同步服务已删除\n\n\n/**\r\n * 默认缓存配置 - 优化版\r\n */ const DEFAULT_CACHE_CONFIG = {\n    enableCaching: true,\n    defaultTTL: 5 * 60 * 1000,\n    maxSize: 1000,\n    strategies: {\n        orders: {\n            ttl: 2 * 60 * 1000,\n            enabled: true,\n            priority: \"high\",\n            maxEntries: 200\n        },\n        products: {\n            ttl: 10 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 300\n        },\n        workstations: {\n            ttl: 30 * 1000,\n            enabled: true,\n            priority: \"critical\",\n            maxEntries: 50\n        },\n        statistics: {\n            ttl: 30 * 1000,\n            enabled: true,\n            priority: \"low\",\n            maxEntries: 100\n        },\n        customers: {\n            ttl: 15 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 150\n        },\n        employees: {\n            ttl: 30 * 60 * 1000,\n            enabled: true,\n            priority: \"medium\",\n            maxEntries: 100\n        } // 30分钟\n    },\n    monitoring: {\n        enabled: true,\n        reportInterval: 60000 // 1分钟\n    },\n    // ✅ 优化：智能缓存管理配置\n    smartEviction: {\n        enabled: true,\n        algorithm: \"adaptive\",\n        thresholds: {\n            memoryWarning: 0.7,\n            memoryCritical: 0.85 // 85%内存使用率紧急清理\n        }\n    },\n    // ✅ 优化：缓存预热配置\n    prewarming: {\n        enabled: true,\n        strategies: [\n            \"workstations\",\n            \"orders\",\n            \"products\"\n        ],\n        scheduleInterval: 30000 // 30秒预热间隔\n    }\n};\n/**\r\n * 默认配置 - 简化版\r\n */ const DEFAULT_CONFIG = {\n    enableLogging: true,\n    enableCaching: true,\n    defaultTTL: 5 * 60 * 1000,\n    maxCacheSize: 1000,\n    retryAttempts: 3,\n    retryDelay: 1000 // 1秒\n};\n/**\r\n * 统一数据访问管理器\r\n */ class DataAccessManager {\n    /**\r\n   * 获取单例实例\r\n   */ static getInstance(config) {\n        if (!DataAccessManager.instance) {\n            DataAccessManager.instance = new DataAccessManager(config);\n        }\n        return DataAccessManager.instance;\n    }\n    // 优先级同步服务已删除\n    /**\r\n   * 记录访问日志\r\n   */ logAccess(service, method, params, duration, success, error, fromCache) {\n        if (!this.config.enableLogging) return;\n        const log = {\n            // ✅ 使用统一时间戳生成器\n            timestamp: _utils_business__WEBPACK_IMPORTED_MODULE_17__.timestampGenerator.now(),\n            service,\n            method,\n            params,\n            duration,\n            success,\n            error\n        };\n        this.accessLogs.push(log);\n        // 保持最近1000条日志\n        if (this.accessLogs.length > 1000) {\n            this.accessLogs = this.accessLogs.slice(-1000);\n        }\n        // 🔧 优化：使用批量报告代替单个日志\n        if (true) {\n            this.addToPerformanceBatch(service, method, duration, success, fromCache || false);\n        }\n    }\n    // 🔧 优化：批量性能报告方法\n    /**\r\n   * 添加到性能批次\r\n   */ addToPerformanceBatch(service, method, duration, success, fromCache) {\n        this.performanceBatch.push({\n            service,\n            method,\n            duration,\n            success,\n            fromCache,\n            timestamp: Date.now()\n        });\n        // 启动批量报告定时器\n        if (!this.batchReportTimer) {\n            this.batchReportTimer = setTimeout(()=>{\n                this.flushPerformanceBatch();\n            }, this.BATCH_REPORT_INTERVAL);\n        }\n    }\n    /**\r\n   * 输出批量性能报告\r\n   */ flushPerformanceBatch() {\n        if (this.performanceBatch.length === 0) {\n            this.batchReportTimer = null;\n            return;\n        }\n        // 统计数据\n        const stats = {\n            totalOperations: this.performanceBatch.length,\n            successCount: this.performanceBatch.filter((op)=>op.success).length,\n            cacheHits: this.performanceBatch.filter((op)=>op.fromCache).length,\n            averageTime: Math.round(this.performanceBatch.reduce((sum, op)=>sum + op.duration, 0) / this.performanceBatch.length),\n            services: new Set(this.performanceBatch.map((op)=>op.service)).size,\n            timeRange: {\n                start: new Date(Math.min(...this.performanceBatch.map((op)=>op.timestamp))).toLocaleTimeString(),\n                end: new Date(Math.max(...this.performanceBatch.map((op)=>op.timestamp))).toLocaleTimeString()\n            }\n        };\n        console.log(\"\\uD83D\\uDCCA [DataAccessManager] 批量性能报告:\", stats);\n        // 清空批次\n        this.performanceBatch = [];\n        this.batchReportTimer = null;\n    }\n    // 性能优化支持方法\n    /**\r\n   * 判断是否应该使用缓存\r\n   */ shouldUseCache(method) {\n        // 🔧 临时修复：禁用getById的缓存，避免ID混淆问题\n        if (method === \"getById\") {\n            return false;\n        }\n        // 读操作使用缓存，写操作不使用\n        const readMethods = [\n            \"get\",\n            \"find\",\n            \"search\",\n            \"list\",\n            \"statistics\",\n            \"utilization\"\n        ];\n        return readMethods.some((readMethod)=>method.toLowerCase().includes(readMethod));\n    }\n    /**\r\n   * 判断是否应该缓存结果\r\n   */ shouldCacheResult(method, result) {\n        // 成功的读操作结果才缓存\n        return this.shouldUseCache(method) && (result === null || result === void 0 ? void 0 : result.status) === \"success\";\n    }\n    /**\r\n   * 生成缓存键\r\n   * 🔧 修复：统一缓存键格式，确保与清理模式匹配\r\n   */ generateCacheKey(service, method, params) {\n        // 🔧 修复：使用冒号分隔符，与WorkstationUpdateService的清理模式保持一致\n        const baseKey = \"\".concat(service, \":\").concat(method);\n        // 🔧 修复：标准化参数处理，确保键的一致性\n        if (!params || typeof params === \"object\" && Object.keys(params).length === 0) {\n            return baseKey;\n        }\n        // 对参数进行标准化处理\n        let paramStr;\n        if (typeof params === \"object\") {\n            // 对对象参数进行排序，确保键的一致性\n            const sortedParams = Object.keys(params).sort().reduce((result, key)=>{\n                result[key] = params[key];\n                return result;\n            }, {});\n            paramStr = JSON.stringify(sortedParams);\n        } else {\n            paramStr = String(params);\n        }\n        return \"\".concat(baseKey, \":\").concat(Buffer.from(paramStr).toString(\"base64\").slice(0, 32));\n    }\n    /**\r\n   * 从服务名获取数据类型\r\n   */ getDataTypeFromService(service) {\n        if (service.includes(\"ProductionOrder\")) return \"orders\";\n        if (service.includes(\"ProductionWorkOrder\")) return \"workOrders\";\n        if (service.includes(\"OrderService\") || service.includes(\"SalesOrder\")) return \"orders\" // 🔧 新增：支持销售订单\n        ;\n        if (service.includes(\"Workstation\")) return \"workstations\";\n        return \"statistics\";\n    }\n    /**\r\n   * 获取访问频率（简化实现）\r\n   */ getAccessFrequency(operationKey) {\n        // 从访问日志中统计频率\n        const recentLogs = this.accessLogs.filter((log)=>Date.now() - new Date(log.timestamp).getTime() < 5 * 60 * 1000 // 最近5分钟\n        );\n        return recentLogs.filter((log)=>\"\".concat(log.service, \".\").concat(log.method) === operationKey).length;\n    }\n    /**\r\n   * 判断是否应该预加载\r\n   */ shouldPreload(method, result) {\n        var _result_data_items, _result_data;\n        // 获取列表数据时触发预加载\n        return method.includes(\"getAll\") && (result === null || result === void 0 ? void 0 : (_result_data = result.data) === null || _result_data === void 0 ? void 0 : (_result_data_items = _result_data.items) === null || _result_data_items === void 0 ? void 0 : _result_data_items.length) > 0;\n    }\n    // ==================== 内置缓存系统核心方法 ====================\n    /**\r\n   * 从缓存获取数据\r\n   */ getFromCache(key) {\n        const entry = this.cache.get(key);\n        if (!entry) return null;\n        // 检查是否过期\n        if (Date.now() > entry.expiresAt) {\n            this.cache.delete(key);\n            return null;\n        }\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return entry.data;\n    }\n    /**\r\n   * 设置缓存数据 - 优化版\r\n   */ setToCache(key, data, service, method) {\n        // ✅ 优化：智能缓存容量管理\n        if (this.cache.size >= this.cacheConfig.maxSize) {\n            this.smartEvictEntries();\n        }\n        const strategy = this.cacheConfig.strategies[service] || {\n            ttl: this.cacheConfig.defaultTTL,\n            priority: \"medium\"\n        };\n        // ✅ 优化：智能数据分析\n        const dataType = this.getDataType(service, method);\n        const priority = strategy.priority || \"medium\";\n        const estimatedSize = this.estimateDataSize(data);\n        const accessPattern = this.predictAccessPattern(key, service);\n        const entry = {\n            data,\n            expiresAt: Date.now() + strategy.ttl,\n            accessCount: 1,\n            lastAccessed: Date.now(),\n            createdAt: Date.now(),\n            // ✅ 优化：智能缓存属性\n            priority,\n            dataType,\n            estimatedSize,\n            accessPattern,\n            refreshable: this.isRefreshableData(service, method)\n        };\n        this.cache.set(key, entry);\n        // ✅ 优化：记录访问历史\n        this.recordCacheAccess(key);\n    }\n    /**\r\n   * ✅ 优化：智能缓存清理策略\r\n   */ smartEvictEntries() {\n        const entries = Array.from(this.cache.entries());\n        const now = Date.now();\n        // 根据配置的算法选择清理策略\n        switch(this.cacheConfig.smartEviction.algorithm){\n            case \"lru\":\n                this.evictByLRU(entries);\n                break;\n            case \"lfu\":\n                this.evictByLFU(entries);\n                break;\n            case \"adaptive\":\n            default:\n                this.evictAdaptive(entries, now);\n                break;\n        }\n        this.cacheStats.evictions++;\n    }\n    /**\r\n   * ✅ 优化：自适应缓存清理\r\n   */ evictAdaptive(entries, now) {\n        // 综合考虑优先级、访问频率、大小等因素\n        const scored = entries.map((param)=>{\n            let [key, entry] = param;\n            let score = 0;\n            // 优先级评分 (优先级越高分数越高，越不容易被清理)\n            const priorityScores = {\n                critical: 100,\n                high: 70,\n                medium: 40,\n                low: 10\n            };\n            score += priorityScores[entry.priority] || 40;\n            // 访问频率评分\n            const age = (now - entry.createdAt) / (24 * 60 * 60 * 1000 // 天数\n            );\n            const frequency = entry.accessCount / Math.max(age, 0.1);\n            score += Math.min(frequency * 10, 50);\n            // 最近访问评分\n            const lastAccessAge = (now - entry.lastAccessed) / (60 * 60 * 1000 // 小时\n            );\n            score += Math.max(50 - lastAccessAge * 2, 0);\n            // 大小惩罚（大数据降低分数）\n            score -= Math.min(entry.estimatedSize / 1000, 20);\n            return {\n                key,\n                entry,\n                score\n            };\n        });\n        // 按分数排序，删除分数最低的条目\n        scored.sort((a, b)=>a.score - b.score);\n        const deleteCount = Math.max(Math.floor(entries.length * 0.15), 1);\n        for(let i = 0; i < deleteCount && i < scored.length; i++){\n            this.cache.delete(scored[i].key);\n            this.cacheAccessHistory.delete(scored[i].key);\n        }\n    }\n    /**\r\n   * ✅ 优化：LRU清理策略\r\n   */ evictByLRU(entries) {\n        entries.sort((a, b)=>a[1].lastAccessed - b[1].lastAccessed);\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n            this.cacheAccessHistory.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * ✅ 优化：LFU清理策略\r\n   */ evictByLFU(entries) {\n        entries.sort((a, b)=>a[1].accessCount - b[1].accessCount);\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n            this.cacheAccessHistory.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * 清理过期缓存 - 保留原有方法作为备用\r\n   */ evictOldestEntries() {\n        const entries = Array.from(this.cache.entries());\n        entries.sort((a, b)=>a[1].lastAccessed - b[1].lastAccessed);\n        // 删除最旧的10%条目\n        const deleteCount = Math.floor(entries.length * 0.1);\n        for(let i = 0; i < deleteCount; i++){\n            this.cache.delete(entries[i][0]);\n        }\n    }\n    /**\r\n   * 统一的缓存执行方法\r\n   */ async executeWithCache(service, method, params, executor) {\n        // ✅ 架构合规：使用内置缓存系统决策\n        if (!this.config.enableCaching) {\n            return executor();\n        }\n        // 简化的缓存决策：读操作才缓存\n        const readMethods = [\n            \"get\",\n            \"find\",\n            \"search\",\n            \"list\",\n            \"statistics\"\n        ];\n        const shouldCache = readMethods.some((readMethod)=>method.toLowerCase().includes(readMethod));\n        if (!shouldCache) {\n            return executor();\n        }\n        const cacheKey = this.generateCacheKey(service, method, params);\n        // 请求去重\n        if (this.requestCache.has(cacheKey)) {\n            return this.requestCache.get(cacheKey);\n        }\n        // ✅ 架构合规：使用DataAccessManager内置缓存系统\n        const cachedEntry = this.cache.get(cacheKey);\n        if (cachedEntry && Date.now() < cachedEntry.expiresAt) {\n            this.cacheStats.hits++;\n            // ✅ 优化：更新访问信息\n            cachedEntry.accessCount++;\n            cachedEntry.lastAccessed = Date.now();\n            this.recordCacheAccess(cacheKey);\n            return cachedEntry.data;\n        }\n        // 执行请求\n        const requestPromise = executor();\n        this.requestCache.set(cacheKey, requestPromise);\n        try {\n            const result = await requestPromise;\n            this.cacheStats.misses++;\n            // ✅ 架构合规：使用DataAccessManager内置缓存系统设置缓存\n            if (this.shouldCacheResult(method, result)) {\n                this.setToCache(cacheKey, result, service, method);\n            }\n            return result;\n        } finally{\n            this.requestCache.delete(cacheKey);\n        }\n    }\n    // ==================== ✅ 优化：智能缓存辅助方法 ====================\n    /**\r\n   * ✅ 优化：初始化高级缓存功能\r\n   */ initializeAdvancedCaching() {\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 启动智能缓存管理\");\n        // 启动缓存预热\n        if (this.cacheConfig.prewarming.enabled) {\n            this.startCachePrewarming();\n        }\n        // 启动定期内存检查\n        this.startMemoryMonitoring();\n        console.log(\"✅ [DataAccessManager] 智能缓存管理已启动\");\n    }\n    /**\r\n   * ✅ 优化：启动缓存预热\r\n   */ startCachePrewarming() {\n        if (this.prewarmTimer) {\n            clearInterval(this.prewarmTimer);\n        }\n        this.prewarmTimer = setInterval(()=>{\n            this.performCachePrewarming();\n        }, this.cacheConfig.prewarming.scheduleInterval);\n        // 立即执行一次预热\n        setTimeout(()=>this.performCachePrewarming(), 5000) // 5秒后开始\n        ;\n    }\n    /**\r\n   * ✅ 优化：执行缓存预热\r\n   */ async performCachePrewarming() {\n        if (this.memoryPressure) {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 内存压力过高，跳过预热\");\n            return;\n        }\n        console.log(\"\\uD83D\\uDD25 [DataAccessManager] 开始缓存预热\");\n        try {\n            for (const strategy of this.cacheConfig.prewarming.strategies){\n                await this.prewarmStrategy(strategy);\n            }\n        } catch (error) {\n            console.error(\"❌ [DataAccessManager] 缓存预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热特定策略\r\n   */ async prewarmStrategy(strategy) {\n        switch(strategy){\n            case \"workstations\":\n                await this.prewarmWorkstations();\n                break;\n            case \"orders\":\n                await this.prewarmOrders();\n                break;\n            case \"products\":\n                await this.prewarmProducts();\n                break;\n        }\n    }\n    /**\r\n   * ✅ 优化：预热工位数据\r\n   */ async prewarmWorkstations() {\n        try {\n            await this.workstations.getActiveWorkstations();\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 工位数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 工位数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热订单数据\r\n   */ async prewarmOrders() {\n        try {\n            await this.orders.getAll({\n                limit: 50\n            }) // 最近50个订单\n            ;\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 订单数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 订单数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：预热产品数据\r\n   */ async prewarmProducts() {\n        try {\n            await this.products.getActive();\n            this.cacheStats.prewarmHits++;\n            console.log(\"\\uD83D\\uDD25 [DataAccessManager] 产品数据预热完成\");\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 产品数据预热失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：启动内存监控\r\n   */ startMemoryMonitoring() {\n        setInterval(()=>{\n            this.checkMemoryPressure();\n        }, 30000) // 每30秒检查一次\n        ;\n    }\n    /**\r\n   * ✅ 优化：检查内存压力\r\n   */ checkMemoryPressure() {\n        const now = Date.now();\n        if (now - this.lastMemoryCheck < 10000) return; // 10秒内不重复检查\n        this.lastMemoryCheck = now;\n        try {\n            // 简单的内存压力检测（基于缓存大小）\n            const cacheMemoryRatio = this.cache.size / this.cacheConfig.maxSize;\n            if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryCritical) {\n                this.memoryPressure = true;\n                this.handleCriticalMemoryPressure();\n            } else if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryWarning) {\n                this.memoryPressure = false;\n                this.handleMemoryWarning();\n            } else {\n                this.memoryPressure = false;\n            }\n        } catch (error) {\n            console.warn(\"⚠️ [DataAccessManager] 内存压力检查失败:\", error);\n        }\n    }\n    /**\r\n   * ✅ 优化：处理紧急内存压力\r\n   */ handleCriticalMemoryPressure() {\n        console.warn(\"\\uD83D\\uDEA8 [DataAccessManager] 检测到紧急内存压力，执行强制清理\");\n        // 强制清理缓存\n        this.smartEvictEntries();\n        // 暂停预热\n        if (this.prewarmTimer) {\n            clearInterval(this.prewarmTimer);\n            this.prewarmTimer = null;\n        }\n    }\n    /**\r\n   * ✅ 优化：处理内存警告\r\n   */ handleMemoryWarning() {\n        console.warn(\"⚠️ [DataAccessManager] 检测到内存压力警告，执行适度清理\");\n        // 清理低优先级缓存\n        this.cleanupLowPriorityCache();\n    }\n    /**\r\n   * ✅ 优化：清理低优先级缓存\r\n   */ cleanupLowPriorityCache() {\n        const entries = Array.from(this.cache.entries());\n        let deletedCount = 0;\n        for (const [key, entry] of entries){\n            if (entry.priority === \"low\" && Date.now() > entry.expiresAt - 60000) {\n                this.cache.delete(key);\n                this.cacheAccessHistory.delete(key);\n                deletedCount++;\n            }\n        }\n        console.log(\"\\uD83E\\uDDF9 [DataAccessManager] 清理了\".concat(deletedCount, \"个低优先级缓存条目\"));\n    }\n    /**\r\n   * ✅ 优化：获取数据类型\r\n   */ getDataType(service, method) {\n        if (!method) return service;\n        if (method.includes(\"statistics\") || method.includes(\"utilization\")) return \"statistics\";\n        if (method.includes(\"list\") || method.includes(\"getAll\")) return \"list\";\n        if (method.includes(\"getById\") || method.includes(\"getBy\")) return \"detail\";\n        return service;\n    }\n    /**\r\n   * ✅ 优化：预测访问模式\r\n   */ predictAccessPattern(key, service) {\n        const history = this.cacheAccessHistory.get(key) || [];\n        if (history.length < 2) {\n            // 根据服务类型预测\n            if (service === \"workstations\" || service === \"statistics\") return \"frequent\";\n            if (service === \"orders\" || service === \"products\") return \"occasional\";\n            return \"rare\";\n        }\n        // 基于历史访问频率\n        const recentAccesses = history.filter((time)=>Date.now() - time < 60 * 60 * 1000).length // 1小时内\n        ;\n        if (recentAccesses > 10) return \"frequent\";\n        if (recentAccesses > 3) return \"occasional\";\n        return \"rare\";\n    }\n    /**\r\n   * ✅ 优化：估算数据大小\r\n   */ estimateDataSize(data) {\n        try {\n            const jsonString = JSON.stringify(data);\n            return jsonString.length * 2 // 大致估算，考虑Unicode字符\n            ;\n        } catch (e) {\n            return 1000 // 默认估算\n            ;\n        }\n    }\n    /**\r\n   * ✅ 优化：判断数据是否可刷新\r\n   */ isRefreshableData(service, method) {\n        // 统计数据、状态数据等是可刷新的\n        if ((method === null || method === void 0 ? void 0 : method.includes(\"statistics\")) || (method === null || method === void 0 ? void 0 : method.includes(\"status\")) || (method === null || method === void 0 ? void 0 : method.includes(\"utilization\"))) {\n            return true;\n        }\n        // 工位数据是频繁变化的\n        if (service === \"workstations\") return true;\n        return false;\n    }\n    /**\r\n   * ✅ 优化：记录缓存访问\r\n   */ recordCacheAccess(key) {\n        const history = this.cacheAccessHistory.get(key) || [];\n        history.push(Date.now());\n        // 只保留最近100次访问记录\n        if (history.length > 100) {\n            history.splice(0, history.length - 100);\n        }\n        this.cacheAccessHistory.set(key, history);\n    }\n    /**\r\n   * ✅ 架构合规：获取缓存TTL\r\n   */ getCacheTTL(service, method) {\n        // 根据服务类型和方法返回不同的TTL\n        const serviceConfig = {\n            orders: 2 * 60 * 1000,\n            products: 10 * 60 * 1000,\n            workstations: 30 * 1000,\n            statistics: 30 * 1000,\n            customers: 15 * 60 * 1000,\n            employees: 30 * 60 * 1000 // 员工数据: 30分钟\n        };\n        return serviceConfig[service] || this.config.defaultTTL || 5 * 60 * 1000 // 默认5分钟\n        ;\n    }\n    /**\r\n   * 初始化性能监控\r\n   */ initializePerformanceMonitoring() {\n        this.performanceMetrics = {\n            totalCalls: 0,\n            successCalls: 0,\n            errorCalls: 0,\n            averageResponseTime: 0,\n            minResponseTime: Infinity,\n            maxResponseTime: 0,\n            cacheHitRate: 0,\n            slowQueries: [],\n            methodStats: new Map(),\n            hourlyStats: []\n        };\n        // 启动定期清理任务\n        this.startPerformanceCleanupTask();\n    }\n    /**\r\n   * 启动性能数据清理任务\r\n   */ startPerformanceCleanupTask() {\n        // 每小时清理一次过期的性能数据\n        setInterval(()=>{\n            this.cleanupPerformanceData();\n        }, 60 * 60 * 1000) // 1小时\n        ;\n    }\n    /**\r\n   * 清理过期的性能数据\r\n   */ cleanupPerformanceData() {\n        const now = Date.now();\n        const oneHourAgo = now - 60 * 60 * 1000;\n        const oneDayAgo = now - 24 * 60 * 60 * 1000;\n        // 清理最近调用记录（保留1小时）\n        this.recentCalls = this.recentCalls.filter((call)=>call.timestamp > oneHourAgo);\n        // 清理慢查询记录（保留1天，最多100条）\n        this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.filter((query)=>query.timestamp > oneDayAgo).slice(-100);\n        // 更新小时统计\n        this.updateHourlyStats();\n    }\n    /**\r\n   * 更新小时统计\r\n   */ updateHourlyStats() {\n        const now = new Date();\n        const currentHour = \"\".concat(now.getFullYear(), \"-\").concat(String(now.getMonth() + 1).padStart(2, \"0\"), \"-\").concat(String(now.getDate()).padStart(2, \"0\"), \" \").concat(String(now.getHours()).padStart(2, \"0\"), \":00\");\n        const oneHourAgo = Date.now() - 60 * 60 * 1000;\n        const hourCalls = this.recentCalls.filter((call)=>call.timestamp > oneHourAgo);\n        if (hourCalls.length > 0) {\n            const totalTime = hourCalls.reduce((sum, call)=>sum + call.duration, 0);\n            const errorCount = hourCalls.filter((call)=>call.status === \"error\").length;\n            const hourStat = {\n                hour: currentHour,\n                calls: hourCalls.length,\n                averageTime: totalTime / hourCalls.length,\n                errorRate: errorCount / hourCalls.length\n            };\n            // 更新或添加当前小时的统计\n            const existingIndex = this.performanceMetrics.hourlyStats.findIndex((stat)=>stat.hour === currentHour);\n            if (existingIndex >= 0) {\n                this.performanceMetrics.hourlyStats[existingIndex] = hourStat;\n            } else {\n                this.performanceMetrics.hourlyStats.push(hourStat);\n            }\n            // 保留最近24小时的统计\n            this.performanceMetrics.hourlyStats = this.performanceMetrics.hourlyStats.slice(-24);\n        }\n    }\n    /**\r\n   * 更新性能指标\r\n   */ updatePerformanceMetrics(operationKey, duration, success) {\n        let cached = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        // 更新总体指标\n        this.performanceMetrics.totalCalls++;\n        if (success) {\n            this.performanceMetrics.successCalls++;\n        } else {\n            this.performanceMetrics.errorCalls++;\n        }\n        // 更新响应时间统计\n        this.performanceMetrics.minResponseTime = Math.min(this.performanceMetrics.minResponseTime, duration);\n        this.performanceMetrics.maxResponseTime = Math.max(this.performanceMetrics.maxResponseTime, duration);\n        // 更新平均响应时间\n        const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalCalls - 1) + duration;\n        this.performanceMetrics.averageResponseTime = totalTime / this.performanceMetrics.totalCalls;\n        // 更新缓存命中率\n        if (cached) {\n            const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate + 1;\n            this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls;\n        } else {\n            const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate;\n            this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls;\n        }\n        // 更新方法统计\n        const methodStat = this.methodStats.get(operationKey) || {\n            calls: 0,\n            totalTime: 0,\n            averageTime: 0,\n            successRate: 0,\n            lastCall: 0,\n            errors: 0\n        };\n        methodStat.calls++;\n        methodStat.totalTime += duration;\n        methodStat.averageTime = methodStat.totalTime / methodStat.calls;\n        methodStat.lastCall = Date.now();\n        if (success) {\n            methodStat.successRate = (methodStat.successRate * (methodStat.calls - 1) + 1) / methodStat.calls;\n        } else {\n            methodStat.errors++;\n            methodStat.successRate = methodStat.successRate * (methodStat.calls - 1) / methodStat.calls;\n        }\n        this.methodStats.set(operationKey, methodStat);\n        // 记录慢查询\n        if (duration > 1000) {\n            this.performanceMetrics.slowQueries.push({\n                method: operationKey,\n                params: {},\n                duration,\n                timestamp: Date.now(),\n                cached\n            });\n            // 保持慢查询记录数量限制\n            if (this.performanceMetrics.slowQueries.length > 100) {\n                this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.slice(-50);\n            }\n        }\n        // 记录最近调用\n        this.recentCalls.push({\n            method: operationKey,\n            duration,\n            status: success ? \"success\" : \"error\",\n            timestamp: Date.now(),\n            cached\n        });\n        // 保持最近调用记录数量限制\n        if (this.recentCalls.length > 1000) {\n            this.recentCalls = this.recentCalls.slice(-500);\n        }\n    }\n    // 注意：executeWithCaching方法已移动到内置缓存系统部分\n    /**\r\n   * 执行带日志记录和性能监控的方法调用 - 统一改造版\r\n   */ async executeWithLogging(service, method, params, executor) {\n        const startTime = Date.now();\n        const operationKey = \"\".concat(service, \".\").concat(method);\n        try {\n            // 使用内置缓存系统\n            const result = await this.executeWithCache(service, method, params, executor);\n            const duration = Date.now() - startTime;\n            // 更新性能指标（成功）\n            this.updatePerformanceMetrics(operationKey, duration, true, false);\n            this.logAccess(service, method, params, duration, true);\n            _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.recordRequest(operationKey, duration, true, params);\n            return result;\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            // 更新性能指标（错误）\n            this.updatePerformanceMetrics(operationKey, duration, false, false);\n            this.logAccess(service, method, params, duration, false, errorMessage);\n            _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.recordRequest(operationKey, duration, false, params);\n            throw error;\n        }\n    }\n    // executeWithUnifiedCache方法已移除，使用内置缓存系统\n    // invalidateUnifiedCache方法已移除，使用内置缓存系统\n    // executeWithWorkstationCaching方法已移除，使用内置缓存系统\n    // 统一缓存相关方法已移除，使用内置缓存系统\n    // ==================== 产品数据访问 ====================\n    /**\r\n   * 产品数据访问器\r\n   */ get products() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductService\", \"getProducts\", params, ()=>this.productService.getProducts(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductService\", \"getProductById\", {\n                    id\n                }, ()=>this.productService.getProductById(id)),\n            getByCode: (code)=>this.executeWithLogging(\"ProductService\", \"getProductByCode\", {\n                    code\n                }, ()=>this.productService.getProductByCode(code)),\n            create: (data)=>this.executeWithLogging(\"ProductService\", \"createProduct\", data, ()=>this.productService.createProduct(data)),\n            update: (id, updates)=>this.executeWithLogging(\"ProductService\", \"updateProduct\", {\n                    id,\n                    updates\n                }, ()=>this.productService.updateProduct(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"ProductService\", \"deleteProduct\", {\n                    id\n                }, ()=>this.productService.deleteProduct(id)),\n            getActive: ()=>this.executeWithLogging(\"ProductService\", \"getActiveProducts\", {}, ()=>this.productService.getActiveProducts()),\n            getByCategory: (category)=>this.executeWithLogging(\"ProductService\", \"getProductsByCategory\", {\n                    category\n                }, ()=>this.productService.getProductsByCategory(category)),\n            search: (keyword)=>this.executeWithLogging(\"ProductService\", \"searchProducts\", {\n                    keyword\n                }, ()=>this.productService.searchProducts(keyword)),\n            getByMold: (moldId)=>this.executeWithLogging(\"ProductService\", \"getProductsByMold\", {\n                    moldId\n                }, ()=>this.productService.getProductsByMold(moldId)),\n            getMoldUsage: ()=>this.executeWithLogging(\"ProductService\", \"getMoldUsageStatistics\", {}, ()=>this.productService.getMoldUsageStatistics())\n        };\n    }\n    // ==================== 客户数据访问 ====================\n    /**\r\n   * 客户数据访问器\r\n   */ get customers() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"CustomerService\", \"getCustomers\", params, ()=>this.customerService.getCustomers(params)),\n            getById: (id)=>this.executeWithLogging(\"CustomerService\", \"getCustomerById\", {\n                    id\n                }, ()=>this.customerService.getCustomerById(id)),\n            create: (data)=>this.executeWithLogging(\"CustomerService\", \"createCustomer\", data, ()=>this.customerService.createCustomer(data)),\n            update: (id, updates)=>this.executeWithLogging(\"CustomerService\", \"updateCustomer\", {\n                    id,\n                    updates\n                }, ()=>this.customerService.updateCustomer(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"CustomerService\", \"deleteCustomer\", {\n                    id\n                }, ()=>this.customerService.deleteCustomer(id)),\n            getActive: ()=>this.executeWithLogging(\"CustomerService\", \"getActiveCustomers\", {}, ()=>this.customerService.getActiveCustomers()),\n            search: (keyword)=>this.executeWithLogging(\"CustomerService\", \"searchCustomers\", {\n                    keyword\n                }, ()=>this.customerService.searchCustomers(keyword))\n        };\n    }\n    // ==================== 员工数据访问 ====================\n    /**\r\n   * 员工数据访问器\r\n   */ get employees() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"EmployeeService\", \"getEmployees\", params, ()=>this.employeeService.getEmployees(params)),\n            getById: (id)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeeById\", {\n                    id\n                }, ()=>this.employeeService.getEmployeeById(id)),\n            create: (data)=>this.executeWithLogging(\"EmployeeService\", \"createEmployee\", data, ()=>this.employeeService.createEmployee(data)),\n            update: (id, updates)=>this.executeWithLogging(\"EmployeeService\", \"updateEmployee\", {\n                    id,\n                    updates\n                }, ()=>this.employeeService.updateEmployee(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"EmployeeService\", \"deleteEmployee\", {\n                    id\n                }, ()=>this.employeeService.deleteEmployee(id)),\n            getActive: ()=>this.executeWithLogging(\"EmployeeService\", \"getActiveEmployees\", {}, ()=>this.employeeService.getActiveEmployees()),\n            getByDepartment: (department)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeesByDepartment\", {\n                    department\n                }, ()=>this.employeeService.getEmployeesByDepartment(department)),\n            getByRole: (role)=>this.executeWithLogging(\"EmployeeService\", \"getEmployeesByRole\", {\n                    role\n                }, ()=>this.employeeService.getEmployeesByRole(role)),\n            getSales: ()=>this.executeWithLogging(\"EmployeeService\", \"getSalesEmployees\", {}, ()=>this.employeeService.getSalesEmployees())\n        };\n    }\n    // ==================== 库存数据访问 ====================\n    /**\r\n   * 库存数据访问器\r\n   */ get inventory() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"InventoryService\", \"getProductInventory\", params, ()=>this.inventoryService.getProductInventory(params)),\n            getByProductCode: (productCode)=>this.executeWithLogging(\"InventoryService\", \"getInventoryByProductCode\", {\n                    productCode\n                }, ()=>this.inventoryService.getInventoryByProductCode(productCode)),\n            update: (productCode, updates)=>this.executeWithLogging(\"InventoryService\", \"updateInventory\", {\n                    productCode,\n                    updates\n                }, ()=>this.inventoryService.updateInventory(productCode, updates)),\n            getLowStock: (threshold)=>this.executeWithLogging(\"InventoryService\", \"getLowStockProducts\", {\n                    threshold\n                }, ()=>this.inventoryService.getLowStockProducts(threshold)),\n            getValue: ()=>this.executeWithLogging(\"InventoryService\", \"getInventoryValue\", {}, ()=>this.inventoryService.getInventoryValue())\n        };\n    }\n    // ==================== 认证数据访问 ====================\n    /**\r\n   * 认证数据访问器\r\n   */ get auth() {\n        return {\n            // 认证相关\n            login: (credentials)=>this.executeWithLogging(\"AuthService\", \"login\", credentials, ()=>this.authService.login(credentials)),\n            logout: (refreshToken)=>this.executeWithLogging(\"AuthService\", \"logout\", {\n                    refreshToken\n                }, ()=>this.authService.logout(refreshToken)),\n            refreshToken: (request)=>this.executeWithLogging(\"AuthService\", \"refreshToken\", request, ()=>this.authService.refreshToken(request)),\n            validateToken: (token)=>this.executeWithLogging(\"AuthService\", \"validateToken\", {\n                    token\n                }, ()=>this.authService.validateToken(token)),\n            // 用户管理\n            getUserById: (id)=>this.executeWithLogging(\"AuthService\", \"getUserById\", {\n                    id\n                }, ()=>this.authService.getUserById(id)),\n            getUserByUsername: (username)=>this.executeWithLogging(\"AuthService\", \"getUserByUsername\", {\n                    username\n                }, ()=>this.authService.getUserByUsername(username)),\n            createUser: (userData)=>this.executeWithLogging(\"AuthService\", \"createUser\", userData, ()=>this.authService.createUser(userData)),\n            updateUser: (id, updates)=>this.executeWithLogging(\"AuthService\", \"updateUser\", {\n                    id,\n                    updates\n                }, ()=>this.authService.updateUser(id, updates)),\n            deleteUser: (id)=>this.executeWithLogging(\"AuthService\", \"deleteUser\", {\n                    id\n                }, ()=>this.authService.deleteUser(id)),\n            // 会话管理\n            createSession: (userId, sessionData)=>this.executeWithLogging(\"AuthService\", \"createSession\", {\n                    userId,\n                    sessionData\n                }, ()=>this.authService.createSession(userId, sessionData)),\n            getSession: (sessionId)=>this.executeWithLogging(\"AuthService\", \"getSession\", {\n                    sessionId\n                }, ()=>this.authService.getSession(sessionId)),\n            updateSession: (sessionId, updates)=>this.executeWithLogging(\"AuthService\", \"updateSession\", {\n                    sessionId,\n                    updates\n                }, ()=>this.authService.updateSession(sessionId, updates)),\n            deleteSession: (sessionId)=>this.executeWithLogging(\"AuthService\", \"deleteSession\", {\n                    sessionId\n                }, ()=>this.authService.deleteSession(sessionId)),\n            getUserSessions: (userId)=>this.executeWithLogging(\"AuthService\", \"getUserSessions\", {\n                    userId\n                }, ()=>this.authService.getUserSessions(userId)),\n            // 密码管理\n            changePassword: (userId, currentPassword, newPassword)=>this.executeWithLogging(\"AuthService\", \"changePassword\", {\n                    userId\n                }, ()=>this.authService.changePassword(userId, currentPassword, newPassword)),\n            resetPassword: (userId, newPassword)=>this.executeWithLogging(\"AuthService\", \"resetPassword\", {\n                    userId\n                }, ()=>this.authService.resetPassword(userId, newPassword))\n        };\n    }\n    // ==================== 角色权限数据访问 ====================\n    /**\r\n   * 角色权限数据访问器\r\n   */ get roles() {\n        return {\n            // 角色管理\n            getAll: ()=>this.executeWithLogging(\"RoleService\", \"getRoles\", {}, ()=>this.roleService.getRoles()),\n            getById: (id)=>this.executeWithLogging(\"RoleService\", \"getRoleById\", {\n                    id\n                }, ()=>this.roleService.getRoleById(id)),\n            getByCode: (code)=>this.executeWithLogging(\"RoleService\", \"getRoleByCode\", {\n                    code\n                }, ()=>this.roleService.getRoleByCode(code)),\n            create: (roleData)=>this.executeWithLogging(\"RoleService\", \"createRole\", roleData, ()=>this.roleService.createRole(roleData)),\n            update: (id, updates)=>this.executeWithLogging(\"RoleService\", \"updateRole\", {\n                    id,\n                    updates\n                }, ()=>this.roleService.updateRole(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"RoleService\", \"deleteRole\", {\n                    id\n                }, ()=>this.roleService.deleteRole(id)),\n            // 权限管理\n            getPermissions: ()=>this.executeWithLogging(\"RoleService\", \"getPermissions\", {}, ()=>this.roleService.getPermissions()),\n            getPermissionsByModule: (module)=>this.executeWithLogging(\"RoleService\", \"getPermissionsByModule\", {\n                    module\n                }, ()=>this.roleService.getPermissionsByModule(module)),\n            assignPermissions: (roleId, permissionIds)=>this.executeWithLogging(\"RoleService\", \"assignRolePermissions\", {\n                    roleId,\n                    permissionIds\n                }, ()=>this.roleService.assignRolePermissions(roleId, permissionIds))\n        };\n    }\n    /**\r\n   * 订单数据访问器\r\n   */ get orders() {\n        return {\n            getAll: (params)=>{\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] orders.getAll 被调用，管理器ID:\", this.__managerId);\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] 使用的OrderService实例ID:\", this.orderService.__serviceId);\n                return this.executeWithLogging(\"OrderService\", \"getOrders\", params, ()=>this.orderService.getOrders(params));\n            },\n            getById: (id)=>this.executeWithLogging(\"OrderService\", \"getOrderById\", {\n                    id\n                }, ()=>this.orderService.getOrderById(id)),\n            getByNumber: (orderNumber)=>this.executeWithLogging(\"OrderService\", \"getOrderByNumber\", {\n                    orderNumber\n                }, ()=>this.orderService.getOrderByNumber(orderNumber)),\n            create: (data)=>{\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] orders.create 被调用，管理器ID:\", this.__managerId);\n                console.log(\"\\uD83D\\uDD0D [DataAccessManager] 使用的OrderService实例ID:\", this.orderService.__serviceId);\n                return this.executeWithLogging(\"OrderService\", \"createOrder\", data, ()=>this.orderService.createOrder(data));\n            },\n            update: (id, updates)=>this.executeWithLogging(\"OrderService\", \"updateOrder\", {\n                    id,\n                    updates\n                }, ()=>this.orderService.updateOrder(id, updates)),\n            delete: (id)=>this.executeWithLogging(\"OrderService\", \"deleteOrder\", {\n                    id\n                }, ()=>this.orderService.deleteOrder(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"OrderService\", \"getOrdersByStatus\", {\n                    status\n                }, ()=>this.orderService.getOrdersByStatus(status)),\n            getByCustomer: (customerId)=>this.executeWithLogging(\"OrderService\", \"getOrdersByCustomer\", {\n                    customerId\n                }, ()=>this.orderService.getOrdersByCustomer(customerId)),\n            getBySalesRep: (salesRepId)=>this.executeWithLogging(\"OrderService\", \"getOrdersBySalesRep\", {\n                    salesRepId\n                }, ()=>this.orderService.getOrdersBySalesRep(salesRepId)),\n            getByDateRange: (startDate, endDate)=>this.executeWithLogging(\"OrderService\", \"getOrdersByDateRange\", {\n                    startDate,\n                    endDate\n                }, ()=>this.orderService.getOrdersByDateRange(startDate, endDate))\n        };\n    }\n    /**\r\n   * 工作时间配置数据访问器\r\n   */ get workTime() {\n        return {\n            getConfigurations: ()=>this.executeWithLogging(\"WorkTimeService\", \"getConfigurations\", {}, ()=>this.workTimeService.getConfigurations()),\n            getById: (id)=>this.executeWithLogging(\"WorkTimeService\", \"getConfigurationById\", {\n                    id\n                }, ()=>this.workTimeService.getConfigurationById(id)),\n            create: (data)=>this.executeWithLogging(\"WorkTimeService\", \"createConfiguration\", data, ()=>this.workTimeService.createConfiguration(data)),\n            update: (id, data)=>this.executeWithLogging(\"WorkTimeService\", \"updateConfiguration\", {\n                    id,\n                    ...data\n                }, ()=>this.workTimeService.updateConfiguration(id, data)),\n            delete: (id)=>this.executeWithLogging(\"WorkTimeService\", \"deleteConfiguration\", {\n                    id\n                }, ()=>this.workTimeService.deleteConfiguration(id)),\n            addWorkTimeSlot: (configId, slot)=>this.executeWithLogging(\"WorkTimeService\", \"addWorkTimeSlot\", {\n                    configId,\n                    slot\n                }, ()=>this.workTimeService.addWorkTimeSlot(configId, slot)),\n            updateWorkTimeSlot: (configId, slotId, data)=>this.executeWithLogging(\"WorkTimeService\", \"updateWorkTimeSlot\", {\n                    configId,\n                    slotId,\n                    ...data\n                }, ()=>this.workTimeService.updateWorkTimeSlot(configId, slotId, data)),\n            deleteWorkTimeSlot: (configId, slotId)=>this.executeWithLogging(\"WorkTimeService\", \"deleteWorkTimeSlot\", {\n                    configId,\n                    slotId\n                }, ()=>this.workTimeService.deleteWorkTimeSlot(configId, slotId)),\n            calculateWorkingMinutes: (workTimeSlots, breakTimeSlots)=>{\n                const startTime = Date.now();\n                const result = this.workTimeService.calculateWorkingMinutes(workTimeSlots, breakTimeSlots);\n                const duration = Date.now() - startTime;\n                this.logAccess(\"WorkTimeService\", \"calculateWorkingMinutes\", {\n                    workTimeSlots,\n                    breakTimeSlots\n                }, duration, true);\n                return result;\n            },\n            validateTimeSlot: (startTime, endTime)=>{\n                const start = Date.now();\n                const result = this.workTimeService.validateTimeSlot(startTime, endTime);\n                const duration = Date.now() - start;\n                this.logAccess(\"WorkTimeService\", \"validateTimeSlot\", {\n                    startTime,\n                    endTime\n                }, duration, true);\n                return result;\n            },\n            getDefault: ()=>this.executeWithLogging(\"WorkTimeService\", \"getDefaultConfiguration\", {}, ()=>this.workTimeService.getDefaultConfiguration())\n        };\n    }\n    /**\r\n   * 生产订单数据访问器\r\n   */ get productionOrders() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductionOrderService\", \"getAll\", params, ()=>this.productionOrderService.getAll(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductionOrderService\", \"getById\", {\n                    id\n                }, ()=>this.productionOrderService.getById(id)),\n            getByOrderNumber: (orderNumber)=>this.executeWithLogging(\"ProductionOrderService\", \"getByOrderNumber\", {\n                    orderNumber\n                }, ()=>this.productionOrderService.getByOrderNumber(orderNumber)),\n            createFromMRP: (data)=>this.executeWithLogging(\"ProductionOrderService\", \"createFromMRP\", data, ()=>this.productionOrderService.createFromMRP(data)),\n            // 添加通用的create方法（用于测试）\n            create: (data)=>this.executeWithLogging(\"ProductionOrderService\", \"createFromMRP\", data, ()=>{\n                    // 为测试目的添加默认MRP字段\n                    const mrpData = {\n                        ...data,\n                        mrpExecutionId: data.mrpExecutionId || \"mrp_\".concat(Date.now()),\n                        mrpExecutedBy: data.mrpExecutedBy || \"test-user\",\n                        mrpExecutedAt: data.mrpExecutedAt || new Date().toISOString()\n                    };\n                    return this.productionOrderService.createFromMRP(mrpData);\n                }),\n            update: (id, data)=>this.executeWithLogging(\"ProductionOrderService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.productionOrderService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"ProductionOrderService\", \"delete\", {\n                    id\n                }, ()=>this.productionOrderService.delete(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"ProductionOrderService\", \"getByStatus\", {\n                    status\n                }, ()=>this.productionOrderService.getByStatus(status)),\n            getBySalesOrderId: (salesOrderId)=>this.executeWithLogging(\"ProductionOrderService\", \"getBySalesOrderId\", {\n                    salesOrderId\n                }, ()=>this.productionOrderService.getBySalesOrderId(salesOrderId)),\n            // 添加统计方法\n            getStatistics: ()=>this.executeWithLogging(\"ProductionOrderService\", \"getStatistics\", {}, ()=>this.productionOrderService.getStatistics())\n        };\n    }\n    /**\r\n   * 生产工单数据访问器\r\n   */ get productionWorkOrders() {\n        return {\n            getAll: (params)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getAll\", params, ()=>this.productionWorkOrderService.getAll(params)),\n            getById: (id)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getById\", {\n                    id\n                }, ()=>this.productionWorkOrderService.getById(id)),\n            getByBatchNumber: (batchNumber)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByBatchNumber\", {\n                    batchNumber\n                }, ()=>this.productionWorkOrderService.getByBatchNumber(batchNumber)),\n            create: (data)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"create\", data, ()=>this.productionWorkOrderService.create(data)),\n            update: (id, data)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.productionWorkOrderService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"delete\", {\n                    id\n                }, ()=>this.productionWorkOrderService.delete(id)),\n            getByStatus: (status)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByStatus\", {\n                    status\n                }, ()=>this.productionWorkOrderService.getByStatus(status)),\n            getBySourceOrderId: (sourceOrderId)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getBySourceOrderId\", {\n                    sourceOrderId\n                }, ()=>this.productionWorkOrderService.getBySourceOrderId(sourceOrderId)),\n            getByWorkstation: (workstation)=>this.executeWithLogging(\"ProductionWorkOrderService\", \"getByWorkstation\", {\n                    workstation\n                }, ()=>this.productionWorkOrderService.getByWorkstation(workstation))\n        };\n    }\n    /**\r\n   * 工位数据访问器\r\n   * 🔧 修复：为工位数据添加特殊的缓存策略，确保排程算法获取最新状态\r\n   */ get workstations() {\n        return {\n            // 查询方法 - 使用内置缓存系统\n            getAll: (params)=>this.executeWithLogging(\"WorkstationService\", \"getAll\", params, ()=>this.workstationService.getAll()),\n            getWorkstations: (params)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstations\", params, ()=>this.workstationService.getWorkstations(params)),\n            getById: (id)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstationById\", {\n                    id\n                }, ()=>this.workstationService.getWorkstationById(id)),\n            getActiveWorkstations: ()=>this.executeWithLogging(\"WorkstationService\", \"getActiveWorkstations\", {}, ()=>this.workstationService.getActiveWorkstations()),\n            // CRUD操作\n            create: (data)=>this.executeWithLogging(\"WorkstationService\", \"create\", data, ()=>this.workstationService.create(data)),\n            update: (id, data)=>this.executeWithLogging(\"WorkstationService\", \"update\", {\n                    id,\n                    ...data\n                }, ()=>this.workstationService.update(id, data)),\n            delete: (id)=>this.executeWithLogging(\"WorkstationService\", \"delete\", {\n                    id\n                }, ()=>this.workstationService.delete(id)),\n            // 状态管理\n            getStatus: (id)=>this.executeWithLogging(\"WorkstationService\", \"getWorkstationStatus\", {\n                    id\n                }, ()=>this.workstationService.getWorkstationStatus(id)),\n            updateStatus: (id, status)=>this.executeWithLogging(\"WorkstationService\", \"updateWorkstationStatus\", {\n                    id,\n                    ...status\n                }, ()=>this.workstationService.updateWorkstationStatus(id, status)),\n            // 队列管理\n            addToQueue: (workstationId, batchNumber)=>this.executeWithLogging(\"WorkstationService\", \"addToQueue\", {\n                    workstationId,\n                    batchNumber\n                }, ()=>this.workstationService.addToQueue(workstationId, batchNumber)),\n            migrateBatchNumberFormats: ()=>this.executeWithLogging(\"WorkstationService\", \"migrateBatchNumberFormats\", {}, ()=>this.workstationService.migrateBatchNumberFormats()),\n            // 🔧 统一的工位重置方法\n            resetAllWorkstationsToIdle: ()=>this.executeWithLogging(\"WorkstationService\", \"resetAllWorkstationsToIdle\", {}, ()=>this.workstationService.resetAllWorkstationsToIdle())\n        };\n    }\n    // ==================== 管理功能 ====================\n    /**\r\n   * 获取访问日志\r\n   */ getAccessLogs(limit) {\n        return limit ? this.accessLogs.slice(-limit) : [\n            ...this.accessLogs\n        ];\n    }\n    /**\r\n   * 清除访问日志\r\n   */ clearAccessLogs() {\n        this.accessLogs = [];\n    }\n    /**\r\n   * 获取统计信息\r\n   */ getStatistics() {\n        const totalRequests = this.accessLogs.length;\n        const successfulRequests = this.accessLogs.filter((log)=>log.success).length;\n        const failedRequests = totalRequests - successfulRequests;\n        const averageDuration = totalRequests > 0 ? this.accessLogs.reduce((sum, log)=>sum + log.duration, 0) / totalRequests : 0;\n        return {\n            totalRequests,\n            successfulRequests,\n            failedRequests,\n            successRate: totalRequests > 0 ? successfulRequests / totalRequests * 100 : 0,\n            averageDuration: Math.round(averageDuration),\n            config: this.config\n        };\n    }\n    /**\r\n   * 初始化数据变更通知机制\r\n   */ initializeDataChangeNotification() {\n    // 这里可以添加额外的初始化逻辑\n    // 数据变更通知器已经在导入时自动初始化\n    }\n    /**\r\n   * 获取数据变更通知器\r\n   */ getDataChangeNotifier() {\n        return _DataChangeNotifier__WEBPACK_IMPORTED_MODULE_12__.dataChangeNotifier;\n    }\n    // 优先级同步服务已删除\n    /**\r\n   * 成本计算数据访问器\r\n   */ get costCalculations() {\n        return {\n            getAllCalculations: (params)=>this.executeWithLogging(\"CostCalculationService\", \"getAllCalculations\", params, ()=>this.costCalculationService.getAllCalculations(params)),\n            getCalculationById: (id)=>this.executeWithLogging(\"CostCalculationService\", \"getCalculationById\", {\n                    id\n                }, ()=>this.costCalculationService.getCalculationById(id)),\n            createCalculation: (data)=>this.executeWithLogging(\"CostCalculationService\", \"createCalculation\", data, ()=>this.costCalculationService.createCalculation(data)),\n            updateCalculation: (id, data)=>this.executeWithLogging(\"CostCalculationService\", \"updateCalculation\", {\n                    id,\n                    ...data\n                }, ()=>this.costCalculationService.updateCalculation(id, data)),\n            deleteCalculation: (id)=>this.executeWithLogging(\"CostCalculationService\", \"deleteCalculation\", {\n                    id\n                }, ()=>this.costCalculationService.deleteCalculation(id)),\n            getCalculationsByProduct: (productModelCode)=>this.executeWithLogging(\"CostCalculationService\", \"getCalculationsByProduct\", {\n                    productModelCode\n                }, ()=>this.costCalculationService.getCalculationsByProduct(productModelCode)),\n            getPendingReconciliations: ()=>this.executeWithLogging(\"CostCalculationService\", \"getPendingReconciliations\", {}, ()=>this.costCalculationService.getPendingReconciliations()),\n            getStatistics: ()=>this.executeWithLogging(\"CostCalculationService\", \"getStatistics\", {}, ()=>this.costCalculationService.getStatistics()),\n            getCostSummary: ()=>this.executeWithLogging(\"CostCalculationService\", \"getCostSummary\", {}, ()=>this.costCalculationService.getCostSummary())\n        };\n    }\n    // 旧缓存管理方法已移除，使用内置缓存系统\n    // 移除重复的方法定义，使用下面的统一缓存管理方法\n    // invalidateCache(tags: string[]): void {\n    //   this.cacheManager.deleteByTags(tags)\n    // }\n    /**\r\n   * 更新配置\r\n   */ updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        // 如果禁用缓存，清空现有缓存\n        if (newConfig.enableCaching === false) {\n            this.cache.clear();\n            this.requestCache.clear();\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存已禁用并清空\");\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 配置已更新:\", this.config);\n        }\n    }\n    // ==================== 缓存管理接口 ====================\n    /**\r\n   * 清除特定服务的缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearServiceCache(serviceName, operation) {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return 0;\n        }\n        // ✅ 架构合规：使用内置缓存系统清理服务缓存\n        let deletedCount = 0;\n        const pattern = \"\".concat(serviceName, \":\");\n        for (const [key] of Array.from(this.cache)){\n            if (key.startsWith(pattern)) {\n                this.cache.delete(key);\n                deletedCount++;\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除服务缓存: \".concat(serviceName).concat(operation ? \" (\".concat(operation, \")\") : \"\", \"，删除了 \").concat(deletedCount, \" 个缓存项\"));\n        }\n        return deletedCount;\n    }\n    /**\r\n   * 清除特定数据类型的缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearDataTypeCache(dataType, affectedIds) {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return 0;\n        }\n        let deletedCount = 0;\n        const serviceMap = {\n            \"products\": \"ProductService\",\n            \"orders\": \"OrderService\",\n            \"workstations\": \"WorkstationService\",\n            \"statistics\": \"StatisticsService\"\n        };\n        const serviceName = serviceMap[dataType] || dataType;\n        for (const [key] of Array.from(this.cache)){\n            if (key.startsWith(\"\".concat(serviceName, \":\"))) {\n                // 如果指定了特定ID，只删除相关的缓存\n                if (affectedIds && affectedIds.length > 0) {\n                    const hasMatchingId = affectedIds.some((id)=>key.includes(id));\n                    if (hasMatchingId) {\n                        this.cache.delete(key);\n                        deletedCount++;\n                    }\n                } else {\n                    // 删除所有该数据类型的缓存\n                    this.cache.delete(key);\n                    deletedCount++;\n                }\n            }\n        }\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除数据类型缓存: \".concat(dataType, \"，影响ID: \").concat((affectedIds === null || affectedIds === void 0 ? void 0 : affectedIds.join(\", \")) || \"全部\", \"，删除了 \").concat(deletedCount, \" 个缓存项\"));\n        }\n        return deletedCount;\n    }\n    /**\r\n   * 清除所有缓存\r\n   * 符合数据调用规范的缓存管理接口\r\n   */ clearAllCache() {\n        if (!this.config.enableCaching) {\n            if (this.config.enableLogging) {\n                console.log(\"[DataAccessManager] 缓存未启用，无需清理\");\n            }\n            return;\n        }\n        // ✅ 架构合规：使用内置缓存系统清理所有缓存\n        const beforeSize = this.cache.size;\n        this.cache.clear();\n        this.requestCache.clear();\n        if (this.config.enableLogging) {\n            console.log(\"[DataAccessManager] 清除所有缓存，删除了 \".concat(beforeSize, \" 个缓存项\"));\n        }\n    }\n    /**\r\n   * 获取缓存统计信息 - 优化版\r\n   * 符合数据调用规范的监控接口\r\n   */ getCacheStatistics() {\n        // ✅ 架构合规：使用内置缓存系统获取统计信息\n        const entries = Array.from(this.cache.values());\n        const totalSize = entries.reduce((sum, entry)=>sum + entry.estimatedSize, 0);\n        const averageEntrySize = entries.length > 0 ? totalSize / entries.length : 0;\n        // 计算优先级分布\n        const priorityDistribution = {\n            critical: 0,\n            high: 0,\n            medium: 0,\n            low: 0\n        };\n        entries.forEach((entry)=>{\n            priorityDistribution[entry.priority]++;\n        });\n        return {\n            enabled: this.config.enableCaching,\n            size: this.cache.size,\n            hits: this.cacheStats.hits,\n            misses: this.cacheStats.misses,\n            hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0,\n            totalRequests: this.cacheStats.totalRequests,\n            // ✅ 优化：新增统计信息\n            evictions: this.cacheStats.evictions,\n            prewarmHits: this.cacheStats.prewarmHits,\n            memoryPressure: this.memoryPressure,\n            averageEntrySize,\n            priorityDistribution\n        };\n    }\n    /**\r\n   * 更新缓存配置\r\n   */ updateCacheConfig(newConfig) {\n        this.cacheConfig = {\n            ...this.cacheConfig,\n            ...newConfig\n        };\n        console.log(\"[DataAccessManager] 缓存配置已更新\");\n    }\n    // ✅ 架构合规：移除违规的CacheStrategyManager方法，使用内置缓存系统替代\n    /**\r\n   * 批量操作方法\r\n   */ get batch() {\n        return {\n            // 批量创建生产订单\n            createProductionOrders: (orders)=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_13__.performanceOptimizer.batchCreateProductionOrders(orders),\n            // 批量更新生产订单\n            updateProductionOrders: (updates)=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_13__.performanceOptimizer.batchUpdateProductionOrders(updates),\n            // 批量创建生产工单 - 使用并发控制优化\n            createProductionWorkOrders: async (workOrders)=>{\n                const startTime = performance.now();\n                try {\n                    // 创建任务数组\n                    const tasks = workOrders.map((workOrder)=>async ()=>{\n                            const result = await this.productionWorkOrders.create(workOrder);\n                            if (result.status !== \"success\") {\n                                throw new Error(result.message || \"创建工单失败\");\n                            }\n                            return result.data;\n                        });\n                    const taskNames = workOrders.map((_, index)=>\"批量创建工单-\".concat(index + 1));\n                    const batchResult = await _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_15__.batchOperationController.executeBatch(tasks, taskNames);\n                    const duration = performance.now() - startTime;\n                    const { successful, failed } = batchResult;\n                    const successfulData = successful.map((result)=>result.data).filter(Boolean);\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.recordRequest(\"batch.createProductionWorkOrders\", duration, failed.length === 0);\n                    return {\n                        status: \"success\",\n                        data: successfulData,\n                        message: \"成功批量创建\".concat(successfulData.length, \"个生产工单\").concat(failed.length > 0 ? \"，\".concat(failed.length, \"个失败\") : \"\"),\n                        batchResult: {\n                            successful: successful.length,\n                            failed: failed.length,\n                            successRate: batchResult.successRate,\n                            totalDuration: duration\n                        }\n                    };\n                } catch (error) {\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.recordRequest(\"batch.createProductionWorkOrders\", duration, false);\n                    throw error;\n                }\n            },\n            // 批量更新工位状态\n            updateWorkstationStatuses: async (updates)=>{\n                const startTime = performance.now();\n                try {\n                    const results = await Promise.all(updates.map((param)=>{\n                        let { id, status } = param;\n                        return this.workstations.updateStatus(id, status);\n                    }));\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.recordRequest(\"batch.updateWorkstationStatuses\", duration, true);\n                    // 使用内置缓存系统清理相关缓存\n                    this.clearDataTypeCache(\"workstations\", updates.map((u)=>u.id));\n                    return {\n                        status: \"success\",\n                        data: results.filter((r)=>r.status === \"success\").map((r)=>r.data),\n                        message: \"成功批量更新\".concat(results.length, \"个工位状态\")\n                    };\n                } catch (error) {\n                    const duration = performance.now() - startTime;\n                    _DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.recordRequest(\"batch.updateWorkstationStatuses\", duration, false);\n                    throw error;\n                }\n            }\n        };\n    }\n    /**\r\n   * 性能监控方法\r\n   */ get performance() {\n        return {\n            // 获取性能指标\n            getMetrics: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.getMetrics(),\n            // 获取性能警告\n            getAlerts: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.getAlerts(),\n            // 获取优化建议\n            getSuggestions: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.getOptimizationSuggestions(),\n            // 生成性能报告\n            generateReport: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.generatePerformanceReport(),\n            // 重置性能指标\n            reset: ()=>_DataAccessPerformanceMonitor__WEBPACK_IMPORTED_MODULE_14__.dataAccessPerformanceMonitor.reset(),\n            // 优化缓存策略\n            optimizeCache: ()=>{\n                // 使用内置缓存系统进行优化\n                this.evictOldestEntries();\n                console.log(\"[DataAccessManager] 缓存优化完成\");\n            },\n            // 获取缓存性能指标\n            getCacheMetrics: ()=>{\n                return this.getCacheStatistics();\n            },\n            // 获取性能优化器指标\n            getOptimizerMetrics: ()=>_PerformanceOptimizer__WEBPACK_IMPORTED_MODULE_13__.performanceOptimizer.getPerformanceMetrics()\n        };\n    }\n    // 旧的缓存管理方法已移除，使用内置缓存系统的统一接口\n    /**\r\n   * 性能监控管理方法\r\n   */ getPerformanceMetrics() {\n        // 更新方法统计到性能指标中\n        this.performanceMetrics.methodStats = this.methodStats;\n        return {\n            ...this.performanceMetrics\n        };\n    }\n    getRealTimeMetrics() {\n        const batchControllerStatus = _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_15__.batchOperationController.getStatus();\n        // 分析系统健康状况\n        const systemHealth = this.analyzeSystemHealth();\n        return {\n            currentConcurrency: batchControllerStatus.running,\n            queueLength: batchControllerStatus.queued,\n            recentCalls: [\n                ...this.recentCalls.slice(-50)\n            ],\n            systemHealth\n        };\n    }\n    analyzeSystemHealth() {\n        const issues = [];\n        const recommendations = [];\n        // 检查错误率\n        const recentErrorRate = this.recentCalls.length > 0 ? this.recentCalls.filter((call)=>call.status === \"error\").length / this.recentCalls.length : 0;\n        if (recentErrorRate > 0.1) {\n            issues.push(\"错误率过高: \".concat((recentErrorRate * 100).toFixed(2), \"%\"));\n            recommendations.push(\"检查网络连接和服务状态\");\n        }\n        // 检查响应时间\n        if (this.performanceMetrics.averageResponseTime > 2000) {\n            issues.push(\"平均响应时间过长: \".concat(this.performanceMetrics.averageResponseTime.toFixed(0), \"ms\"));\n            recommendations.push(\"考虑优化查询或增加缓存\");\n        }\n        // 检查缓存命中率\n        if (this.performanceMetrics.cacheHitRate < 0.3) {\n            issues.push(\"缓存命中率较低: \".concat((this.performanceMetrics.cacheHitRate * 100).toFixed(2), \"%\"));\n            recommendations.push(\"检查缓存策略和TTL设置\");\n        }\n        // 检查并发队列\n        const batchStatus = _utils_concurrencyControl__WEBPACK_IMPORTED_MODULE_15__.batchOperationController.getStatus();\n        if (batchStatus.queued > 10) {\n            issues.push(\"批量操作队列积压: \".concat(batchStatus.queued, \"个任务\"));\n            recommendations.push(\"考虑增加并发数或优化任务处理\");\n        }\n        // 确定系统状态\n        let status = \"healthy\";\n        if (issues.length > 0) {\n            status = recentErrorRate > 0.2 || this.performanceMetrics.averageResponseTime > 5000 ? \"critical\" : \"warning\";\n        }\n        return {\n            status,\n            issues,\n            recommendations\n        };\n    }\n    resetPerformanceMetrics() {\n        this.initializePerformanceMonitoring();\n        console.log(\"[DataAccessManager] 性能指标已重置\");\n    }\n    getSlowQueries() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;\n        return this.performanceMetrics.slowQueries.sort((a, b)=>b.duration - a.duration).slice(0, limit);\n    }\n    getMethodStatistics() {\n        return Array.from(this.methodStats.entries()).map((param)=>{\n            let [method, stats] = param;\n            return {\n                method,\n                calls: stats.calls,\n                averageTime: stats.averageTime,\n                successRate: stats.successRate,\n                lastCall: new Date(stats.lastCall).toLocaleString()\n            };\n        }).sort((a, b)=>b.calls - a.calls) // 按调用次数排序\n        ;\n    }\n    exportPerformanceReport() {\n        return {\n            timestamp: new Date().toISOString(),\n            summary: this.getPerformanceMetrics(),\n            realTime: this.getRealTimeMetrics(),\n            topMethods: this.getMethodStatistics().slice(0, 10),\n            slowQueries: this.getSlowQueries(10)\n        };\n    }\n    // ==================== 批量操作 ====================\n    /**\r\n   * 批量操作访问器\r\n   * 提供批量创建、更新、删除等操作\r\n   */ get batch() {\n        return {\n            // 批量创建生产订单\n            createProductionOrders: async (orders)=>{\n                const results = [];\n                const errors = [];\n                for (const order of orders){\n                    try {\n                        const result = await this.productionOrders.create(order);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(result.message || \"创建失败\");\n                        }\n                    } catch (error) {\n                        errors.push(error instanceof Error ? error.message : \"未知错误\");\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功创建\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"创建过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量更新生产订单\n            updateProductionOrders: async (updates)=>{\n                const results = [];\n                const errors = [];\n                for (const { id, data } of updates){\n                    try {\n                        const result = await this.productionOrders.update(id, data);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"更新失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功更新\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"更新过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量删除生产订单\n            deleteProductionOrders: async (ids)=>{\n                const successIds = [];\n                const errors = [];\n                for (const id of ids){\n                    try {\n                        const result = await this.productionOrders.delete(id);\n                        if (result.status === \"success\") {\n                            successIds.push(id);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"删除失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: true,\n                        message: \"成功删除\".concat(successIds.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: false,\n                        message: \"删除过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            },\n            // 批量查询生产订单\n            getProductionOrders: async (ids)=>{\n                const results = [];\n                const errors = [];\n                for (const id of ids){\n                    try {\n                        const result = await this.productionOrders.getById(id);\n                        if (result.status === \"success\") {\n                            results.push(result.data);\n                        } else {\n                            errors.push(\"ID \".concat(id, \": \").concat(result.message || \"查询失败\"));\n                        }\n                    } catch (error) {\n                        errors.push(\"ID \".concat(id, \": \").concat(error instanceof Error ? error.message : \"未知错误\"));\n                    }\n                }\n                if (errors.length === 0) {\n                    return {\n                        status: \"success\",\n                        data: results,\n                        message: \"成功查询\".concat(results.length, \"个生产订单\")\n                    };\n                } else {\n                    return {\n                        status: \"error\",\n                        data: results,\n                        message: \"查询过程中发生错误: \".concat(errors.join(\", \")),\n                        errors\n                    };\n                }\n            }\n        };\n    }\n    /**\r\n   * 获取系统配置\r\n   */ getConfig() {\n        return {\n            ...this.systemConfig\n        };\n    }\n    /**\r\n   * 更新系统配置\r\n   */ updateConfig(config) {\n        this.systemConfig = {\n            ...this.systemConfig,\n            ...config\n        };\n        console.log(\"[DataAccessManager] 系统配置已更新:\", config);\n        // 根据配置更新对应的系统行为\n        if (config.enableCaching !== undefined) {\n            console.log(\"[DataAccessManager] 缓存状态: \".concat(config.enableCaching ? \"已启用\" : \"已禁用\"));\n        }\n        if (config.cacheTimeout !== undefined) {\n            console.log(\"[DataAccessManager] 缓存超时时间: \".concat(config.cacheTimeout, \"ms\"));\n        }\n        if (config.enableLogging !== undefined) {\n            console.log(\"[DataAccessManager] 日志状态: \".concat(config.enableLogging ? \"已启用\" : \"已禁用\"));\n        }\n        if (config.logLevel !== undefined) {\n            console.log(\"[DataAccessManager] 日志级别: \".concat(config.logLevel));\n        }\n    }\n    /**\r\n   * 重置配置到默认值\r\n   */ resetConfig() {\n        this.systemConfig = {\n            enableCaching: true,\n            cacheTimeout: 5 * 60 * 1000,\n            enableLogging: true,\n            logLevel: \"INFO\",\n            enablePerformanceMonitoring: true,\n            maxConcurrentRequests: 10,\n            retryAttempts: 3,\n            retryDelay: 1000\n        };\n        console.log(\"[DataAccessManager] 系统配置已重置\");\n    }\n    constructor(config = {}){\n        this.accessLogs = [];\n        // ✅ 优化：增强缓存管理属性\n        this.cache = new Map();\n        this.cacheConfig = DEFAULT_CACHE_CONFIG;\n        this.requestCache = new Map();\n        this.cacheStats = {\n            hits: 0,\n            misses: 0,\n            totalRequests: 0,\n            evictions: 0,\n            prewarmHits: 0\n        };\n        // ✅ 优化：智能缓存管理\n        this.cacheAccessHistory = new Map() // 访问时间历史\n        ;\n        this.prewarmTimer = null;\n        this.lastMemoryCheck = 0;\n        this.memoryPressure = false;\n        // 性能监控相关属性\n        this.performanceMetrics = {\n            totalCalls: 0,\n            successCalls: 0,\n            errorCalls: 0,\n            averageResponseTime: 0,\n            minResponseTime: 0,\n            maxResponseTime: 0,\n            cacheHitRate: 0,\n            slowQueries: [],\n            methodStats: new Map(),\n            hourlyStats: []\n        };\n        this.recentCalls = [];\n        this.methodStats = new Map();\n        // 🔧 优化：批量性能报告\n        this.performanceBatch = [];\n        this.batchReportTimer = null;\n        this.BATCH_REPORT_INTERVAL = 5000 // 5秒批量报告一次\n        ;\n        // ==================== 配置管理 ====================\n        /**\r\n   * 系统配置管理\r\n   * 提供动态配置更新和查询功能\r\n   */ this.systemConfig = {\n            enableCaching: true,\n            cacheTimeout: 5 * 60 * 1000,\n            enableLogging: true,\n            logLevel: \"INFO\",\n            enablePerformanceMonitoring: true,\n            maxConcurrentRequests: 10,\n            retryAttempts: 3,\n            retryDelay: 1000\n        };\n        this.config = {\n            ...DEFAULT_CONFIG,\n            ...config\n        };\n        this.__managerId = Math.random().toString(36).substr(2, 9);\n        console.log(\"✅ [DataAccessManager] 创建实例，ID:\", this.__managerId, \"内置缓存系统已启用\");\n        // 初始化内置缓存系统\n        if (this.config.enableCaching) {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 内置缓存系统已启用\");\n            // ✅ 优化：启动智能缓存管理\n            this.initializeAdvancedCaching();\n        } else {\n            console.log(\"\\uD83D\\uDD27 [DataAccessManager] 缓存已禁用\");\n        }\n        // 初始化性能监控\n        this.initializePerformanceMonitoring();\n        // 初始化服务实例\n        this.productService = _ProductDataAccessService__WEBPACK_IMPORTED_MODULE_0__.ProductDataAccessService.getInstance();\n        this.customerService = _CustomerDataAccessService__WEBPACK_IMPORTED_MODULE_1__.CustomerDataAccessService.getInstance();\n        this.employeeService = _EmployeeDataAccessService__WEBPACK_IMPORTED_MODULE_2__.EmployeeDataAccessService.getInstance();\n        this.inventoryService = _InventoryDataAccessService__WEBPACK_IMPORTED_MODULE_3__.InventoryDataAccessService.getInstance();\n        this.orderService = _OrderDataAccessService__WEBPACK_IMPORTED_MODULE_4__.OrderDataAccessService.getInstance();\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 初始化OrderService，管理器ID:\", this.__managerId);\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] OrderService实例ID:\", this.orderService.__serviceId);\n        this.workTimeService = _WorkTimeDataAccessService__WEBPACK_IMPORTED_MODULE_5__.WorkTimeDataAccessService.getInstance();\n        this.productionOrderService = _ProductionOrderDataAccessService__WEBPACK_IMPORTED_MODULE_6__.ProductionOrderDataAccessService.getInstance();\n        this.productionWorkOrderService = _ProductionWorkOrderDataAccessService__WEBPACK_IMPORTED_MODULE_7__.ProductionWorkOrderDataAccessService.getInstance();\n        this.workstationService = _WorkstationDataAccessService__WEBPACK_IMPORTED_MODULE_8__.WorkstationDataAccessService.getInstance();\n        this.costCalculationService = _CostCalculationDataAccessService__WEBPACK_IMPORTED_MODULE_9__.CostCalculationDataAccessService.getInstance();\n        this.authService = _AuthDataAccessService__WEBPACK_IMPORTED_MODULE_10__.AuthDataAccessService.getInstance();\n        this.roleService = new _RoleDataAccessService__WEBPACK_IMPORTED_MODULE_11__.RoleDataAccessService();\n        console.log(\"\\uD83D\\uDD27 [DataAccessManager] 认证服务和角色服务已初始化\");\n        // 初始化数据变更通知机制\n        this.initializeDataChangeNotification();\n        // 优先级同步服务已删除\n        if (true) {\n            console.log({\n                version: _DataAccessLayer__WEBPACK_IMPORTED_MODULE_16__.API_VERSION,\n                config: this.config,\n                dataChangeNotifier: \"已启用\"\n            });\n        }\n    }\n}\n// 创建单例实例\nconst dataAccessManager = DataAccessManager.getInstance();\n// 🔧 将DataAccessManager暴露到全局作用域（用于调试和测试）\nif (true) {\n    window.dataAccessManager = dataAccessManager;\n    console.log(\"\\uD83D\\uDD27 [DataAccessManager] 已暴露到全局作用域 window.dataAccessManager\");\n}\nconst { products, customers, employees, inventory, productionOrders, productionWorkOrders, workstations, costCalculations, auth } = dataAccessManager;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\n"));

/***/ })

});