/**
 * 简单认证服务
 * 
 * 根据PRD文档要求实现的极简版认证服务
 * 为小企业提供简单易用的认证功能，专注于admin/employee两级权限
 * 
 * 注意：这是现有复杂认证系统的简化接口，底层仍使用DataAccessManager
 */

import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { SECURITY_CONFIG, validatePassword } from '@/config/security'
import { User, LoginRequest, LoginResult } from '@/types/auth'

/**
 * 简化的用户类型（PRD要求）
 */
export interface SimpleUser {
  id: string
  username: string
  role: 'admin' | 'employee'
}

/**
 * 简化的登录结果（PRD要求）
 */
export interface SimpleLoginResult {
  user: SimpleUser
  token: string
}

/**
 * 简单认证服务类
 * 
 * 提供PRD文档中要求的简化认证接口
 * 底层使用现有的完整认证系统，但对外提供简单的API
 */
export class SimpleAuthService {
  
  /**
   * 用户登录
   * 
   * @param username 用户名
   * @param password 密码
   * @returns 登录结果，包含用户信息和token
   */
  static async login(username: string, password: string): Promise<SimpleLoginResult> {
    try {
      console.log('🔐 [SimpleAuthService] 开始处理登录请求:', username)

      // 调用底层认证服务
      const loginRequest: LoginRequest = {
        username,
        password,
        rememberMe: false
      }

      const result = await dataAccessManager.auth.login(loginRequest)

      if (result.status !== 'success' || !result.data) {
        throw new Error(result.message || '用户名或密码错误')
      }

      const { user, sessionId } = result.data

      // 检查用户状态
      if (user.status !== 'active') {
        throw new Error('用户不存在或已被禁用')
      }

      // 生成JWT token
      const accessToken = await dataAccessManager.tokenManagement.generateAccessToken({
        userId: user.id,
        username: user.username,
        roles: user.roles.map(role => role.code),
        permissions: user.roles.flatMap(role => role.permissions.map(perm => perm.code)),
        sessionId: sessionId!
      })

      // 简化用户信息，只返回PRD要求的字段
      const simpleUser: SimpleUser = {
        id: user.id,
        username: user.username,
        role: this.mapUserRole(user.roles)
      }

      console.log('✅ [SimpleAuthService] 登录成功:', simpleUser.username)

      return {
        user: simpleUser,
        token: accessToken
      }

    } catch (error) {
      console.error('❌ [SimpleAuthService] 登录失败:', error)
      throw error
    }
  }

  /**
   * 验证token
   * 
   * @param token JWT token
   * @returns 用户信息，如果token无效则返回null
   */
  static async verifyToken(token: string): Promise<SimpleUser | null> {
    try {
      console.log('🔍 [SimpleAuthService] 开始验证token')

      // 使用底层token管理服务验证
      const tokenValidation = await dataAccessManager.tokenManagement.verifyAccessToken(token)

      if (!tokenValidation.isValid || !tokenValidation.payload) {
        console.log('❌ [SimpleAuthService] Token验证失败')
        return null
      }

      // 获取用户信息
      const userResult = await dataAccessManager.auth.getUserById(tokenValidation.payload.userId)

      if (userResult.status !== 'success' || !userResult.data) {
        console.log('❌ [SimpleAuthService] 用户不存在')
        return null
      }

      const user = userResult.data

      // 检查用户状态
      if (user.status !== 'active') {
        console.log('❌ [SimpleAuthService] 用户已被禁用')
        return null
      }

      // 返回简化的用户信息
      const simpleUser: SimpleUser = {
        id: user.id,
        username: user.username,
        role: this.mapUserRole(user.roles)
      }

      console.log('✅ [SimpleAuthService] Token验证成功:', simpleUser.username)
      return simpleUser

    } catch (error) {
      console.error('❌ [SimpleAuthService] Token验证异常:', error)
      return null
    }
  }

  /**
   * 权限检查
   * 
   * @param userRole 用户角色
   * @param requiredRole 需要的角色
   * @returns 是否有权限
   */
  static hasPermission(userRole: string, requiredRole: 'admin' | 'employee'): boolean {
    console.log('🔍 [SimpleAuthService] 检查权限:', { userRole, requiredRole })

    // PRD要求的简单权限逻辑
    if (requiredRole === 'admin') {
      return userRole === 'admin'
    }
    
    // employee权限：admin和employee都可以访问
    return ['admin', 'employee'].includes(userRole)
  }

  /**
   * 将复杂的角色映射为简单的admin/employee角色
   * 
   * @param roles 用户角色列表
   * @returns 简化的角色
   */
  private static mapUserRole(roles: User['roles']): 'admin' | 'employee' {
    // 如果有admin角色，则为admin
    if (roles.some(role => role.code === 'admin' || role.code === 'system_admin')) {
      return 'admin'
    }

    // 否则为employee
    return 'employee'
  }

  /**
   * 验证密码强度（根据PRD的简化要求）
   * 
   * @param password 密码
   * @returns 验证结果
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean
    errors: string[]
  } {
    return validatePassword(password)
  }

  /**
   * 获取安全配置信息
   * 
   * @returns 安全配置
   */
  static getSecurityConfig() {
    return {
      passwordPolicy: SECURITY_CONFIG.password,
      sessionDuration: SECURITY_CONFIG.session.duration,
      maxLoginAttempts: SECURITY_CONFIG.session.maxLoginAttempts,
      lockoutDuration: SECURITY_CONFIG.session.lockoutDuration
    }
  }

  /**
   * 检查用户是否为管理员
   * 
   * @param user 用户信息
   * @returns 是否为管理员
   */
  static isAdmin(user: SimpleUser): boolean {
    return user.role === 'admin'
  }

  /**
   * 检查用户是否为员工
   * 
   * @param user 用户信息
   * @returns 是否为员工
   */
  static isEmployee(user: SimpleUser): boolean {
    return user.role === 'employee'
  }
}

/**
 * 导出简化的认证服务实例
 * 提供与PRD文档完全一致的API接口
 */
export default SimpleAuthService
